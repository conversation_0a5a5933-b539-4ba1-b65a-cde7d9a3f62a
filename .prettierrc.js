module.exports = {
  // Specifies the maximum line length before wrapping
  printWidth: 90,
  // Enforces the use of single quotes in strings
  singleQuote: true,
  // Adds trailing commas wherever possible
  trailingComma: 'all',
  // Controls spacing between brackets in object literals
  bracketSpacing: false,
  // Places the closing bracket of JSX elements on a new line
  bracketSameLine: false,
  // Requires parentheses around arrow function parameters
  arrowParens: 'always',
  // Uses spaces instead of tabs for indentation
  useTabs: false,
  // Sets the number of spaces per indentation level
  tabWidth: 2,
  // Adds semicolons at the end of statements
  semi: true,
};
