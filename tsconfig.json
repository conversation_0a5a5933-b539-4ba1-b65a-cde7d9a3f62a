{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@assets/*": ["src/assets/*"],
      "@components/*": ["src/components/*"],
      "@utils/*": ["src/utils/*"],
      "@styles/*": ["src/styles/*"]
    },
    "allowJs": false,
    "alwaysStrict": true,
    "forceConsistentCasingInFileNames": true,
    "lib": ["esnext"],
    "noImplicitThis": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictPropertyInitialization": true,
    "noUncheckedIndexedAccess": true,

    // TODO: add types & remove these rules
    "noImplicitAny": false,
    "useUnknownInCatchVariables": false,
    "module": "esnext"
  }
}
