# Development

Make sure you have [setup your environment for React Native](https://reactnative.dev/docs/environment-setup).

## Installation

**1. Clone the repository**

```shell
<NAME_EMAIL>:AssetifyNet/react-native-app.git && cd react-native-app
```

**2. Switch Node version**

Switch to the Node.js version defined in `package.json`.

**3. Install dependencies**

```shell
yarn
```

Run the following command for iOS Development:

```shell
cd ios && pod install && cd ..
```

**4. Start the project**

On iOS Simulator/Device:

```shell
yarn ios
```

On Android Emulator/Device:

```shell
yarn android
```

## Troubleshooting

When running into issues there are a couple things to check.

- Clean caches & build folders: `yarn clean`
- Clean simulator cache (iOS): `xcrun simctl erase all`
- Increase emulated device storage (Android): `Android Studio -> Device Manager -> Edit Device -> Show Advanced Settings -> increase RAM, VM heap and Internal Storage sizes`
