import {LoanSettingsParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import React, {memo, ReactElement} from 'react';
import {useTranslation} from 'react-i18next';
import {useTheme} from 'styled-components/native';

import GlobalStyles from '@/constants/GlobalStyles';
import {DEFAULT_HEADER_STYLE, getThemedHeaderStyle} from '../utils';
import {ITheme} from '@/styles/themes';

import LoanChangeEmail from '@/screens/Loan/screens/manage-loan/LoanChangeEmail';
import LoanSettings from '@/screens/Loan/screens/manage-loan/LoanSettingsHome';
import LoanChangePassword from '@/screens/Loan/screens/settings/LoanChangePassword';
import TwoFactorAuthScreen from '@/screens/Loan/screens/settings/two-factor-auth/LoanTwoFactorAuth';

const Stack = createStackNavigator<LoanSettingsParamList>();

const LoanSettingsNavigator = (): ReactElement => {
  const {t} = useTranslation();
  const currentTheme = useTheme() as ITheme;

  const screenOptions: StackNavigationOptions = {
    headerShown: true,
    ...getThemedHeaderStyle(currentTheme),
  };

  return (
    <Stack.Navigator screenOptions={screenOptions} initialRouteName="LoanSettingsHome">
      <Stack.Screen
        name="LoanSettingsHome"
        component={LoanSettings}
        options={{headerTitle: 'Settings'}}
      />
      <Stack.Screen
        name="LoanTwoFactorAuth"
        component={TwoFactorAuthScreen}
        options={{headerTitle: 'Two-Factor Authentication'}}
      />
      <Stack.Screen
        name="LoanChangeEmail"
        component={LoanChangeEmail}
        options={{headerTitle: 'Change Email'}}
      />
      <Stack.Screen
        name="LoanChangePassword"
        component={LoanChangePassword}
        options={{headerTitle: 'Change Password'}}
      />
    </Stack.Navigator>
  );
};

export default memo(LoanSettingsNavigator);
