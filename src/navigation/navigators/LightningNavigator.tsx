import {LightningStackParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import {ReactElement} from 'react';

import AssetifyLogo from '@/assets/logo/Logo.svg';
import TopNavigationHeaderIconTitle from '@/components/TopNavigationHeaderIconTitle';
import GlobalStyles from '@/constants/GlobalStyles';
import {useScreenSize} from '@/hooks/screen';
import LightningHome from '@/screens/LightningNetwork/LightningHome';
import ReceiveLightningScreen from '@/screens/LightningNetwork/LightningReceive';
import LightningRefunds from '@/screens/LightningNetwork/LightningRefunds';
import LightningSend from '@/screens/LightningNetwork/LightningSend';
import LightningSwapOutConfirmation from '@/screens/LightningNetwork/LightningSwapOutConfirmation';
import SwapScreen from '@/screens/LightningNetwork/LightningSwaps';
import {DEFAULT_HEADER_STYLE} from '../utils';

const Stack = createStackNavigator<LightningStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: true,
  ...DEFAULT_HEADER_STYLE,
  headerBackTitle: undefined,
  headerBackTitleVisible: false,
};

const LightningNavigator = (): ReactElement => {
  const {isSmallScreen} = useScreenSize();

  return (
    <Stack.Navigator screenOptions={screenOptions} initialRouteName="LightningHome">
      <Stack.Screen
        name="LightningHome"
        component={LightningHome}
        options={{
          headerTitle: () => (
            <TopNavigationHeaderIconTitle title="Lightning" icon={AssetifyLogo} />
          ),
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
            height: isSmallScreen ? 80 : 110,
          },
          headerBackTitle: undefined,
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="LightningSend"
        component={LightningSend}
        options={{
          title: 'Send Lightning',
          headerBackTitle: undefined,
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="LightningReceive"
        component={ReceiveLightningScreen}
        options={{
          title: 'Receive Lightning',
          headerBackTitle: undefined,
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="LightningSwaps"
        component={SwapScreen}
        options={{
          title: 'Lightning Swaps',
          headerBackTitle: undefined,
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="LightningRefunds"
        component={LightningRefunds}
        options={{
          title: 'Lightning Refunds',
          headerBackTitle: undefined,
          headerBackTitleVisible: false,
        }}
      />
      <Stack.Screen
        name="LightningSwapOutConfirmation"
        component={LightningSwapOutConfirmation}
        options={{
          title: 'Lightning Confirm',
          headerBackTitle: undefined,
          headerBackTitleVisible: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default LightningNavigator;
