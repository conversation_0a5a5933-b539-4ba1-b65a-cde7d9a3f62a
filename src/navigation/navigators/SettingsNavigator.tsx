import {SettingsStackParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import {ReactElement} from 'react';

import TopNavigationHeaderExit from '@/components/TopNavigationHeaderExit';
import GlobalStyles from '@/constants/GlobalStyles';
import ChainsScreen from '@/screens/SettingsScreen/screens/ChainsScreen/ChainsScreen';
import PrivateKeyScreen from '@/screens/SettingsScreen/screens/PrivateKeyScreen/PrivateKeyScreen';
import SeedPhraseScreen from '@/screens/SettingsScreen/screens/SeedPhraseScreen/SeedPhraseScreen';
import TermsOfServiceScreen from '@/screens/SettingsScreen/screens/TermsOfServiceScreen/TermsOfServiceScreen';
import VerifySeedPhraseScreen from '@/screens/SettingsScreen/screens/VerifySeedPhraseScreen';
import SettingsScreen from '@/screens/SettingsScreen/SettingsScreen';
import {DEFAULT_HEADER_STYLE} from '../utils';

const Stack = createStackNavigator<SettingsStackParamList>();
const screenOptions: StackNavigationOptions = {
  ...DEFAULT_HEADER_STYLE,
  headerShown: true,
};

const SettingsNavigator = (): ReactElement => (
  <Stack.Navigator screenOptions={screenOptions} initialRouteName="SettingsHome">
    <Stack.Screen
      name="SettingsHome"
      component={SettingsScreen}
      options={{
        title: 'Settings',
        headerStyle: {
          backgroundColor: GlobalStyles.base.white,
          borderBottomWidth: 0,
          shadowColor: 'transparent',
          elevation: 0,
        },
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
      }}
    />

    <Stack.Screen
      name="PrivateKeys"
      component={ChainsScreen}
      options={{
        title: 'Private Keys',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
      }}
    />

    <Stack.Screen
      name="SeedPhrase"
      component={SeedPhraseScreen}
      options={({navigation}) => ({
        title: 'Seed Phrase',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
        headerRight: () => (
          <TopNavigationHeaderExit
            onPress={() => navigation.replace('BottomTabs', {screen: 'Home'})}
          />
        ),
        headerStyle: {
          ...DEFAULT_HEADER_STYLE.headerStyle,
          backgroundColor: GlobalStyles.base.white,
        },
      })}
    />

    <Stack.Screen
      name="VerifySeedPhrase"
      component={VerifySeedPhraseScreen}
      options={({navigation}) => ({
        title: 'Verify Seed Phrase',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
        headerRight: () => (
          <TopNavigationHeaderExit
            onPress={() => navigation.replace('BottomTabs', {screen: 'Home'})}
          />
        ),
      })}
    />
    <Stack.Screen
      name="PrivateKey"
      component={PrivateKeyScreen}
      options={({navigation}) => ({
        title: 'Private Key',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
        headerRight: () => (
          <TopNavigationHeaderExit
            onPress={() => navigation.replace('BottomTabs', {screen: 'Home'})}
          />
        ),
      })}
    />
    <Stack.Screen
      name="TermsOfService"
      component={TermsOfServiceScreen}
      options={({navigation}) => ({
        title: 'Terms of Service',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
        headerRight: () => (
          <TopNavigationHeaderExit
            onPress={() => navigation.replace('BottomTabs', {screen: 'Home'})}
          />
        ),
      })}
    />
  </Stack.Navigator>
);

export default SettingsNavigator;
