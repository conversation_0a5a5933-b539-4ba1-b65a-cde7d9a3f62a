import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import React, {memo} from 'react';

import GlobalStyles from '@/constants/GlobalStyles';
import {UserLoansTopTabsParamList} from '@/navigation/types';

import LoanActiveList from '@/screens/Loan/screens/manage-loan/LoanActiveList';
import LoanInactiveList from '@/screens/Loan/screens/manage-loan/LoanInactiveList';
import LoanPendingList from '@/screens/Loan/screens/manage-loan/LoanPendingList';

const UserLoansTopTabs = createMaterialTopTabNavigator<UserLoansTopTabsParamList>();

const UserLoansTopTabsNavigator: React.FC = () => {
  return (
    <UserLoansTopTabs.Navigator
      initialRouteName="Active"
      style={{
        backgroundColor: GlobalStyles.base.white,
      }}
      screenOptions={{
        tabBarIndicatorContainerStyle: {
          backgroundColor: GlobalStyles.base.white,
        },
        tabBarLabelStyle: {
          fontSize: 14,
          fontWeight: '700',
        },
        tabBarIndicatorStyle: {
          backgroundColor: GlobalStyles.gray.gray900,
        },
      }}
    >
      <UserLoansTopTabs.Screen name="Pending" component={LoanPendingList} />
      <UserLoansTopTabs.Screen name="Active" component={LoanActiveList} />
      <UserLoansTopTabs.Screen name="Inactive" component={LoanInactiveList} />
    </UserLoansTopTabs.Navigator>
  );
};

export default memo(UserLoansTopTabsNavigator);
