import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import {ReactElement} from 'react';

import {KeychainStackParamList} from '@/navigation/types';
import KeychainConfirmPassword from '@/screens/KeychainBackup/KeychainConfirmPassword';
import KeychainCreatePassword from '@/screens/KeychainBackup/KeychainCreatePassword';
import KeychainHome from '@/screens/KeychainBackup/KeychainHome';
import {DEFAULT_HEADER_STYLE} from '../utils';
import theme from '@/styles/themes';
import KeychainWalletLabel from '@/screens/KeychainBackup/KeychainWalletLabel';

const Stack = createStackNavigator<KeychainStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: true,
  ...DEFAULT_HEADER_STYLE,
  headerStyle: {
    ...DEFAULT_HEADER_STYLE.headerStyle,
    backgroundColor: theme.colors.base.white,
  },
};

const KeychainNavigator = (): ReactElement => {
  return (
    <Stack.Navigator screenOptions={screenOptions} initialRouteName="KeychainHome">
      <Stack.Screen name="KeychainHome" component={KeychainHome} />

      <Stack.Screen name="KeychainWalletLabel" component={KeychainWalletLabel} />

      <Stack.Screen name="KeychainPassword" component={KeychainCreatePassword} />

      <Stack.Screen name="KeychainConfirmPassword" component={KeychainConfirmPassword} />
    </Stack.Navigator>
  );
};

export default KeychainNavigator;
