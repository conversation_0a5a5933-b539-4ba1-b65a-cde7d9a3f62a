import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import {memo, ReactElement} from 'react';

import GlobalStyles from '@/constants/GlobalStyles';
import ImportNewWallet from '@/screens/Onboarding/create-new-wallet/ImportNewWallet';
import NewWalletCreating from '@/screens/Onboarding/create-new-wallet/NewWalletCreating';
import NewWalletScreen from '@/screens/Onboarding/create-new-wallet/NewWalletScreen';
import VerifyImport from '@/screens/Onboarding/create-new-wallet/VerifyImport';
import ImportWalletOptions from '@/screens/Onboarding/import-wallet/ImportWalletOptions';
import ImportWalletScreen from '@/screens/Onboarding/import-wallet/ImportWalletScreen';
import RestoreRecoveryAndImport from '@/screens/Onboarding/import-wallet/restore-wallet/RestoreRecoveryAndImport';
import RestoreWalletCheck from '@/screens/Onboarding/import-wallet/restore-wallet/RestoreWalletCheck';
import RestoreWalletListAvailable from '@/screens/Onboarding/import-wallet/restore-wallet/RestoreWalletListAvailable';
import Onboarding from '@/screens/Onboarding/Onboarding';
import {OnboardingStackParamList} from '../types';
import {DEFAULT_HEADER_STYLE} from '../utils';
import KeychainNavigator from './KeychainNavigator';

const Stack = createStackNavigator<OnboardingStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: false,
};

const OnboardingNavigator = (): ReactElement => {
  return (
    <Stack.Navigator screenOptions={screenOptions} initialRouteName="OnboardingHome">
      <Stack.Screen name="OnboardingHome" component={Onboarding} />

      <Stack.Screen
        name="NewWalletCreating"
        component={NewWalletCreating}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />

      <Stack.Screen
        name="NewWallet"
        component={NewWalletScreen}
        options={({route}) => ({
          headerShown: route.params?.loading ? false : true,
          gestureEnabled: route.params?.loading ? false : true,
          ...(!route.params?.loading && {
            ...DEFAULT_HEADER_STYLE,
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          }),
        })}
      />

      <Stack.Screen name="Keychain" component={KeychainNavigator} />

      <Stack.Screen
        name="VerifyImport"
        component={VerifyImport}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />

      <Stack.Screen
        name="ImportNewWallet"
        component={ImportNewWallet}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerTitle: 'New Wallet',
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />

      <Stack.Screen
        name="ImportWalletOptions"
        component={ImportWalletOptions}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />

      <Stack.Screen
        name="ImportWallet"
        component={ImportWalletScreen}
        options={({route}) => ({
          headerShown: route.params?.loading ? false : true,
          gestureEnabled: route.params?.loading ? false : true,
          ...(!route.params?.loading && {
            ...DEFAULT_HEADER_STYLE,
            headerStyle: {
              ...DEFAULT_HEADER_STYLE.headerStyle,
              backgroundColor: GlobalStyles.base.white,
            },
          }),
        })}
      />

      <Stack.Screen
        name="RestoreWalletCheck"
        component={RestoreWalletCheck}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />

      <Stack.Screen
        name="RestoreWalletListAvailable"
        component={RestoreWalletListAvailable}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />

      <Stack.Screen
        name="RestoreRecoveryAndImport"
        component={RestoreRecoveryAndImport}
        options={{
          headerShown: true,
          ...DEFAULT_HEADER_STYLE,
          headerStyle: {
            ...DEFAULT_HEADER_STYLE.headerStyle,
            backgroundColor: GlobalStyles.base.white,
          },
        }}
      />
    </Stack.Navigator>
  );
};

export default memo(OnboardingNavigator);
