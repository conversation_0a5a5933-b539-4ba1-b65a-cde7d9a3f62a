import {ExchangeStackParamList} from '@/navigation/types';
import {createStackNavigator, StackNavigationOptions} from '@react-navigation/stack';
import {ReactElement} from 'react';

import TopNavigationHeaderExit from '@/components/TopNavigationHeaderExit';
import ExchangeConfirm from '@/screens/Exchange/ExchangeConfirm';
import ExchangeDetails from '@/screens/Exchange/ExchangeDetails';
import {DEFAULT_HEADER_STYLE} from '../utils';

const Stack = createStackNavigator<ExchangeStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: true,
  ...DEFAULT_HEADER_STYLE,
  headerBackTitle: undefined,
  headerBackTitleVisible: false,
};

const ExchangeNavigator = (): ReactElement => (
  <Stack.Navigator screenOptions={screenOptions} initialRouteName="ExchangeHome">
    <Stack.Screen
      name="ExchangeHome"
      component={ExchangeDetails}
      options={{
        title: 'Swap',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
      }}
    />
    <Stack.Screen
      name="ExchangeConfirm"
      component={ExchangeConfirm}
      options={({navigation}) => ({
        title: 'Swap Confirm',
        headerBackTitle: undefined,
        headerBackTitleVisible: false,
        headerRight: () => (
          <TopNavigationHeaderExit
            onPress={() => navigation.replace('BottomTabs', {screen: 'Home'})}
          />
        ),
      })}
    />
  </Stack.Navigator>
);

export default ExchangeNavigator;
