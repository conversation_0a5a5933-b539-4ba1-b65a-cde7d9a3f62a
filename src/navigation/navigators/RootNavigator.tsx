import {useFocusEffect} from '@react-navigation/native';
import {
  CardStyleInterpolators,
  createStackNavigator,
  StackNavigationOptions,
} from '@react-navigation/stack';
import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import {AppState, AppStateStatus, BackHandler, Platform} from 'react-native';

import {useAppDispatch, useAuth} from '@/hooks/redux';
import {DEFAULT_HEADER_STYLE, isScreenForbidden} from '@/navigation/utils/index';
import Authenticate from '@/screens/Authenticate';
import VerifySeedPhraseScreen from '@/screens/SettingsScreen/screens/VerifySeedPhraseScreen';
import InfoScreen from '@/screens/WalletScreen/screens/InfoScreen/InfoScreen';
import NotificationsScreen from '@/screens/WalletScreen/screens/NotificationsScreen/NotificationsScreen';
import {setIsAuthenticated, setShowAuthScreen} from '@/storage/actions/authActions';
import BottomSheets from '../bottom-sheet/BottomSheets';
import {RootStackParamList} from '../types';
import {goBack} from '../utils/navigation';
import BottomTabsNavigator from './BottomTabsNavigator';
import KeychainNavigator from './KeychainNavigator';
import SettingsNavigator from './SettingsNavigator';

const Stack = createStackNavigator<RootStackParamList>();
const screenOptions: StackNavigationOptions = {
  headerShown: false,
};

const RootNavigator = () => {
  const dispatch = useAppDispatch();
  const appState = useRef<AppStateStatus>(AppState.currentState);

  const {isLoggedIn, isAuthenticated, showAuthScreen, biometricsInProgress} = useAuth();

  const handleAppStateChange = useCallback(
    async (nextAppState: AppStateStatus) => {
      if (nextAppState.match(/inactive|background/)) {
        dispatch(setIsAuthenticated(false));
        dispatch(setShowAuthScreen(true));
      }
      appState.current = nextAppState;
    },
    [dispatch],
  );

  const handleAppStateChangeAndroid = useCallback(() => {
    // Prevent multiple biometrics at the same time
    if (biometricsInProgress) return;

    dispatch(setIsAuthenticated(false));
    dispatch(setShowAuthScreen(true));
  }, [biometricsInProgress, dispatch]);

  useFocusEffect(
    useCallback(() => {
      dispatch(setIsAuthenticated(false));
    }, []),
  );

  useEffect(() => {
    let inactiveAndroidSubscription: any;

    const appStateSubscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    const backHandlerSubscription = BackHandler.addEventListener(
      'hardwareBackPress',
      goBack,
    );

    if (Platform.OS === 'android') {
      inactiveAndroidSubscription = AppState.addEventListener(
        'blur',
        handleAppStateChangeAndroid,
      );
    }

    return () => {
      appStateSubscription.remove();
      backHandlerSubscription.remove();

      if (Platform.OS === 'android') {
        inactiveAndroidSubscription.remove();
      }
    };
  }, [isLoggedIn, isAuthenticated, biometricsInProgress]);

  return (
    <>
      {isLoggedIn &&
      !isAuthenticated &&
      !isScreenForbidden(dispatch) &&
      showAuthScreen ? (
        <Authenticate />
      ) : null}

      <Stack.Navigator screenOptions={screenOptions}>
        <Stack.Screen name="BottomTabs" component={BottomTabsNavigator} />

        <Stack.Screen name="Settings" component={SettingsNavigator} />

        <Stack.Screen
          name="Info"
          component={InfoScreen}
          options={{
            headerShown: true,
            title: 'About Assetify',
            ...DEFAULT_HEADER_STYLE,
            gestureDirection: 'horizontal-inverted',
            cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
          }}
        />

        <Stack.Screen
          name="Notifications"
          component={NotificationsScreen}
          options={{
            headerShown: true,
            ...DEFAULT_HEADER_STYLE,
            headerTitle: 'Notifications',
          }}
        />

        <Stack.Screen name="Keychain" component={KeychainNavigator} />

        <Stack.Screen name="VerifySeedPhrase" component={VerifySeedPhraseScreen} />
      </Stack.Navigator>

      <BottomSheets />
    </>
  );
};

export default memo(RootNavigator);
