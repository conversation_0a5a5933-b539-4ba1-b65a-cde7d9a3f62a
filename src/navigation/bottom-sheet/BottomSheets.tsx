import React, {memo} from 'react';

import CountrySheet from '@/screens/Loan/components/kyc/sheets/CountrySheet';
import IndustrySheet from '@/screens/Loan/components/kyc/sheets/IndustrySheet';
import ResidentialCitizenshipSheet from '@/screens/Loan/components/kyc/sheets/ResidentialCitizenshipSheet';
import ResidentialCountrySheet from '@/screens/Loan/components/kyc/sheets/ResidentialCountrySheet';
import SectorSheet from '@/screens/Loan/components/kyc/sheets/SectorSheet';
import KeychainBackupSheet from './KeychainBackupSheet';
import LightningHistorySheet from './LightningHistorySheet';
import LightningSendSheet from './LightningSendSheet';
import ListSheet from './ListSheet';
import WalletSheet from './WalletSheet';

const BottomSheets = (): JSX.Element => {
  return (
    <>
      <ListSheet />
      <WalletSheet />

      <LightningHistorySheet />
      <LightningSendSheet />
      <KeychainBackupSheet />

      <IndustrySheet />
      <SectorSheet />
      <CountrySheet />

      <ResidentialCountrySheet />
      <ResidentialCitizenshipSheet />
    </>
  );
};

export default memo(BottomSheets);
