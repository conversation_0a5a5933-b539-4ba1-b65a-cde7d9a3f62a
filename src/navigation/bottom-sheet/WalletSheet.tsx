import React, {memo, useCallback, useMemo} from 'react';
import {FlatList, StyleSheet, View} from 'react-native';

import type {AuthAsset} from '@/types/authTypes';
import BottomSheetWrapper from '@/components/BottomSheetWrapper';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet, useSnapPoints} from '@/hooks/bottomSheet';
import {useAppSelector} from '@/hooks/redux';
import {selectSheet} from '@/storage/slices/ui';
import {useCombinedCurrencyChecker} from '@/screens/CurrencySpecificScreen/hooks/useCombined';
import RampAsset from '@/screens/WalletScreen/components/AssetList/components/WalletAsset/RampAsset';

export type WalletSheetData = {
  assets: Array<AuthAsset>;
  calculatedPrices: string[][];
  tokenPrices: string[][];
  stableCoins: string[][];
  handleAssetPress: (asset: AuthAsset) => void;
  handleStableCoinPress: (asset: AuthAsset, stableCoin: string) => void;
  currency: string;
};

const convertNetworkTicket = (network: string): string => {
  switch (network.toLowerCase()) {
    case 'trx':
      return 'tron';
    case 'xrp':
      return 'ripple';
    case 'bitcoin-cash':
      return 'bitcoin_cash';
    case 'dogecoin':
      return 'doge';
    case 'binance-smart-chain':
      return 'binance_smart_chain';
    default:
      return network.toLowerCase();
  }
};

const WalletSheet = () => {
  const snapPoints = useSnapPoints(95);
  const {close} = useBottomSheet('wallet');
  const {isOpen, data} = useAppSelector((state) => selectSheet(state, 'wallet'));
  const {buy} = useCombinedCurrencyChecker('', '');
  const sheetData = data as WalletSheetData | null;

  const keyExtractor = useCallback(
    (item: AuthAsset, index: number) => `${item.blockchain}-${item.tokenSymbol}-${index}`,
    [],
  );

  const renderItem = useCallback(
    ({item, index}: {item: AuthAsset; index: number}) => {
      if (!item || item.tokenSymbol === 'N/A') return null;

      const convertedNetwork = convertNetworkTicket(item.blockchain);
      const isSupported = buy?.isSymbolSupported?.(convertedNetwork, '');

      if (!isSupported) return null;

      return (
        <RampAsset
          key={index}
          index={index}
          asset={item}
          calculatedPrices={sheetData?.calculatedPrices[index] || []}
          tokenPrices={sheetData?.tokenPrices[index] || []}
          stableCoins={sheetData?.stableCoins[index] || []}
          handleAssetPress={sheetData?.handleAssetPress || (() => {})}
          handleStableCoinPress={sheetData?.handleStableCoinPress || (() => {})}
          currency={sheetData?.currency || ''}
        />
      );
    },
    [
      sheetData?.calculatedPrices,
      sheetData?.tokenPrices,
      sheetData?.stableCoins,
      sheetData?.handleAssetPress,
      sheetData?.handleStableCoinPress,
      sheetData?.currency,
      buy,
    ],
  );

  const filteredAssets = useMemo(() => {
    if (!sheetData?.assets || !buy?.isSymbolSupported) return [];

    return sheetData.assets.filter((item) => {
      if (!item || item.tokenSymbol === 'N/A') return false;
      const convertedNetwork = convertNetworkTicket(item.blockchain);
      return buy.isSymbolSupported(convertedNetwork, '');
    });
  }, [sheetData?.assets, buy]);

  if (!isOpen || !sheetData) return null;

  return (
    <BottomSheetWrapper isOpen={isOpen} onClose={close} snapPoints={snapPoints}>
      <View style={styles.container}>
        <FlatList
          data={filteredAssets}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          style={styles.list}
          contentContainerStyle={styles.listContent}
        />
      </View>
    </BottomSheetWrapper>
  );
};

export default memo(WalletSheet);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    paddingTop: 8,
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 24,
    paddingHorizontal: 12,
  },
});
