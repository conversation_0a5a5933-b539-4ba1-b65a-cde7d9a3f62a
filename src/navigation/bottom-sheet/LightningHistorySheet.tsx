import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import {FlatList, StyleSheet, Text, View} from 'react-native';
import BottomSheet from '@gorhom/bottom-sheet';

import BottomSheetWrapper from '@/components/BottomSheetWrapper';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet, useSnapPoints} from '@/hooks/bottomSheet';
import {useAppSelector} from '@/hooks/redux';
import {selectSheet} from '@/storage/slices/ui';
import {msatToSat} from '@/screens/LightningNetwork/helpers';
import {formatNumber} from '@/utils';
import {listPayments, Payment} from '@breeztech/react-native-breez-sdk';

const LightningHistorySheet = () => {
  const {close} = useBottomSheet('lightningHistory');
  const {isOpen} = useAppSelector((state) => selectSheet(state, 'lightningHistory'));
  const snapPoints = useSnapPoints(90);
  const bottomSheetRef = useRef<BottomSheet>(null);

  const [payments, setPayments] = useState<Payment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchPayments = useCallback(async () => {
    try {
      const paymentsList = await listPayments({});
      setPayments(paymentsList as Payment[]);
    } catch (err) {
      console.error('[Lightning] Error fetching payments:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      fetchPayments();
    }
  }, [fetchPayments, isOpen]);

  const renderItem = useCallback(({item}: {item: Payment}) => {
    const isPositive = item.paymentType === 'received';
    // Convert Unix timestamp (seconds) to milliseconds and create Date object
    const date = new Date(item.paymentTime * 1000);
    // Format date as: "MMM DD, YYYY, HH:MM AM/PM"
    const formattedDate = date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    return (
      <View style={styles.paymentItem}>
        <View style={styles.paymentInfo}>
          <Text style={styles.description} numberOfLines={1}>
            {item.description || 'Lightning Transaction'}
          </Text>
          <Text style={styles.date}>{formattedDate}</Text>
        </View>
        <View style={styles.amountContainer}>
          <Text
            style={[
              styles.amount,
              {
                color: isPositive
                  ? GlobalStyles.success.success500
                  : GlobalStyles.error.error500,
              },
            ]}
          >
            {isPositive ? '+' : '-'}{' '}
            {formatNumber(msatToSat(item.amountMsat), {decimals: 0})} sat
          </Text>
          {item.feeMsat > 0 && (
            <Text style={styles.fee}>
              Fee: {formatNumber(msatToSat(item.feeMsat), {decimals: 0})} sat
            </Text>
          )}
        </View>
      </View>
    );
  }, []);

  if (!isOpen) return null;

  return (
    <BottomSheetWrapper
      isOpen={isOpen}
      snapPoints={snapPoints}
      onClose={close}
      ref={bottomSheetRef}
    >
      <View style={styles.container}>
        <Text style={styles.title}>Lightning Transactions</Text>

        {isLoading ? (
          <View style={styles.centered}>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        ) : payments.length === 0 ? (
          <View style={styles.centered}>
            <Text style={styles.emptyText}>No transactions to show.</Text>
          </View>
        ) : (
          <FlatList
            data={payments}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
            maxToRenderPerBatch={13}
            nestedScrollEnabled={true}
            onScrollBeginDrag={() => bottomSheetRef?.current?.snapToIndex(0)}
            scrollEventThrottle={16}
            style={styles.list}
          />
        )}
      </View>
    </BottomSheetWrapper>
  );
};

export default memo(LightningHistorySheet);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: GlobalStyles.primary.primary900,
    fontFamily: GlobalStyles.fonts.poppins,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
    fontFamily: GlobalStyles.fonts.poppins,
  },
  emptyText: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 17,
    color: GlobalStyles.gray.gray800,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  paymentInfo: {
    flex: 1,
    marginRight: 16,
  },
  description: {
    fontSize: 16,
    color: GlobalStyles.primary.primary900,
    fontFamily: GlobalStyles.fonts.poppins,
    fontWeight: '500',
    marginBottom: 4,
  },
  date: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  amount: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.poppins,
    fontWeight: '600',
  },
  fee: {
    fontSize: 12,
    color: GlobalStyles.gray.gray900,
    fontFamily: GlobalStyles.fonts.poppins,
    marginTop: 2,
  },
  list: {
    flex: 1,
    width: '100%',
  },
});
