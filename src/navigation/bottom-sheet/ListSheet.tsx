import React, {memo} from 'react';

import BottomSheetList from '@/components/BottomSheetList';
import {useBottomSheet, useSnapPoints} from '@/hooks/bottomSheet';
import {useAppSelector} from '@/hooks/redux';
import {selectSheet} from '@/storage/slices/ui';

const ListSheet = () => {
  const {isOpen, data, close} = useBottomSheet('list');
  const sheet = useAppSelector((state) => selectSheet(state, 'list'));

  const snapPointsToUse = Array.isArray(sheet.snapPoints)
    ? useSnapPoints(sheet.snapPoints[0]!, sheet.snapPoints[1]!)
    : useSnapPoints(sheet.snapPoints ?? 40);

  if (!isOpen || !data) return null;

  return (
    <BottomSheetList
      isOpen={isOpen}
      snapPoints={snapPointsToUse}
      data={data.data}
      onSelect={data.onSelect}
      onClose={close}
      enableSearch={sheet.enableSearch}
    />
  );
};

export default memo(ListSheet);
