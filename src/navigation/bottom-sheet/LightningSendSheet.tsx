import {InputTypeVariant, parseInput} from '@breeztech/react-native-breez-sdk';
import React, {memo, useCallback, useRef, useState} from 'react';
import {Animated, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import BottomSheetWrapper from '@/components/BottomSheetWrapper';
import Camera from '@/components/Camera';
import SafeAreaInset from '@/components/SafeAreaInset';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet, useSnapPoints} from '@/hooks/bottomSheet';
import {showWarningToast} from '@/utils/toast';
import {navigateViaBottomTabs} from '../utils/navigation';
const lightningPayReq = require('bolt11');

const LightningSendSheet = () => {
  const {isOpen, close} = useBottomSheet('lightningSend');
  const snapPoints = useSnapPoints(90);

  const [address, setAddress] = useState('');
  const [isCameraVisible, setIsCameraVisible] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const toggleCamera = useCallback(() => {
    setIsCameraVisible((prev) => {
      Animated.timing(fadeAnim, {
        toValue: !prev ? 1 : 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
      return !prev;
    });
  }, [fadeAnim]);

  const handleAddressChange = useCallback((text: string) => {
    setAddress(text);
  }, []);

  const processLightningAddress = async (data: string) => {
    try {
      const cleanData = data.trim().toUpperCase();

      if (cleanData.startsWith('LNURL')) {
        try {
          const input = await parseInput(cleanData);
          if (input?.type === InputTypeVariant.LN_URL_PAY) {
            navigateViaBottomTabs('Lightning', 'LightningSend', {
              input,
              lnurl: cleanData,
            });
          }
        } catch (err) {
          showWarningToast('Your LNURL is not valid. Check if the LNURL is not yours');
          console.error('[Lightning] Error parsing LNURL:', err);
        }
      } else if (cleanData.startsWith('LNBC')) {
        try {
          lightningPayReq.decode(cleanData);
          navigateViaBottomTabs('Lightning', 'LightningSend', {
            bolt11: cleanData,
          });
        } catch (err) {
          showWarningToast('Your LNBC is not valid. Please try again');
          console.error('[Lightning] Error parsing BOLT11 invoice:', err);
        }
      } else {
        showWarningToast('Your payment request is not valid. Please try again');
      }
    } catch (err) {
      showWarningToast('Something went wrong. Please try again');
      console.error('[Lightning] Error processing address:', err);
    }
  };

  const handleAddressBlur = useCallback(async () => {
    if (address) {
      await processLightningAddress(address);
      close();
    }
  }, [address, close, processLightningAddress]);

  const onQrCodeRead = useCallback(
    async (e: any) => {
      close();
      await processLightningAddress(e);
    },
    [close, processLightningAddress],
  );

  if (!isOpen) return null;

  return (
    <BottomSheetWrapper isOpen={isOpen} snapPoints={snapPoints} onClose={close}>
      <>
        <View style={styles.container}>
          <Text style={styles.title}>Send Lightning Payment</Text>

          {!isCameraVisible ? (
            <>
              <View style={styles.inputContainer}>
                <TextInput
                  label="Send to address"
                  placeholder="LNURL or LNBC format"
                  placeholderTextColor={GlobalStyles.gray.gray700}
                  value={address}
                  onChangeText={handleAddressChange}
                  onBlur={handleAddressBlur}
                />
              </View>

              <Text style={styles.otherMethodsTitle}>Other Methods</Text>

              <View style={styles.buttonContainer}>
                <TouchableOpacity onPress={toggleCamera} style={styles.cameraButton}>
                  <Text style={styles.cameraButtonText}>Scan QR Code</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => {}}
                  style={[styles.cameraButton, styles.disabledButton]}
                  disabled={true}
                >
                  <Text style={[styles.cameraButtonText, styles.disabledText]}>
                    NFC (Soon)
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <>
              <View style={styles.buttonContainer}>
                <TouchableOpacity onPress={toggleCamera} style={styles.cameraButton}>
                  <Text style={styles.cameraButtonText}>Close Scanner</Text>
                </TouchableOpacity>
              </View>

              <Animated.View
                style={[
                  styles.cameraContainer,
                  {
                    opacity: fadeAnim,
                    transform: [
                      {
                        scale: fadeAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.95, 1],
                        }),
                      },
                    ],
                  },
                ]}
              >
                <Camera onBarcodeRead={onQrCodeRead} />
              </Animated.View>
            </>
          )}
        </View>

        <SafeAreaInset type="bottom" />
      </>
    </BottomSheetWrapper>
  );
};

export default memo(LightningSendSheet);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  title: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 20,
    color: GlobalStyles.primary.primary900,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  otherMethodsTitle: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 16,
    color: GlobalStyles.primary.primary900,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  inputContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 16,
    marginBottom: 6,
    gap: 8,
  },
  cameraButton: {
    width: '45%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: GlobalStyles.primary.primary400,
    paddingVertical: 12,
    borderRadius: 8,
  },
  disabledButton: {
    backgroundColor: GlobalStyles.gray.gray600,
  },
  disabledText: {
    color: GlobalStyles.gray.gray900,
  },
  cameraButtonText: {
    color: GlobalStyles.base.white,
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.poppins,
  },
  cameraContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 8,
  },
  closeButtonContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
});
