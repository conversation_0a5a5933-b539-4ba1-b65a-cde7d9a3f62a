import {StatusInstance} from '@/services/BackendServices';
import analytics from '@react-native-firebase/analytics';
import {Platform} from 'react-native';
import {getBuildNumber} from 'react-native-device-info';

export const logAnalyticsEventForTab = (tab: string) => {
  const tabEventMap = {
    // Onboarding
    'Add Wallet': 'BottomNav_addWalletButton_tap',
    // Main
    Wallet: 'BottomNav_walletButton_tap',
    Lightning: 'BottomNav_breezButton_tap',
    InitiateExchange: 'BottomNav_swapsButton_tap',

    // 'More': 'BottomNav_calculatorButton_tap',
    // 'Calculator': 'BottomNav_calculatorButton_tap',
    // 'News': 'BottomNav_newsButton_tap',
  };

  const eventName = tabEventMap[tab];
  if (eventName) {
    analytics().logEvent(eventName);
  }
};

const getApiStatus = async (currentBuildNumber: string): Promise<boolean> => {
  try {
    const apiRes = await StatusInstance.get<{
      build: {
        number: string;
        enabled: boolean;
      };
      additionalDisabledBuilds: number[];
    }>('build/status');

    const {
      build: {number: apiBuildNumber, enabled: apiStatus},
      additionalDisabledBuilds,
    } = apiRes.data;

    const currentBuildNum = Number(currentBuildNumber);
    const apiBuildNum = Number(apiBuildNumber);

    if (currentBuildNum === apiBuildNum) {
      return apiStatus;
    }

    return !additionalDisabledBuilds.includes(currentBuildNum);
  } catch (error) {
    console.error('Error getting features flag response:', error);
    return false;
  }
};

export const checkStatus = async (): Promise<boolean> => {
  const platformConstants = Platform.constants as {systemName?: string};

  // Early returns for specific platforms
  if (platformConstants.systemName === 'iPadOS') {
    return false;
  }

  if (Platform.OS === 'android') {
    return true;
  }

  // For iOS, check build status
  const buildNumber = getBuildNumber();
  return await getApiStatus(buildNumber);
};
