import {Platform} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {setIsAuthenticated} from '@/storage/actions/authActions';
import {AppDispatch} from '@/storage/store';
import {navigationRef} from './navigation';
import {ITheme} from '@/styles/themes';

const ANDROID_PREVENT_INACTIVE_SCREENS = [
  'SettingsHome',
  'Keychain',
  'SendAssetConfirmation',
  'ExchangeConfirm',
];

export const DEFAULT_HEADER_STYLE = {
  headerStyle: {
    backgroundColor: GlobalStyles.gray.gray400,
    borderBottomWidth: 0,
    shadowColor: 'transparent',
    elevation: 0,
  },
  // Header Back Title
  headerBackTitleVisible: false,
  headerTintColor: GlobalStyles.gray.gray800,
  // Header Title
  headerTitle: '',
  headerTitleAlign: 'center' as const,
  headerTitleStyle: {
    color: GlobalStyles.base.black,
    fontSize: 19,
  },
  // Header Left Container
  headerLeftContainerStyle: {
    paddingLeft: 12,
  },
  // Header Right Container
  headerRightContainerStyle: {
    paddingRight: 12,
  },
};

export const getThemedHeaderStyle = (theme: ITheme) => ({
  ...DEFAULT_HEADER_STYLE,
  headerStyle: {
    ...DEFAULT_HEADER_STYLE.headerStyle,
    backgroundColor: theme.colors.background,
  },
  headerTitleStyle: {
    ...DEFAULT_HEADER_STYLE.headerTitleStyle,
    color: theme.colors.text,
  },
  headerTintColor: theme.colors.text,
});

/**
 * Prevents the biometrics from being shown on system ui elements.
 *
 * MUST be removed once a custom dialog is implemented.
 */
export const isScreenForbidden = (dispatch: AppDispatch) => {
  // if (Platform.OS !== 'android') return false;

  if (!navigationRef.current) return false;

  const currentRouteName = navigationRef.current.getCurrentRoute()?.name;
  const preventScreen = ANDROID_PREVENT_INACTIVE_SCREENS.includes(
    currentRouteName as string,
  );

  if (preventScreen) {
    dispatch(setIsAuthenticated(true));
  }

  return preventScreen;
};
