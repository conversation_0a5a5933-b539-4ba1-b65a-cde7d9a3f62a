import {NavigationContainer} from '@react-navigation/native';
import * as Sen<PERSON> from '@sentry/react-native';
import React from 'react';

import NoInternet from '@/components/NoInternet';
import {useDebounce} from '@/hooks';
import RootNavigator from '../navigators/RootNavigator';
import {linking, navigationRef} from '../utils/navigation';

type NavigationProviderProps = {
  isConnected: boolean;
};

export const NavigationProvider = ({isConnected}: NavigationProviderProps) => {
  const debouncedLoading = useDebounce(isConnected, 1_000);

  const handleStateChange = React.useCallback((state: any) => {
    try {
      if (__DEV__) {
        // console.log('New navigation state:', state);
      }
    } catch (error) {
      Sentry.captureException(error);
      console.error('Navigation state change error:', error);
    }
  }, []);

  const linkingConfig = {
    ...linking,
    onError: (error: Error) => {
      Sentry.captureException(error);
      console.error('Deep linking error:', error);
    },
  };

  return (
    <NavigationContainer
      ref={navigationRef}
      linking={linkingConfig}
      onStateChange={handleStateChange}
    >
      {debouncedLoading ? <RootNavigator /> : <NoInternet />}
    </NavigationContainer>
  );
};
