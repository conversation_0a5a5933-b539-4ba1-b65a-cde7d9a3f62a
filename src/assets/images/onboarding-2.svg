<?xml version="1.0" encoding="UTF-8"?>
<svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Definitions -->
  <defs>
    <!-- Background Gradient -->
    <linearGradient id="bgGradient" x1="0" y1="0" x2="160" y2="160" gradientUnits="userSpaceOnUse">
      <stop offset="0%" style="stop-color:#633c61;stop-opacity:0.1"/>
      <stop offset="100%" style="stop-color:#633c61;stop-opacity:0.05"/>
    </linearGradient>
    
    <!-- Key Gradient -->
    <linearGradient id="keyGradient" x1="80" y1="40" x2="80" y2="120" gradientUnits="userSpaceOnUse">
      <stop offset="0%" style="stop-color:#f05439"/>
      <stop offset="100%" style="stop-color:#633c61"/>
    </linearGradient>

    <!-- Glow Effect -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>
  </defs>

  <!-- Animated Background Circle -->
  <circle cx="80" cy="80" r="76" fill="url(#bgGradient)">
    <animate attributeName="opacity" values="0.5;0.7;0.5" dur="3s" repeatCount="indefinite"/>
  </circle>

  <!-- Key Design -->
  <g transform="translate(80 80) rotate(-45) translate(-80 -80)">
    <!-- Key Head (more modern design) -->
    <path d="M80 45
             a15 15 0 0 1 0 30
             a15 15 0 0 1 0 -30
             z" 
          fill="url(#keyGradient)" 
          stroke="#633c61" 
          stroke-width="2"
          filter="url(#glow)">
      <animate attributeName="stroke-width" values="2;2.5;2" dur="2s" repeatCount="indefinite"/>
    </path>
    
    <!-- Key Shaft (modernized) -->
    <rect x="77" y="70" width="6" height="45" 
          rx="3"
          fill="url(#keyGradient)" 
          stroke="#633c61" 
          stroke-width="2"
          filter="url(#glow)"/>
    
    <!-- Modern Key Notches -->
    <path d="M83 85 h8 a2 2 0 0 1 2 2 v2 a2 2 0 0 1 -2 2 h-8" 
          fill="url(#keyGradient)" 
          stroke="#633c61" 
          stroke-width="2"/>
    
    <path d="M83 95 h6 a2 2 0 0 1 2 2 v2 a2 2 0 0 1 -2 2 h-6" 
          fill="url(#keyGradient)" 
          stroke="#633c61" 
          stroke-width="2"/>
  </g>

  <!-- Subtle Circular Accent -->
  <circle cx="80" cy="80" r="60" 
          stroke="#f05439" 
          stroke-width="1" 
          fill="none" 
          opacity="0.2">
    <animate attributeName="r" values="60;62;60" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.2;0.3;0.2" dur="3s" repeatCount="indefinite"/>
  </circle>
</svg> 