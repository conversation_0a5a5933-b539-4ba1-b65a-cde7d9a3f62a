<?xml version="1.0" encoding="UTF-8"?>
<svg width="240" height="220" viewBox="0 0 240 220" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Shield Gradient -->
    <linearGradient id="shieldGradient" x1="120" y1="20" x2="120" y2="220" gradientUnits="userSpaceOnUse">
      <stop offset="0%" style="stop-color:#f05439"/>
      <stop offset="100%" style="stop-color:#633c61"/>
    </linearGradient>
    
    <!-- Glow Effect -->
    <filter id="shieldGlow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feComposite in="SourceGraphic" in2="blur" operator="over"/>
    </filter>

    <!-- Lock Shine -->
    <linearGradient id="lockShine" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f05439"/>
      <stop offset="100%" style="stop-color:#633c61"/>
    </linearGradient>
  </defs>

  <!-- Animated Background Shield -->
  <g filter="url(#shieldGlow)">
    <!-- Outer Shield -->
    <path d="M120 20L40 60V120C40 171.856 74.144 220 120 220C165.856 220 200 171.856 200 120V60L120 20Z" 
          fill="#633c61" 
          fill-opacity="0.1">
      <animate attributeName="fill-opacity" 
               values="0.1;0.15;0.1" 
               dur="3s" 
               repeatCount="indefinite"/>
    </path>

    <!-- Inner Shield -->
    <path d="M120 30L50 65V120C50 166.856 80.144 210 120 210C159.856 210 190 166.856 190 120V65L120 30Z" 
          fill="url(#shieldGradient)" 
          fill-opacity="0.15">
      <animate attributeName="fill-opacity" 
               values="0.15;0.2;0.15" 
               dur="2.5s" 
               repeatCount="indefinite"/>
    </path>
  </g>

  <!-- Lock Design -->
  <g transform="translate(120 100) scale(0.95)">
    <!-- Lock Body with Gradient -->
    <rect x="-30" y="10" width="60" height="45" rx="12" 
          fill="url(#lockShine)" 
          filter="url(#shieldGlow)">
      <animate attributeName="opacity" 
               values="0.9;1;0.9" 
               dur="2s" 
               repeatCount="indefinite"/>
    </rect>

    <!-- Lock Shackle -->
    <path d="M-15 10V0C-15 -8.284 -8.284 -15 0 -15C8.284 -15 15 -8.284 15 0V10" 
          stroke="url(#lockShine)" 
          stroke-width="8" 
          stroke-linecap="round"
          filter="url(#shieldGlow)">
      <animate attributeName="stroke-opacity" 
               values="0.9;1;0.9" 
               dur="2s" 
               repeatCount="indefinite"/>
    </path>

    <!-- Lock Keyhole -->
    <circle cx="0" cy="32" r="6" 
            fill="#633c61" 
            fill-opacity="0.3">
      <animate attributeName="fill-opacity" 
               values="0.3;0.4;0.3" 
               dur="2s" 
               repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Subtle Accent Lines -->
  <path d="M120 40L60 70" 
        stroke="#f05439" 
        stroke-width="1" 
        stroke-opacity="0.2" 
        stroke-dasharray="4 4">
    <animate attributeName="stroke-opacity" 
             values="0.2;0.3;0.2" 
             dur="3s" 
             repeatCount="indefinite"/>
  </path>
  <path d="M120 40L180 70" 
        stroke="#f05439" 
        stroke-width="1" 
        stroke-opacity="0.2" 
        stroke-dasharray="4 4">
    <animate attributeName="stroke-opacity" 
             values="0.2;0.3;0.2" 
             dur="3s" 
             repeatCount="indefinite"/>
  </path>
</svg> 