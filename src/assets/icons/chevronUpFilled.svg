<svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 162979">
<circle id="Ellipse 2052" cx="16.8335" cy="16" r="16" fill="url(#paint0_linear_803_1820)"/>
<g id="Icons" filter="url(#filter0_d_803_1820)">
<path id="Vector" d="M12.8335 20L20.8335 12" stroke="#ECE7EB" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector_2" d="M14.3335 12H20.8335V18.5" stroke="#ECE7EB" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_803_1820" x="4.8335" y="8" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_803_1820"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_803_1820" result="shape"/>
</filter>
<linearGradient id="paint0_linear_803_1820" x1="16.0017" y1="28.7273" x2="34.6067" y2="26.142" gradientUnits="userSpaceOnUse">
<stop stop-color="#3C0B3A"/>
<stop offset="1" stop-color="#633C61"/>
</linearGradient>
</defs>
</svg>
