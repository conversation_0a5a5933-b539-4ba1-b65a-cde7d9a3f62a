import {createCipheriv, createDecipheriv, randomBytes} from 'crypto';

/**
 * Derives a 32-byte encryption key from the first 4 words of the mnemonic
 */
const deriveKeyFromMnemonic = (mnemonic: string): Buffer => {
  const words = mnemonic.split(' ').slice(0, 4).join(' '); // Extract first 4 words
  const hash = Buffer.from(words).toString('hex').padEnd(64, '0'); // Ensure 32-byte key
  return Buffer.from(hash, 'hex').slice(0, 32);
};

/**
 * Encrypts the first 4 words of the mnemonic using AES-256-GCM
 */
export const encryptMnemonic = (mnemonic: string): string => {
  const key = deriveKeyFromMnemonic(mnemonic);
  const iv = randomBytes(12); // Generate a 12-byte IV

  const cipher = createCipheriv('aes-256-gcm', key, iv);
  let encrypted = cipher.update(mnemonic.split(' ').slice(0, 4).join(' '), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  const authTag = cipher.getAuthTag(); // Authentication tag for integrity

  return Buffer.concat([iv, Buffer.from(encrypted, 'hex'), authTag]).toString('base64');
};

/**
 * Decrypts the encrypted mnemonic and verifies it against the first 4 words.
 */
export const verifyMnemonic = (encryptedMessage: string, mnemonic: string): boolean => {
  try {
    const key = deriveKeyFromMnemonic(mnemonic);
    const buffer = Buffer.from(encryptedMessage, 'base64');

    const iv = buffer.slice(0, 12);
    const encryptedText = buffer.slice(12, -16);
    const authTag = buffer.slice(-16);

    const decipher = createDecipheriv('aes-256-gcm', key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encryptedText).toString('utf8');
    decrypted += decipher.final('utf8');

    console.log('DECRYPTED >>>>> ', decrypted);

    // Fix: If the decrypted text is in ASCII character codes format, convert it to string
    if (decrypted.includes(',')) {
      const asciiCodes = decrypted.split(',').map(Number);
      decrypted = String.fromCharCode(...asciiCodes);
      console.log('CONVERTED DECRYPTED >>>>> ', decrypted);
    }

    console.log('MNEMONIC TO COMPARE >>>>> ', mnemonic.split(' ').slice(0, 4).join(' '));

    return decrypted === mnemonic.split(' ').slice(0, 4).join(' '); // Compare
  } catch (error) {
    console.error('Error verifying mnemonic:', error);
    return false;
  }
};
