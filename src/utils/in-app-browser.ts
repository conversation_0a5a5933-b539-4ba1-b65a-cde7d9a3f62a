import {Dimensions, Linking, Platform, StatusBar} from 'react-native';
import {InAppBrowser} from 'react-native-inappbrowser-reborn';

import GlobalStyles from '@/constants/GlobalStyles';
import {showWarningToast} from './toast';

const sleep = (timeout: number) =>
  new Promise<void>((resolve) => setTimeout(resolve, timeout));

export const openLink = async (url: string, animated = true) => {
  try {
    const {width, height} = Dimensions.get('window');
    if (await InAppBrowser.isAvailable()) {
      // A delay to change the StatusBar when the browser is opened
      const delay = animated && Platform.OS === 'ios' ? 400 : 0;
      setTimeout(() => StatusBar.setBarStyle('light-content'), delay);
      const result = await InAppBrowser.open(url, {
        // iOS Properties
        dismissButtonStyle: 'cancel',
        preferredBarTintColor: GlobalStyles.primary.primary500,
        preferredControlTintColor: 'white',
        animated,
        modalPresentationStyle: 'formSheet',
        modalTransitionStyle: 'coverVertical',
        modalEnabled: true,
        enableBarCollapsing: true,
        formSheetPreferredContentSize: {
          width: width - width / 6,
          height: height - height / 6,
        },
        // Android Properties
        showTitle: true,
        toolbarColor: '#6200EE',
        secondaryToolbarColor: 'black',
        navigationBarColor: 'black',
        navigationBarDividerColor: 'white',
        enableUrlBarHiding: true,
        enableDefaultShare: true,
        forceCloseOnRedirection: false,
        // Specify full animation resource identifier(package:anim/name)
        // or only resource name(in case of animation bundled with app).
        animations: {
          startEnter: 'slide_in_right',
          startExit: 'slide_out_left',
          endEnter: 'slide_in_left',
          endExit: 'slide_out_right',
        },
        headers: {
          'my-custom-header': 'my custom header value',
        },
        hasBackButton: true,
        browserPackage: undefined,
        showInRecents: true,
        includeReferrer: true,
      });
    } else {
      Linking.openURL(url);
    }
  } catch (error) {
    await sleep(50);
    showWarningToast('Something went wrong');
  } finally {
    StatusBar.setBarStyle('dark-content');
  }
};
