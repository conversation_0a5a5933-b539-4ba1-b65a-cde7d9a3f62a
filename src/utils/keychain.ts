import * as Keychain from 'react-native-keychain';

export const getKeychainValue = async (identifier: string, password: string) => {
  try {
    const passwordData = await Keychain.getGenericPassword({
      service: `assetify-password_${identifier}`,
    });

    if (!passwordData || passwordData.password !== password) {
      console.error('Invalid password or no password set');
      return null;
    }

    const dataStored = await Keychain.getGenericPassword({
      service: `assetify-recovery_${identifier}`,
    });

    if (!dataStored) return null;

    return dataStored;
  } catch (error) {
    console.error('[keychain] Error retrieving keychaindata:', error);
    return null;
  }
};

export const setKeychainValue = async ({
  walletLabel,
  value,
  uniqueId,
  password,
}: {
  walletLabel: string;
  value: string;
  uniqueId: string;
  password: string;
}) => {
  try {
    await Keychain.setGenericPassword(walletLabel, password, {
      service: `assetify-password_${walletLabel}__${uniqueId}`,
      accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
    });

    await Keychain.setGenericPassword(walletLabel, value, {
      service: `assetify-recovery_${walletLabel}__${uniqueId}`,
      accessible: Keychain.ACCESSIBLE.WHEN_UNLOCKED_THIS_DEVICE_ONLY,
    });

    return true;
  } catch (error) {
    console.error('[keychain] Error storing keychain data:', error);
    return false;
  }
};

export const resetKeychainValue = async (key: string) => {
  try {
    const result = await Keychain.resetGenericPassword({
      service: key,
    });

    return result;
  } catch (e) {
    console.log(e);
  }
};

/**
 * WARNING: This function will wipe all known device keychain data.
 * @returns {Promise<void>}
 */
export const wipeKeychain = async (): Promise<void> => {
  const services = await Keychain.getAllGenericPasswordServices();
  await Promise.all(services.map((key) => resetKeychainValue(key)));
};
