import ReactNativeBiometrics from 'react-native-biometrics';

const rnBiometrics = new ReactNativeBiometrics({
  allowDeviceCredentials: true,
});

export enum BiometricError {
  NOT_AVAILABLE = 'Biometrics not available',
  AUTH_FAILED = 'Authentication failed',
  IN_PROGRESS = 'Authentication in progress',
}

let BIOMETRICS_IN_PROGRESS = false;

export const areBiometricsAvailable = async (): Promise<boolean> => {
  const {available} = await rnBiometrics.isSensorAvailable();
  return available;
};

export const triggerBiometrics = async (): Promise<{
  success: boolean;
  error?: BiometricError;
}> => {
  if (BIOMETRICS_IN_PROGRESS) {
    return {success: false, error: BiometricError.IN_PROGRESS};
  }

  const {available} = await rnBiometrics.isSensorAvailable();
  if (!available) {
    return {success: false, error: BiometricError.NOT_AVAILABLE};
  }

  try {
    BIOMETRICS_IN_PROGRESS = true;
    const {success} = await rnBiometrics.simplePrompt({
      promptMessage: 'Please authenticate yourself',
    });

    if (!success) {
      console.log('Biometric authentication failed or canceled');
      return {success: false, error: BiometricError.AUTH_FAILED};
    }

    return {success: true};
  } catch (e) {
    console.log('Biometric prompt error:', e);
    return {success: false, error: BiometricError.AUTH_FAILED};
  } finally {
    BIOMETRICS_IN_PROGRESS = false;
  }
};
