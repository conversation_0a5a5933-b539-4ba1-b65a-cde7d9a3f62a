
export interface BchUtxo {
    txId: string;
    outputIndex: number;
    script: string;      // hex-encoded scriptPub<PERSON>ey
    satoshis: number;
  }
  
  // utils/bch.ts   (or wherever toBitcoreUtxo lives)
  export const toBitcoreUtxo = (input: any): BchUtxo => ({
    txId:        input.txid,          // unchanged
    outputIndex: input.outputIndex,   // <— THIS was the blocker
    script:      input.script,        // already the hex string
    satoshis:    input.satoshis,      // already a number
  });