import {PixelRatio} from 'react-native';

export const parsePrice = (price: string) => {
  price = price ?? '0.00';
  let priceBeforeDecimal = price.split('.')[0];
  // Split the price before the decimal point into groups of 3 digits
  priceBeforeDecimal = priceBeforeDecimal.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  // Check if there is a decimal point in the price
  let priceAfterDecimal = price.split('.')[1]
    ? price.split('.')[1].replace(TrailingZeroRegex, '')
    : '';
  return priceAfterDecimal != ''
    ? priceBeforeDecimal + '.' + priceAfterDecimal
    : priceBeforeDecimal;
};

export const getFontSize = (originalSize: number) => {
  if (PixelRatio.get() < 1.5) {
    return (originalSize * 0.5) / PixelRatio.get();
  } else if (PixelRatio.get() >= 1.5 && PixelRatio.get() < 2.5) {
    return (originalSize * 1.5) / PixelRatio.get();
  } else if (PixelRatio.get() >= 2.5) {
    return (originalSize * 2.5) / PixelRatio.get();
  } else {
    return originalSize;
  }
};

// TODO: to be removed
export const getScreenAlignment = (
  height: number,
  largeDevicePosition: string,
  smallDevicePosition: string,
) => (height > 800 ? largeDevicePosition : smallDevicePosition);

export const symbolToSvg = (symbol: string) => {
  // Remove the '.e' from the symbol and convert all '.' to '' and then convert to uppercase
  return `${symbol.replace('.e', '').replace(/\./g, '')}Svg`.toUpperCase();
};

export const NameParsing = (name: string) => {
  switch (name) {
    case 'bitcoin':
      return 'Bitcoin';
    case 'ethereum':
      return 'Ethereum';
    case 'binance-smart-chain':
      return 'Binance Smart Chain';
    case 'trx':
      return 'Tron';
    case 'xrp':
      return 'XRP';
    case 'litecoin':
      return 'Litecoin';
    case 'bitcoin-cash':
      return 'Bitcoin Cash';
    case 'dash':
      return 'Dash';
    case 'dogecoin':
      return 'Dogecoin';
    case 'kaspa':
      return 'Kaspa';
    case 'avalanche':
      return 'Avalanche';
    case 'solana':
      return 'Solana';
    case 'usdt':
      return 'USDT';
    default:
      return name;
  }
};

export const TrailingZeroRegex = /\.?0+$/;
