import {nodeInfo, signMessage} from '@breeztech/react-native-breez-sdk';
import axios from 'axios';

import {LNURL_CALLBACK, LNURL_PAY, WS_NOTIF} from '@env';

type GenerateInvoiceMethod = (
  amount: number,
  note?: string,
  setOpeningFeeInSat?: (fee: number) => void,
) => Promise<string | undefined>;

type SetUrlMethod = (url: string) => void;

class LightningService {
  public webSocket: WebSocket | null = null;

  private generateInvoice: GenerateInvoiceMethod | undefined;
  private setLnUrl: SetUrlMethod | undefined;

  constructor(generateInvoice?: GenerateInvoiceMethod, setUrl?: SetUrlMethod) {
    this.generateInvoice = generateInvoice;
    this.setLnUrl = setUrl;
  }

  private async openWebSocket(pubkey: string): Promise<void> {
    this.webSocket = new WebSocket(WS_NOTIF, 'echo-protocol');

    this.webSocket.onopen = () => {
      console.log('WebSocket connection opened!');
      this.webSocket?.send(JSON.stringify({type: 'register', pubkey}));
    };

    this.webSocket.onclose = () => {
      console.log('WebSocket connection closed!');
    };

    this.webSocket.onmessage = async (message: any) => {
      await this.handleWebSocketMessage(message);
    };
  }

  private async handleWebSocketMessage(message: any): Promise<void> {
    const data = JSON.parse(message.data);

    if (data.type === 'lnurlpay_info') {
      await this.handleLnurlPayInfo(data);
    }

    if (data.type === 'lnurlpay_invoice') {
      await this.handleLnurlPayInvoice(data);
    }
  }

  private async handleLnurlPayInfo(data: any): Promise<void> {
    const {reply_url, callback_url} = data;

    const payload = {
      callback: callback_url,
      maxSendable: 100_000_000,
      minSendable: 1_000,
      metadata: '[["text/plain","Pay to Assetify user"]]',
      tag: 'payRequest',
    };

    await axios.post(reply_url, payload);
  }

  private async handleLnurlPayInvoice(data: any): Promise<void> {
    const {reply_url, amount} = data;

    const invoice = await this.generateInvoice!(amount, '');
    await axios.post(reply_url, {pr: invoice, routes: []});
  }

  public async getLnurl(): Promise<void> {
    try {
      const pubkey = (await nodeInfo()).id;
      await this.openWebSocket(pubkey);

      const time = Math.floor(Date.now() / 1000);
      const webhookUrl = LNURL_CALLBACK;
      const {signature} = await signMessage({
        message: `${time}-${webhookUrl}`,
      });

      const response = await axios.post(`${LNURL_PAY}${pubkey}`, {
        time,
        webhook_url: webhookUrl,
        signature,
      });

      this.setLnUrl?.(response.data.lnurl.toUpperCase());
    } catch (e: any) {
      console.error('[LightningService] Error getting/processing LNURL:', e);
      // Set empty URL to indicate error state and stop loading
      this.setLnUrl?.('');
      // Close WebSocket if it's open
      if (this.webSocket) {
        this.webSocket.close();
        this.webSocket = null;
      }
    }
  }
}

export default LightningService;
