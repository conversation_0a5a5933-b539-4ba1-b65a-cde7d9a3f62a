import { KeystoreInstance } from "./BackendServices";

export interface ILoanMPCShareEncrypted {
    shareCipherBundle: {
        iv: string;
        ciphertext: string;
        authTag: string;
    };
    wrappedKeyBundle: {
        iv: string;
        wrapped: string;
        authTag: string;
    };
    loanID: string;
}

export class LoanMPCSharesService {

    public async getLoanMPCShares(btcAddress: string): Promise<ILoanMPCShareEncrypted[]> {
        try {
            const shares = await KeystoreInstance.get(`/keystore/${btcAddress}`);

            return shares.data;
        } catch (err) {
            console.error('Error getting MPC shares:', err);
            throw err;
        }
    }

    public async createLoanMPCShare(share: ILoanMPCShareEncrypted, btcAddress: string): Promise<void> {
        try {

            await KeystoreInstance.post('/keystore', {
                loanID: share.loanID,
                btcAddress: btcAddress,
                shareCipherBundle: share.shareCipherBundle,
                wrappedKeyBundle: share.wrappedKeyBundle,
            })

        } catch (err) {
            console.error('Error creating MPC share:', err);
            throw err;
        }
    }
    
}