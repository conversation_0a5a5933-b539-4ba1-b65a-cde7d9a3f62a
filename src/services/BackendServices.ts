import {
  AUTHENTICATION_SERVICE,
  EMAIL_LOGGER_SERVICE,
  NOTIFICATION_SERVICE,
  PRICE_FETCHING_SERVICE,
  STAKING_CALCULATOR_BACKEND_SERVICE,
  SWAPS_SERVICE,
  WALLET_BALANCE_SERVICE,
  WALLET_LOGGER_SERVICE,
  STATUS_SERVICE,
  RAMPS_SERVICE,
  LOAN_SERVICE,
  MPC_KEYSTORE_SERVICE,
} from '@env';
import axios from 'axios';
import {store} from '@/storage/store';

const WalletLoggerInstance = axios.create({
  baseURL: WALLET_LOGGER_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const StakingCalculatorInstance = axios.create({
  baseURL: STAKING_CALCULATOR_BACKEND_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const WalletBalanceInstance = axios.create({
  baseURL: WALLET_BALANCE_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const PriceFetchingInstance = axios.create({
  baseURL: PRICE_FETCHING_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const EmailLoggerInstance = axios.create({
  baseURL: EMAIL_LOGGER_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const NotificationInstance = axios.create({
  baseURL: 'http://192.168.10.157/notification-service/api/v1',
  headers: {
    'content-type': 'application/json',
  },
});

const AuthenticationInstance = axios.create({
  baseURL: 'http://192.168.10.157/auth',
  headers: {
    'content-type': 'application/json',
  },
});

const RampsInstance = axios.create({
  baseURL: RAMPS_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const ExchangeInstance = axios.create({
  baseURL: SWAPS_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const KeystoreInstance = axios.create({
  baseURL: MPC_KEYSTORE_SERVICE,
  headers: {
    'content-type': 'application/json',
  },
});

const StatusInstance = axios.create({
  baseURL: STATUS_SERVICE,
  // headers: {
  //   'content-type': 'application/json',
  // },
});

export function createLoanInstance(token: string) {
  return axios.create({
    baseURL: 'http://192.168.10.157/loan-service/api/v1',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });
}

const LoanInstance = axios.create({
  baseURL: LOAN_SERVICE,
  headers: {
    'content-type': 'application/json',
    Authorization: `Bearer ${store.getState().loan.accessToken}`,
  },
});

export {
  WalletLoggerInstance,
  StakingCalculatorInstance,
  WalletBalanceInstance,
  PriceFetchingInstance,
  EmailLoggerInstance,
  NotificationInstance,
  AuthenticationInstance,
  ExchangeInstance,
  RampsInstance,
  StatusInstance,
  LoanInstance,
  KeystoreInstance,
};
