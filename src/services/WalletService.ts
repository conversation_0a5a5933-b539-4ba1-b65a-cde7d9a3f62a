import * as ecc from '@bitcoin-js/tiny-secp256k1-asmjs';
import * as bitcoin from 'bitcoinjs-lib';
import {ECPairFactory, ECPairInterface} from 'ecpair';
import {Contract, TransactionRequest, Wallet, ethers, parseUnits} from 'ethers';
import * as HDKey from 'hdkey';
import {NativeModules} from 'react-native';
import {Wallet as xrplWallet} from 'xrpl';
import bitcore from 'bitcore-lib-cash';
// @ts-ignore
import * as bip39 from 'bip39';
const {Enumerations, Services} = require('@AssetifyNet/cryptoapis-kms');
const TronWeb = require('tronweb');
// needed for XRP
import 'text-encoding-polyfill';

const LTC_NETWORK = {
  bech32: 'ltc',
  messagePrefix: '\x19Litecoin Signed Message:\n',
  bip32: {
    public: 0x019da462,
    private: 0x019d9cfe,
  },
  bip49: {
    public: 0x01b26ef6,
    private: 0x01b26792,
  },
  bip84: {
    public: 0x04b24746,
    private: 0x04b2430c,
  },
  pubKeyHash: 0x30,
  scriptHash: 0x32,
  wif: 0xb0,
};

const BTC_CASH_NETWORK = {
  bech32: 'bch',
  messagePrefix: 'unused',
  bip32: {
    public: 0x0488b21e,
    private: 0x0488ade4,
  },
  pubKeyHash: 0x00,
  scriptHash: 0x05,
  wif: 0x80,
};

const DOGE_NETWORK = {
  bech32: 'doge',
  messagePrefix: '\x19Dogecoin Signed Message:\n',
  bip32: {
    public: 0x0488b21e,
    private: 0x02fac398,
  },
  pubKeyHash: 0x1e,
  scriptHash: 0x16,
  wif: 0x9e,
};

import {
  AuthAddress,
  AuthAddressType,
  AuthAddresses,
  AuthKaspaAddress,
  AuthUserWallet,
  NativeUserWallet,
} from '@/types/authTypes';
import {ChainToSymbol, ChainsOrder, EVMChainsIds} from '../constants/Chains';
import {LoggerRequest} from '../types/walletTypes';
import {WalletBalanceInstance, WalletLoggerInstance} from './BackendServices';
import { toBitcoreUtxo } from '@/utils/bch-utxo';
import { WALLET_BALANCE_SERVICE } from '@env';

const {WalletManagerBridge} = NativeModules;
const ECPair = ECPairFactory(ecc);

export default class WalletService {
  constructor() {}

  createWallets = async (mnemonic: string) => {
    const walletTypes = [
      Enumerations.Blockchains.BITCOIN,
      Enumerations.Blockchains.ETHEREUM,
      'solana',
      Enumerations.Blockchains.BINANCE_SMART_CHAIN,
      Enumerations.Blockchains.TRX,
      Enumerations.Blockchains.XRP,
      Enumerations.Blockchains.KASPA,
      Enumerations.Blockchains.AVALANCHE,
    ];

    let createdWallets: NativeUserWallet[] = [];

    try {
      const walletPromises: Promise<NativeUserWallet>[] = walletTypes.map(
        async (blockchain: string) => {
          if (blockchain !== Enumerations.Blockchains.KASPA) {
            return await WalletManagerBridge.createHDWalletFromMnemonic(
              mnemonic,
              blockchain,
            );
          } else {
            const walletService = new Services.WalletService(blockchain, 'mainnet');
            const wallet = await walletService.createHDWalletFromMnemonic(mnemonic);
            const formattedWallet: NativeUserWallet = {
              blockchain: blockchain,
              network: 'mainnet',
              mnemonic: wallet.mnemonic,
              seed: wallet.seed,
              xPubsList: wallet.xPubsList,
            };

            return formattedWallet;
          }
        },
      );

      createdWallets = await Promise.all(walletPromises);
      return createdWallets;
    } catch (error) {
      console.error(error);
    }
  };

  createAddresses = async (wallets: NativeUserWallet[]) => {
    console.log('wallets: ', wallets);
    const kaspaWallet = wallets.find(
      (wallet: NativeUserWallet) => wallet.blockchain === Enumerations.Blockchains.KASPA,
    );

    let kaspaAddresses: AuthAddress[] = [];
    try {
      const addressService = new Services.AddressService(
        kaspaWallet?.blockchain,
        'mainnet',
      );

      const walletService = new Services.WalletService(
        kaspaWallet?.blockchain,
        'mainnet',
      );

      const kaspaHDWallet = await walletService.createHDWalletFromMnemonic(
        kaspaWallet?.mnemonic,
      );

      for (let i = 0; i < 10; i++) {
        const address = await addressService.generateAddressFromHDWalletWithCustomPath(
          kaspaHDWallet,
          `/0/${i}`,
        );

        kaspaAddresses.push(address._data === undefined ? address : address._data);
      }
    } catch (error) {
      console.error(error);
    }

    const addresses = wallets.map((wallet: NativeUserWallet) => {
      if (wallet.blockchain !== Enumerations.Blockchains.KASPA) {
        return {
          address: wallet.address,
          privateKey: wallet.privateKey,
          publicKey: wallet.publicKey,
          chain: wallet.blockchain,
        };
      } else {
        return {
          addresses: kaspaAddresses,
          chain: Enumerations.Blockchains.KASPA,
        };
      }
    });

    return addresses;
  };

  walletLoggerBTC = async (btcAddress: string, xPubKey: string) =>
    WalletLoggerInstance.post(`/v1/wallet`, {
      bitcoin_address: btcAddress,
      bitcoin_xpub: xPubKey,
      bitcoin_network: 'mainnet',
    })
      .then((response) => response)
      .catch((error) => {
        if (error.response?.status === 409) {
          console.log(error.response);
          return error.response;
        } else {
          console.log('logger', error);
          throw error;
        }
      });

  createLoggerRequest = async (
    createdWallets: NativeUserWallet[],
    createdAddresses: AuthAddresses,
  ) => {
    let wallets: any[] = [];
    let addresses: AuthAddresses = [];
    let loggerRequest: any[] = [];

    console.log('createdAddresses: ', createdAddresses);

    let btcAddress = createdAddresses.find(
      (address: AuthAddressType) => address?.chain === Enumerations.Blockchains.BITCOIN,
    ) as AuthAddress;

    for (let i = 0; i < createdWallets.length; i++) {
      const currentWallet = createdWallets[i];
      if (!currentWallet) continue;

      let index = ChainsOrder.indexOf(currentWallet.blockchain);
      if (index === -1) continue;

      wallets[index] = {
        seed: currentWallet.seed,
        zPub: currentWallet.xPubsList?.[0],
        mnemonic: currentWallet.mnemonic,
        blockchain: currentWallet.blockchain,
        network: currentWallet.network,
      };

      if (currentWallet.blockchain === Enumerations.Blockchains.BITCOIN) {
        addresses[index] = btcAddress;
      } else {
        let address = createdAddresses.find(
          (address: AuthAddressType) => address.chain === currentWallet.blockchain,
        ) as AuthAddress | AuthKaspaAddress;

        if (address === undefined) {
          console.error('Address not found', currentWallet.blockchain);
          throw new Error('Address not found');
        }

        if (address.chain === Enumerations.Blockchains.KASPA) {
          addresses[index] = {
            // @ts-ignore
            addresses: address.addresses,
            chain: address.chain,
          } as AuthAddressType;
        } else {
          addresses[index] = {
            ...address, // @ts-ignore
            address: address.address.replace('bitcoincash:', ''),
          };
        }

        const walletAtIndex = wallets[index];
        const addressAtIndex = addresses[index];

        if (walletAtIndex && addressAtIndex && btcAddress) {
          loggerRequest[index - 1] = {
            btcAddress: btcAddress.address,
            chain: walletAtIndex.blockchain === 'trx' ? 'tron' : walletAtIndex.blockchain,
            xpub: walletAtIndex.zPub?.accountXpub,
            address:
              addressAtIndex.chain === 'kaspa' // @ts-ignore
                ? addressAtIndex.addresses // @ts-ignore
                : addressAtIndex.address,
            network: walletAtIndex.network,
          };
        }
      }
    }

    return {wallets, addresses, loggerRequest};
  };

  async walletLogger(loggerRequestArray: LoggerRequest[]) {
    return WalletLoggerInstance.post(
      `/v1/wallet/${loggerRequestArray[0]?.btcAddress}/add-xpub`,
      {
        chains: loggerRequestArray.map((loggerRequest: LoggerRequest) => {
          if (loggerRequest.address instanceof Array)
            return {
              chain: loggerRequest.chain,
              xpub: loggerRequest.xpub,
              address: loggerRequest.address[0].address,
              kaspaxPubAddresses: loggerRequest.address
                .map((a: any) => a.address)
                .join(','),
              network: loggerRequest.network,
            };
          return {
            chain: loggerRequest.chain,
            xpub: loggerRequest.xpub,
            address:
              loggerRequest.address instanceof Array
                ? loggerRequest.address[0].address
                : loggerRequest.address,
            network: loggerRequest.network,
          };
        }),
      },
    )
      .then((response) => response.data)
      .then((json) => {
        console.log('json', json);
      })
      .catch((error) => {
        console.error('Error in walletLogger:', error);
      });
  }

  async prepareAndSignUTXOTransaction(
    wallet: AuthUserWallet,
    receiverAddress: string,
    amount: number,
    network: 'mainnet' | 'testnet' = 'mainnet',
  ): Promise<{txHex: string; txId: string}> {
    try {
      // 1. Sync wallet first
      await WalletLoggerInstance.post(
        `/v1/wallet/sync/${wallet.zPub.accountXpub}/${wallet.blockchain}/${wallet.network}`,
      );

      // 2. Get prepared transaction data from backend
      const {data: response} = await WalletBalanceInstance.post(
        `/prepare-utxo-transaction/${wallet.blockchain}`,
        {
          payload: {
            data: {
              item: {
                xpub: wallet.zPub.accountXpub,
                fee: {
                  priority: 'standard',
                },
                prepareStrategy: 'minimize-dust',
                recipients: [
                  {
                    address: receiverAddress,
                    amount: amount.toString(),
                  },
                ],
              },
            },
          },
        },
      );

      console.log('response: ', response);

      if (!response || !response.inputs || !response.outputs) {
        throw new Error('Invalid response from prepare-utxo-transaction API');
      }

      // 3. Set up network configuration for `bitcoinjs-lib`
      const btcNetwork =
        network === 'mainnet' ? bitcoin.networks.bitcoin : bitcoin.networks.testnet;

      // 4. Create HD wallet from mnemonic (more reliable than extended keys)
      let hdWallet: any;
      try {
        console.log('Creating HD wallet from mnemonic...');
        const seed = bip39.mnemonicToSeedSync(wallet.mnemonic);
        hdWallet = HDKey.fromMasterSeed(seed);
        console.log('HD wallet created successfully');
      } catch (error) {
        console.error('Error creating HD wallet from mnemonic:', error);
        throw new Error('Failed to create HD wallet from mnemonic');
      }

      // 5. Create transaction builder
      const psbt = new bitcoin.Psbt({network: btcNetwork});

      // 6. Process inputs and collect signing keys
      const signingKeys: ECPairInterface[] = [];

      for (let i = 0; i < response.inputs.length; i++) {
        const input = response.inputs[i];
        console.log(`Processing input ${i}:`, input);

        try {
          // 7. Parse and derive the private key for this input
          // Extract derivation path from the full path (e.g., "m/84'/0'/0'/0/0")
          const fullPath = input.path;
          console.log('Full derivation path:', fullPath);

          // Derive the key using the full path
          const derivedKey = hdWallet.derive(fullPath);
          const keyPair = ECPair.fromPrivateKey(derivedKey.privateKey, {
            network: btcNetwork,
          });

          signingKeys.push(keyPair);

          // 8. Add input to PSBT
          psbt.addInput({
            hash: input.txid,
            index: input.vout,
            witnessUtxo: {
              script: Buffer.from(input.scriptPubKey, 'hex'),
              value: parseInt(input.value),
            },
          });

          console.log(`Input ${i} added successfully`);
        } catch (error) {
          console.error(`Error processing input ${i}:`, error);
          throw new Error(`Failed to process input ${i}: ${error.message}`);
        }
      }

      // 9. Add outputs
      for (let i = 0; i < response.outputs.length; i++) {
        const output = response.outputs[i];
        console.log(`Adding output ${i}:`, output);

        psbt.addOutput({
          address: output.address,
          value: parseInt(output.amount),
        });
      }

      // 10. Sign all inputs
      for (let i = 0; i < signingKeys.length; i++) {
        const keyPair = signingKeys[i];
        console.log(`Signing input ${i}...`);

        try {
          if (!keyPair) {
            throw new Error(`No signing key available for input ${i}`);
          }

          // Create a Buffer-compatible signer for PSBT
          const signer = {
            publicKey: Buffer.from(keyPair.publicKey),
            sign: (hash: Buffer) => Buffer.from(keyPair.sign(hash)),
          };

          psbt.signInput(i, signer);
          console.log(`Input ${i} signed successfully`);
        } catch (error) {
          console.error(`Error signing input ${i}:`, error);
          throw new Error(`Failed to sign input ${i}: ${error.message}`);
        }
      }

      // 11. Validate signatures
      console.log('Validating signatures...');
      const validationResult = psbt.validateSignaturesOfAllInputs(
        (pubkey: Buffer, msghash: Buffer, signature: Buffer): boolean => {
          return ECPair.fromPublicKey(pubkey, {network: btcNetwork}).verify(
            msghash,
            signature,
          );
        },
      );

      if (!validationResult) {
        throw new Error('Signature validation failed');
      }

      console.log('All signatures validated successfully');

      // 12. Finalize and extract transaction
      console.log('Finalizing transaction...');
      psbt.finalizeAllInputs();
      const transaction = psbt.extractTransaction();

      const result = {
        txHex: transaction.toHex(),
        txId: transaction.getId(),
      };

      console.log('Transaction prepared successfully:', result);
      return result;
    } catch (error) {
      console.error('Error in prepareAndSignUTXOTransaction:', error);
      throw error;
    }
  }

  async prepareStableCoinTransaction(
    authWallet: AuthUserWallet,
    coinData: {address: string; abi: any; decimals: number},
    value: number,
    chainId: number,
    senderAddress: string,
    recipientAddress: string,
  ): Promise<any> {
    const wallet = ethers.Wallet.fromPhrase(authWallet.mnemonic);
    const contract = new Contract(coinData.address, coinData.abi, wallet);
    const amount = parseUnits(value.toFixed(6), coinData.decimals);

    if (chainId === 1 || chainId === 43114) {
      const payload: TransactionRequest = {
        to: coinData.address,
        from: senderAddress,
        data: contract.interface.encodeFunctionData('transfer', [
          recipientAddress,
          amount,
        ]),
      };

      const feeInHex = await WalletBalanceInstance.post(
        `/transaction/${
          authWallet.blockchain === 'avalanche'
            ? authWallet.blockchain
            : ChainToSymbol[authWallet.blockchain]
        }/${authWallet.network}/getfeeinwei`,
        {
          tx_data: payload,
        },
      );

      payload.chainId = chainId;
      payload.gasPrice = parseInt(feeInHex.data.gasPrice, 16);
      payload.gasLimit = parseInt(feeInHex.data.estimatedGas, 16);
      payload.nonce = feeInHex.data.nonce;

      return payload;
    } else if (chainId === 56) {
      let tx = {
        sender: senderAddress,
        recipient: coinData.address,
        chainId: chainId,
        amount: 0,
        data: contract.interface.encodeFunctionData('transfer', [
          recipientAddress,
          amount,
        ]),
        gasPrice: 0,
        gasLimit: 0,
        nonce: 0,
      };

      const feeInHex = await WalletBalanceInstance.post(
        `/transaction/${
          authWallet.blockchain === 'avalanche'
            ? authWallet.blockchain
            : ChainToSymbol[authWallet.blockchain]
        }/${authWallet.network}/getfeeinwei`,
        {
          tx_data: {
            to: coinData.address,
            from: senderAddress,
            data: tx.data,
          },
        },
      );

      // tx.gasPrice = parseInt(feeInHex.data.gasPrice, 16);
      // 1 gwei = 1000000000 wei
      tx.gasPrice = 1000000000;
      tx.gasLimit = parseInt(feeInHex.data.estimatedGas, 16);
      tx.nonce = feeInHex.data.nonce;

      return tx;
    } else if (chainId === 195) {
      console.log({
        senderAddress: senderAddress,
        contractAddress: coinData.address,
        functionSelector: 'transfer(address,uint256)',
        amount: 0,
        callValue: 0,
        paramters: [
          {
            type: 'address',
            value: recipientAddress,
          },
          {
            type: 'uint256',
            value: amount.toString(),
          },
        ],
      });
      try {
        const {data} = await WalletBalanceInstance.post(`/generate-transaction/tron`, {
          senderAddress: senderAddress,
          contractAddress: coinData.address,
          functionSelector: 'transfer(address,uint256)',
          amount: 0,
          callValue: 0,
          parameters: [
            {
              type: 'address',
              value: recipientAddress,
            },
            {
              type: 'uint256',
              value: amount.toString(),
            },
          ],
        });

        const resp = await WalletBalanceInstance.get(`/check-tron-energy`);
        const energyPrice = resp.data.energyPrice;

        const energySun = data.energyEstimate * energyPrice;
        const estimateTrx = energySun / 10 ** 6;

        return {
          tx: data.transaction.transaction,
          bandwidth: data.bandwidthEstimate,
          estimateTrx: estimateTrx,
        };
      } catch (err) {
        console.error(err);
      }
    }
  }

  async prepareEvmBasedTransaction(
    authWallet: AuthUserWallet,
    senderAddress: string,
    recipientAddress: string,
    amount: number,
  ): Promise<any> {
    if (authWallet.blockchain === 'ethereum' || authWallet.blockchain === 'avalanche') {
      const valueInWei = Number(amount) * 10 ** 18;
      let payload: TransactionRequest = {
        to: recipientAddress,
        from: senderAddress,
        value: valueInWei.toString(),
        chainId: EVMChainsIds[authWallet.blockchain],
      };

      const feeInHex = await WalletBalanceInstance.post(
        `/transaction/${
          authWallet.blockchain === 'avalanche'
            ? authWallet.blockchain
            : ChainToSymbol[authWallet.blockchain]
        }/${authWallet.network}/getfeeinwei`,
        {
          tx_data: payload,
        },
      );

      payload.gasPrice = parseInt(feeInHex.data.gasPrice, 16);
      payload.gasLimit = parseInt(feeInHex.data.estimatedGas, 16);
      payload.nonce = feeInHex.data.nonce;

      return payload;
    } else if (authWallet.blockchain === 'xrp') {
      const {data} = await WalletBalanceInstance.post(
        `/generate-transaction/xrp/payment`,
        {
          transactionType: 'Payment',
          account: senderAddress,
          amount: (amount * 10 ** 6).toString(),
          destination: recipientAddress,
        },
      );

      return data.unsignedTx;
    } else if (authWallet.blockchain === 'trx') {
      const {data} = await WalletBalanceInstance.post(`/generate-transaction/tron`, {
        senderAddress: senderAddress,
        recipientAddress: recipientAddress,
        amount: (amount * 10 ** 6).toString(),
      });

      return {
        tx: data.transaction,
        bandwidthEstimate: data.bandwidthEstimate,
      };
    } else if (authWallet.blockchain === 'binance-smart-chain') {
      const valueInWei = amount * 10 ** 18;
      const valueInHex = '0x' + valueInWei.toString(16);

      let payload: TransactionRequest = {
        to: recipientAddress,
        from: senderAddress,
        value: valueInHex,
        chainId: EVMChainsIds[authWallet.blockchain],
      };

      try {
        const feeInHex = await WalletBalanceInstance.post(
          `/transaction/${ChainToSymbol[authWallet.blockchain]}/${
            authWallet.network
          }/getfeeinwei`,
          {
            tx_data: payload,
          },
        );

        payload.gasPrice = feeInHex.data.gasPrice;
        payload.gasLimit = feeInHex.data.estimatedGas;
        payload.nonce = feeInHex.data.nonce;

        // Required in this format: https://github.com/Crypto-APIs/Crypto_APIs_Key_Management_System/blob/master/src/helpers/sign/bscSignerHelper.js#L21
        let bscFormattedPayload = {
          sender: payload.from,
          recipient: payload.to,
          amount: payload.value,
          gasLimit: payload.gasLimit,
          gasPrice: payload.gasPrice,
          nonce: payload.nonce,
          chainId: payload.chainId,
        };

        return bscFormattedPayload;
      } catch (err) {
        console.error(err);
      }
    }
  }

  async prepareKaspaTransaction(
    senderAddresses: AuthAddress[],
    receiverAddress: string,
    amount: number,
  ): Promise<any> {
    const amountInKas = amount * 10 ** 8;

    return await WalletBalanceInstance.post(`/prepare-kaspa-tx`, {
      senderAddresses: senderAddresses.map((address: AuthAddress) => address.address),
      receiverAddress: receiverAddress,
      amount: amountInKas,
    });
  }

  async sendPreparedTx(tx: any, privKey: string, wallet: AuthUserWallet): Promise<any> {
    try {
      let signedTx;

      // Handle UTXO-based transactions (Bitcoin, Bitcoin Cash, Litecoin, Dogecoin)
      if (
        wallet.blockchain === 'bitcoin' ||
        wallet.blockchain === 'bitcoin-cash' ||
        wallet.blockchain === 'litecoin' ||
        wallet.blockchain === 'dogecoin'
      ) {
        if(wallet.blockchain === 'bitcoin-cash') {
          return await this.signAndBroadcastBchTx(tx, wallet);
        } else {
          return await this.signAndBroadcastUTXOTransaction(tx, wallet);
        }
      }

      if (wallet.blockchain === 'ethereum' || wallet.blockchain === 'avalanche') {
        const walletInstance = new Wallet(privKey);
        signedTx = await walletInstance.signTransaction(tx);
      } else if (wallet.blockchain === 'trx') {
        const tronWeb = new TronWeb({
          fullHost: 'https://api.trongrid.io',
          privateKey: privKey,
        });

        console.log('tx', tx);
        let txWithSignature = await tronWeb.trx.sign(tx.tx);
        signedTx = txWithSignature;
        console.log('signed', signedTx);
      } else if (wallet.blockchain === 'xrp') {
        console.log('wallet from mnemonic? ', wallet.mnemonic);
        const xrpWallet = xrplWallet.fromMnemonic(wallet.mnemonic);

        console.log('Wallet', wallet);

        console.log('xrpWallet', xrpWallet);
        const {tx_blob: signed_tx_blob, hash: signed_tx_hash} = await xrpWallet.sign(tx);

        signedTx = signed_tx_blob;
      } else if (wallet.blockchain === 'binance-smart-chain') {
        const signService = new Services.SignService(wallet.blockchain, wallet.network);
        signedTx = await signService.signPreparedTransactionLocally(privKey, tx);
      } else {
        const signService = new Services.SignService(wallet.blockchain, wallet.network);
        signedTx = await signService.signPreparedTransactionLocally(
          wallet.zPub.accountXpriv,
          tx.data,
        );
      }

      if (wallet.blockchain !== 'trx' && wallet.blockchain !== 'avalanche') {
        try {
          console.log('broadcasting hex', signedTx)
          const res = await WalletBalanceInstance.post(
            `/broadcast-transaction/${
              wallet.blockchain == 'trx' ? 'tron' : wallet.blockchain
            }`,
            {
              tx: signedTx.raw ? signedTx.raw : signedTx,
            },
          );

          return res;
        } catch (err) {
          console.error(err);
        }
      } else {
        console.log('broadcasting hex', signedTx)
        return await WalletBalanceInstance.post(
          `/broadcast-transaction/${
            wallet.blockchain == 'trx' ? 'tron' : wallet.blockchain
          }`,
          {
            tx: signedTx,
          },
        );
      }
    } catch (err) {
      console.error(err);
    }
  }

  async updateLastLoggedIn(bitcoinAddress: string): Promise<any> {
    return await WalletLoggerInstance.post(`/v1/wallet/${bitcoinAddress}/last-logged-in`);
  }

  async prepareUTXOTransaction(
    wallet: AuthUserWallet,
    receiverAddress: string,
    amount: number,
    network: 'mainnet' | 'testnet' = 'mainnet',
  ): Promise<any> {
    try {
      // 1. Sync wallet first
      await WalletLoggerInstance.post(
        `/v1/wallet/sync/${wallet.zPub.accountXpub}/${wallet.blockchain}/${wallet.network}`,
      );

      console.log(WALLET_BALANCE_SERVICE)
      // 2. Get prepared transaction data from backend
      const {data: response} = await WalletBalanceInstance.post(
        `/prepare-utxo-transaction/${wallet.blockchain}`,
        {
          payload: {
            data: {
              item: {
                xpub: wallet.zPub.accountXpub,
                fee: {
                  priority: 'standard',
                },
                prepareStrategy: 'minimize-dust',
                recipients: [
                  {
                    address: receiverAddress,
                    amount: amount.toString(),
                  },
                ],
              },
            },
          },
        },
      );

      console.log('UTXO prepare response: ', response);

      if (!response || !response.inputs || !response.outputs) {
        throw new Error('Invalid response from prepare-utxo-transaction API');
      }

      // 3. Transform the response to match the expected format
      const transformedInputs = response.inputs.map((input: any) => {
        console.log('input >>>', input);
        const path = input.path.split('/');

        console.log('RETURN INPUTS >>>>', {
          address: input.address,
          txid: input.txid,
          satoshis: parseInt(input.value),
          script: input.scriptPubKey,
          outputIndex: input.vout,
          derivationIndex: path.pop(),
          change: path.slice(-2, -1)[0],
        });

        return {
          address: input.address,
          txid: input.txid,
          satoshis: parseInt(input.value),
          script: input.scriptPubKey,
          outputIndex: input.vout,
          derivationIndex: path.pop(),
          change: path.slice(-2, -1)[0],
          path: path,
          rawTx: input.rawTx,
          isSegwit: input.isSegwit,
          fullPath: input.path,
        };
      });

      // Pre-transform outputs to calculate fee
      const tempTransformedOutputs = response.outputs.map((output: any) => ({
        address: output.address,
        satoshis: parseInt(output.amount),
        amount: parseInt(output.amount),
      }));

      // 4. Calculate transaction size and fee based on inputs/outputs
      const calculatedFee = this.calculateUTXOTransactionFee(
        transformedInputs,
        tempTransformedOutputs,
        response.feeRate,
        wallet.blockchain,
      );

      console.log('Calculated fee with 10% buffer:', calculatedFee);

      // Calculate total input amount
      const totalInputAmount = transformedInputs.reduce(
        (sum, input) => sum + input.satoshis,
        0,
      );

      // Transform outputs with locally calculated fee for change output
      const transformedOutputs = response.outputs.map((output: any, index: number) => {
        console.log('output >>>', output);

        // For the change output (last output), use locally calculated fee
        if (index === response.outputs.length - 1) {
          // Change amount = total inputs - recipient amount - calculated fee
          const recipientAmount = parseInt(response.outputs[0].amount);
          const changeAmount = totalInputAmount - recipientAmount - calculatedFee;

          console.log('Calculating change output:', {
            totalInputAmount,
            recipientAmount,
            calculatedFee,
            changeAmount,
            originalChangeAmount: parseInt(output.amount),
          });

          if (changeAmount <= 0) {
            throw new Error('Insufficient funds to cover fee');
          }

          return {
            address: output.address,
            satoshis: changeAmount,
            amount: changeAmount,
          };
        }

        // For recipient output, use the original amount
        return {
          address: output.address,
          satoshis: parseInt(output.amount),
          amount: parseInt(output.amount),
        };
      });

      // Verify fee calculation
      const totalOutputAmount = transformedOutputs.reduce((sum, output) => sum + output.satoshis, 0);
      const actualFee = totalInputAmount - totalOutputAmount;
      
      console.log('Fee verification:', {
        totalInputAmount,
        totalOutputAmount,
        actualFee,
        expectedFee: calculatedFee,
        difference: actualFee - calculatedFee,
      });

      if (Math.abs(actualFee - calculatedFee) > 1) { // Allow 1 satoshi difference for rounding
        throw new Error(`Fee mismatch: actual ${actualFee} vs expected ${calculatedFee}`);
      }

      const returningData = {  
        data: {
          inputs: transformedInputs,
          outputs: transformedOutputs,
          fee: calculatedFee,
          feeRate: response.feeRate,
        },
        // Store original response for signing later
        _originalResponse: response,
      }

      console.log('returningData >>>', returningData);

      // 5. Return in the expected format for the existing flow
      return {  
        data: {
          inputs: transformedInputs,
          outputs: transformedOutputs,
          fee: calculatedFee,
          feeRate: response.feeRate,
        },
        // Store original response for signing later
        _originalResponse: response,
      };
    } catch (error) {
      console.error('Error in prepareUTXOTransaction:', error);
      throw error;
    }
  }
  
  async signAndBroadcastBchTx(
    preparedTxData: any,
    wallet: AuthUserWallet,
  ): Promise<any> {
    const tx = preparedTxData.data;
  
    // 1️⃣  Build the bitcore UTXO & key lists
    const utxos  = tx.inputs.map(toBitcoreUtxo);

    const masterSeed = bip39.mnemonicToSeedSync(wallet.mnemonic);
    const root       = HDKey.fromMasterSeed(masterSeed);
    
    const keys = tx.inputs.map((i: any) => {
      const child = root.derive(i.fullPath);
    
      // 1️⃣  Build a BN from the raw private-key buffer
      const bn = bitcore.crypto.BN.fromBuffer(child.privateKey);
    
      // 2️⃣  Construct the PrivateKey object with `compressed: true`
      return new bitcore.PrivateKey({
        bn,
        network: bitcore.Networks.livenet,   // or Networks.mainnet in older versions
        compressed: true                     // <--- critical!
      });
    });
  
    const toCashAddr = (addr: string) =>
      bitcore.Address.fromString(addr).toCashAddress();
    
    const recipient  = toCashAddr(tx.outputs[0].address);
    const changeAddr = toCashAddr(tx.outputs[1].address);

    console.log('recipient >>>', recipient);
    console.log('changeAddr >>>', changeAddr);
    console.log(tx.fee)


    console.log('utxos >>>', utxos);
    console.log('keys >>>', keys);
    
    console.log('tx >>>', tx);

    const bitcoreTx = new bitcore.Transaction()
    .from(utxos)                                    // → add all inputs
    .to(recipient, tx.outputs[0].satoshis)    // → recipient
    .change(changeAddr)                     // → change

    // estimate size before signing
    const vBytes = bitcoreTx._estimateSize();   // bitcore-lib-cash helper
    const fee    = Math.ceil(vBytes * tx.feeRate); // feeRate you already have
    bitcoreTx.fee(fee);

    bitcoreTx.sign(keys);

    const txHex = bitcoreTx.serialize(true);   // `true` = validate each input

    console.log('txHex >>>', txHex);
    // 3️⃣  Broadcast the raw hex
    return WalletBalanceInstance.post('/broadcast/bitcoin-cash', { txhex: txHex });
  }


  async signAndBroadcastUTXOTransaction(
    preparedTxData: any,
    wallet: AuthUserWallet,
  ): Promise<any> {
    try {
      // 1. Use the original response stored in prepared data
      const response = preparedTxData._originalResponse;

      if (!response || !response.inputs || !response.outputs) {
        throw new Error('Invalid prepared transaction data');
      }

      const netParams =
        wallet.blockchain === 'bitcoin'
          ? bitcoin.networks.bitcoin
          : wallet.blockchain === 'bitcoin-cash'
          ? BTC_CASH_NETWORK
          : wallet.blockchain === 'litecoin'
          ? LTC_NETWORK
          : wallet.blockchain === 'dogecoin'
          ? DOGE_NETWORK
          : null;

      if (!netParams) {
        throw new Error('Invalid network parameters');
      }

      // 2. Create HD wallet from mnemonic
      let hdWallet: any;
      try {
        console.log('Creating HD wallet from mnemonic for signing...');
        const seed = bip39.mnemonicToSeedSync(wallet.mnemonic);
        hdWallet = HDKey.fromMasterSeed(seed);
        console.log('HD wallet created successfully for signing');
      } catch (error) {
        console.error('Error creating HD wallet from mnemonic:', error);
        throw new Error('Failed to create HD wallet from mnemonic');
      }

      // 3. Create transaction builder
      const psbt = new bitcoin.Psbt({network: netParams});

      // 4. Process inputs and collect signing keys
      const signingKeys: ECPairInterface[] = [];

      for (let i = 0; i < response.inputs.length; i++) {
        const input = response.inputs[i];
        console.log(`Processing input ${i} for signing:`, input);

        try {
          // 5. Parse and derive the private key for this input
          const fullPath = input.path;
          console.log('Full derivation path for signing:', fullPath);

          // 6. Derive the key using the full path
          const derivedKey = hdWallet.derive(fullPath);
          const keyPair = ECPair.fromPrivateKey(derivedKey.privateKey, {
            network: netParams,
          });

          signingKeys.push(keyPair);

          const psbtIn: any = {
            hash: input.txid,
            index: input.vout,
          };

          // 7. Add input to PSBT
          if (input.isSegwit) {
            psbtIn.witnessUtxo = {
              script: Buffer.from(input.scriptPubKey, 'hex'),
              value: parseInt(input.value),
            };
          } else {
            if (!input.rawTx) {
              throw new Error('Raw transaction is required for non-segwit inputs');
            }

            psbtIn.nonWitnessUtxo = Buffer.from(input.rawTx, 'hex');
          }

          psbt.addInput(psbtIn);

          console.log(`Input ${i} added successfully for signing`);
        } catch (error) {
          console.error(`Error processing input ${i} for signing:`, error);
          throw new Error(`Failed to process input ${i} for signing: ${error.message}`);
        }
      }

      // 8. Add outputs
      for (let i = 0; i < response.outputs.length; i++) {
        const output = response.outputs[i];
        console.log(`Adding output ${i} for signing:`, output);

        psbt.addOutput({
          address: output.address,
          value: parseInt(output.amount.toString()),
        });
      }

      // 9. Sign all inputs
      for (let i = 0; i < signingKeys.length; i++) {
        const keyPair = signingKeys[i];
        console.log(`Signing input ${i}...`);

        try {
          if (!keyPair) {
            throw new Error(`No signing key available for input ${i}`);
          }

          // 10. Create a Buffer-compatible signer for PSBT
          const signer = {
            publicKey: Buffer.from(keyPair.publicKey),
            sign: (hash: Buffer) => Buffer.from(keyPair.sign(hash)),
          };

          psbt.signInput(i, signer);
          console.log(`Input ${i} signed successfully`);
        } catch (error) {
          console.error(`Error signing input ${i}:`, error);
          throw new Error(`Failed to sign input ${i}: ${error.message}`);
        }
      }

      // 11. Validate signatures
      console.log('Validating signatures...');
      const validationResult = psbt.validateSignaturesOfAllInputs(
        (pubkey: Buffer, msghash: Buffer, signature: Buffer): boolean => {
          return ECPair.fromPublicKey(pubkey, {network: netParams}).verify(
            msghash,
            signature,
          );
        },
      );

      if (!validationResult) {
        throw new Error('Signature validation failed');
      }

      console.log('All signatures validated successfully');

      // 12. Finalize and extract transaction
      console.log('Finalizing transaction...');
      psbt.finalizeAllInputs();
      const transaction = psbt.extractTransaction();

      console.log('transaction >>>>', transaction);

      const txHex = transaction.toHex();
      const txId = transaction.getId();

      console.log('Transaction signed successfully:', {txHex, txId});

      // 13. Broadcast the transaction
      console.log('Broadcasting UTXO transaction...');
      return await WalletBalanceInstance.post(
        `/broadcast/${wallet.blockchain}`,
        {
          txhex: txHex,
        },
      );
    } catch (error) {
      console.error('Error in signAndBroadcastUTXOTransaction:', error);
      throw error;
    }
  }

  /* ============================================================================================== */
  /*                                         PRIVATE METHODS                                        */
  /* ============================================================================================== */

  private calculateUTXOTransactionFee(
    inputs: any[],
    outputs: any[],
    feeRatePerByte: number,
    blockchain: string,
  ): number {
    try {
      let baseSize = 10;  // version(4) + locktime(4) + var-ints(2)
      let hasWitness = false;

      const SIZES = {
        input: {
          P2PKH: 148,   // standard non-segwit input
          P2WPKH: 68,   // native segwit input
        },
        output: {
          P2PKH: 34,    // standard non-segwit output
          P2WPKH: 31,   // native segwit output
        },
      };

      // Determine input size
      for (const input of inputs) {
        // BCH always uses P2PKH
        if (blockchain === 'bitcoin-cash') {
          baseSize += SIZES.input.P2PKH;
        }
        // For other chains, check segwit status
        else {
          let type = 'P2PKH';
          if (['bitcoin', 'litecoin'].includes(blockchain)) {
            if (input.isSegwit || (input.path && input.path.includes("84'"))) {
              type = 'P2WPKH';
              hasWitness = true;
            }
          }
          baseSize += SIZES.input[type];
        }
      }

      // Determine output size
      for (const _ of outputs) {
        // BCH always uses P2PKH outputs
        if (blockchain === 'bitcoin-cash') {
          baseSize += SIZES.output.P2PKH;
        }
        // For other chains, use appropriate format
        else if (['bitcoin', 'litecoin'].includes(blockchain)) {
          baseSize += SIZES.output.P2WPKH;
        } else {
          baseSize += SIZES.output.P2PKH;
        }
      }

      // SegWit adds 2 extra bytes to the base tx (not applicable for BCH)
      if (hasWitness) {
        baseSize += 2;
      }

      console.log(`Calculated transaction size for ${blockchain}: ${baseSize} bytes`);

      const baseFee = Math.ceil(baseSize * feeRatePerByte);
      const feeWithBuffer = Math.ceil(baseFee * 1.1); // 10% safety buffer

      console.log(`Base fee: ${baseFee} sat, Fee with 10% buffer: ${feeWithBuffer} sat`);

      return feeWithBuffer;
    } catch (error) {
      console.error('Error calculating UTXO transaction fee:', error);
      throw error;
    }
  }
}
