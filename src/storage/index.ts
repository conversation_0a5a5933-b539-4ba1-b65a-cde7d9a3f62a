import {MMKV} from 'react-native-mmkv';
import {Storage} from 'redux-persist';

export const storage = new MMKV();

export const reduxStorage: Storage = {
  setItem: (key, value): Promise<boolean> => {
    storage.set(key, value);
    return Promise.resolve(true);
  },
  getItem: (key): Promise<string | undefined> => {
    const value = storage.getString(key);
    return Promise.resolve(value);
  },
  removeItem: (key): Promise<void> => {
    storage.delete(key);
    return Promise.resolve();
  },
};
