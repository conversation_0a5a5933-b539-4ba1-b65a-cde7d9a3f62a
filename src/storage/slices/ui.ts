import {BottomSheetListData} from '@/components/BottomSheetList';
import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import type {Sheet, SheetsState, SheetType} from '../types';
import type {RootState} from '../store/storeConfig';

const initialState: SheetsState = {
  list: {
    isOpen: false,
    data: null,
    snapPoints: null,
    enableSearch: false,
  },
  wallet: {
    isOpen: false,
    data: null,
    snapPoints: null,
    enableSearch: false,
  },
  lightningHistory: {
    isOpen: false,
    data: null,
    snapPoints: null,
    enableSearch: false,
  },
  lightningSend: {
    isOpen: false,
    data: null,
    snapPoints: null,
    enableSearch: false,
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    openSheet: (
      state,
      action: PayloadAction<{
        type: SheetType;
        data: BottomSheetListData;
        snapPoints: number | number[];
        enableSearch: boolean;
      }>,
    ) => {
      state[action.payload.type] = {
        isOpen: true,
        data: action.payload.data,
        snapPoints: action.payload.snapPoints,
        enableSearch: action.payload.enableSearch,
      };
    },
    closeSheet: (state, action: PayloadAction<SheetType>) => {
      state[action.payload] = {
        isOpen: false,
        data: null,
        snapPoints: null,
        enableSearch: false,
      };
    },
    resetAllSheets: (state) => {
      Object.keys(state).forEach((key) => {
        state[key] = {
          isOpen: false,
          data: null,
          snapPoints: null,
          enableSearch: false,
        };
      });
    },
  },
});

export const selectSheet = (state: RootState, type: SheetType): Sheet =>
  state.ui[type] || initialState[type];

export const {openSheet, closeSheet, resetAllSheets} = uiSlice.actions;
export default uiSlice.reducer;
