import {
  AuthAddresses,
  AuthAssets,
  AuthCalculatedPrices,
  AuthStableCoins,
  AuthTokenPrices,
  AuthUser,
  AuthWalletTxs,
} from '@/types/authTypes';

export const setIsLoggedIn = () => {
  return {
    type: 'SET_IS_LOGGED_IN',
  };
};

export const setUser = (user: AuthUser) => {
  return {
    type: 'SET_USER',
    payload: user,
  };
};

export const logout = () => {
  return {
    type: 'LOGOUT',
  };
};

export const setUserAddresses = (addresses: AuthAddresses) => {
  return {
    type: 'SET_USER_ADDRESSES',
    payload: addresses,
  };
};

export const setWalletTxs = (txs: AuthWalletTxs) => {
  return {
    type: 'SET_WALLET_TXS',
    payload: txs,
  };
};

export const setReduxCalculatedPrices = (prices: AuthCalculatedPrices) => {
  return {
    type: 'SET_CALCULATED_PRICES',
    payload: prices,
  };
};

export const setReduxTokenPrices = (prices: AuthTokenPrices) => {
  return {
    type: 'SET_TOKEN_PRICES',
    payload: prices,
  };
};

export const setReduxAssets = (assets: AuthAssets) => {
  return {
    type: 'SET_ASSETS',
    payload: assets,
  };
};

export const setReduxStableCoins = (coins: AuthStableCoins) => {
  return {
    type: 'SET_STABLE_COINS',
    payload: coins,
  };
};

export const setWalletBalance = (balance: string) => {
  return {
    type: 'SET_WALLET_BALANCE',
    payload: balance,
  };
};

export const setAvalancheFixed = () => {
  return {
    type: 'SET_AVALANCHE_FIXED',
  };
};

export const setIsAuthenticated = (isAuthenticated: boolean) => {
  return {
    type: 'SET_IS_AUTHENTICATED',
    payload: isAuthenticated,
  };
};

export const setShowAuthScreen = (showAuthScreen: boolean) => {
  return {
    type: 'SET_SHOW_AUTH_SCREEN',
    payload: showAuthScreen,
  };
};

export const setBiometricsInProgress = (biometricsInProgress: boolean) => {
  return {
    type: 'SET_BIOMETRICS_IN_PROGRESS',
    payload: biometricsInProgress,
  };
};
