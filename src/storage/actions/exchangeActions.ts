export const setPayinAmount = (payinAmount: string) => ({
  type: 'SET_PAYIN_AMOUNT',
  payload: payinAmount,
});

export const setPayoutAmount = (payoutAmount: string) => ({
  type: 'SET_PAYOUT_AMOUNT',
  payload: payoutAmount,
});

export const setRate = (rate: string) => ({
  type: 'SET_RATE',
  payload: rate,
});

export const setRateID = (rateID: string) => ({
  type: 'SET_RATE_ID',
  payload: rateID,
});

export const setTimeLeft = (timeLeft: number) => ({
  type: 'SET_TIME_LEFT',
  payload: timeLeft,
});

export const setExpiryTimestamp = (timestamp: number) => ({
  type: 'SET_EXPIRY_TIMESTAMP',
  payload: timestamp,
});

export const setPaused = (isPaused: boolean) => ({
  type: 'SET_PAUSED',
  payload: isPaused,
});
