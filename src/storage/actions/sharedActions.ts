export const changeLanguage = (language: string) => {
  return {
    type: 'CHANGE_LANGUAGE',
    payload: language,
  };
};

export const changeCurrency = (currency: string) => {
  return {
    type: 'CHANGE_CURRENCY',
    payload: currency,
  };
};

export const setSeedPhraseConfirmed = (seedPhraseConfirmed: number) => {
  return {
    type: 'SET_SEED_PHRASE_CONFIRMED',
    payload: seedPhraseConfirmed,
  };
};

export const setOnboardingSavedToICloud = (onboardingSavedToICloud: boolean) => {
  return {
    type: 'SET_ONBOARDING_SAVED_TO_ICLOUD',
    payload: onboardingSavedToICloud,
  };
};

export const setWalletIdentifier = (walletIdentifier: string | null) => {
  return {
    type: 'SET_WALLET_IDENTIFIER',
    payload: walletIdentifier,
  };
};

export const setSeedPhraseVerified = (seedPhraseVerified: boolean) => {
  return {
    type: 'SET_SEED_PHRASE_VERIFIED',
    payload: seedPhraseVerified,
  };
};
