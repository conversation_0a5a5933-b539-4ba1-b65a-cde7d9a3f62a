export const incrementModalCount = () => ({ type: 'INCREMENT_MODAL_COUNT' });

export const setHideModalPermanently = (hide: boolean) => ({
  type: 'SET_HIDE_MODAL_PERMANENTLY',
  payload: hide,
});

export const setWorkingDir = (workingDir: string) => ({
  type: 'SET_WORKING_DIR',
  payload: workingDir,
});

export const setWebSocket = (webSocket: WebSocket | null) => ({
  type: 'SET_WEB_SOCKET',
  payload: webSocket,
});

export const setBalanceMsat = (balanceMsat: number) => ({
  type: 'SET_BALANCE_MSAT',
  payload: balanceMsat,
});

export const setRefundablesLength = (setRefundablesLength: number) => ({
  type: 'SET_REFUNDABLES_LENGTH',
  payload: setRefundablesLength,
});
