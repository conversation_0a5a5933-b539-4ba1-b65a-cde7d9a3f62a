const INITIAL_STATE = {
  payinAmount: 0,
  payoutAmount: 0,
  rate: 0,
  rateID: '',
  timeLeft: 0,
  isPaused: false,
  expiryTimestamp: 0,
};

export default function exchangeReducer(state = INITIAL_STATE, action: any) {
  switch (action.type) {
    case 'SET_PAYIN_AMOUNT':
      return {
        ...state,
        payinAmount: action.payload,
      };
    case 'SET_RATE':
      return {
        ...state,
        rate: action.payload,
      };
    case 'SET_RATE_ID':
      return {
        ...state,
        rateID: action.payload,
      };
    case 'SET_PAYOUT_AMOUNT':
      return {
        ...state,
        payoutAmount: action.payload,
      };
    case 'SET_TIME_LEFT':
      return {
        ...state,
        timeLeft: action.payload,
      };
    case 'SET_EXPIRY_TIMESTAMP':
      return {
        ...state,
        expiryTimestamp: action.payload,
      };
    case 'SET_PAUSED':
      return {
        ...state,
        isPaused: action.payload,
      };
    default:
      return state;
  }
}
