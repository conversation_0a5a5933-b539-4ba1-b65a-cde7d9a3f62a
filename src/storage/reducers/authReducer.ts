const INITIAL_STATE = {
  isLoggedIn: false,
  user: null,
  userAddresses: [],
  walletTxs: [],
  calculatedPrices: [],
  tokenPrices: [],
  assets: [],
  stableCoins: [],
  walletBalance: 0,
  avalancheFixed: false,
  isAuthenticated: false,
  showAuthScreen: true,
  biometricsInProgress: false,
};

export default (state = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case 'SET_IS_LOGGED_IN':
      return {
        ...state,
        isLoggedIn: true,
      };
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
      };
    case 'LOGOUT':
      const logoutObject = {
        ...state,
        isLoggedIn: false,
        user: null,
        userAddresses: [],
        walletTxs: [],
        calculatedPrices: [],
        tokenPrices: [],
        assets: [],
        stableCoins: [],
        walletBalance: 0,
      };

      // Fix old state (1.2.0)
      delete (logoutObject as any).userAddress;

      return logoutObject;
    case 'SET_USER_ADDRESSES':
      return {
        ...state,
        userAddresses: action.payload,
      };
    case 'SET_WALLET_TXS':
      return {
        ...state,
        walletTxs: action.payload,
      };
    case 'SET_CALCULATED_PRICES':
      return {
        ...state,
        calculatedPrices: action.payload,
      };
    case 'SET_TOKEN_PRICES':
      return {
        ...state,
        tokenPrices: action.payload,
      };
    case 'SET_ASSETS':
      return {
        ...state,
        assets: action.payload,
      };
    case 'SET_STABLE_COINS':
      return {
        ...state,
        stableCoins: action.payload,
      };
    case 'SET_WALLET_BALANCE':
      return {
        ...state,
        walletBalance: action.payload,
      };
    case 'SET_AVALANCHE_FIXED':
      return {
        ...state,
        avalancheFixed: true,
      };
    case 'SET_IS_AUTHENTICATED':
      return {
        ...state,
        isAuthenticated: action.payload,
      };
    case 'SET_SHOW_AUTH_SCREEN':
      return {
        ...state,
        showAuthScreen: action.payload,
      };
    case 'SET_BIOMETRICS_IN_PROGRESS':
      return {
        ...state,
        biometricsInProgress: action.payload,
      };
    default:
      return state;
  }
};
