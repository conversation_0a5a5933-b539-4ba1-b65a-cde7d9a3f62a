const initialState = {
  loanData: {
    borrowAmount: '',
    borrowCurrency: 'USD',
    collateralAmount: '',
    collateralCurrency: 'BTC',
    loanToValue: '50%',
    interestPayment: 'Monthly',
    term: '12 months',
    id: '',
  },
  accessToken: null,
  refreshToken: null,
  isRegistered: false,
  tokenTimestamp: null,
  hasLoan: false,
  mpcComputedAddress: null,
  activeLoans: {},
};

const loanReducer = (state = initialState, action) => {
  switch (action.type) {
    case 'SET_LOAN_DATA':
      return {
        ...state,
        loanData: action.payload,
      };

    case 'SET_ACCESS_TOKEN':
      if (action.payload === null) {
        return {
          ...state,
          accessToken: null,
          tokenTimestamp: null,
        };
      }
      return {
        ...state,
        accessToken: action.payload,
      };

    case 'SET_REFRESH_TOKEN':
      if (action.payload === null) {
        return {
          ...state,
          refreshToken: null,
          tokenTimestamp: null,
        };
      }
      return {
        ...state,
        refreshToken: action.payload,
      };

    case 'SET_TOKEN_TIMESTAMP':
      return {
        ...state,
        tokenTimestamp: action.payload,
      };

    case 'SET_IS_REGISTERED':
      return {
        ...state,
        isRegistered: action.payload,
      };

    case 'SET_HAS_LOAN':
      return {
        ...state,
        hasLoan: action.payload,
      };

    case 'SET_MPC_COMPUTED_ADDRESS':
      return {
        ...state,
        mpcComputedAddress: action.payload,
      };

    case 'ADD_LOAN':
      return {
        ...state,
        activeLoans: {
          ...state.activeLoans,
          [action.payload.loanID]: action.payload.party,
        },
      };

    case 'REMOVE_LOAN':
      const activeLoans = {...state.activeLoans};
      delete activeLoans[action.payload.loanID];
      return {
        ...state,
        activeLoans,
      };

    default:
      return state;
  }
};

export default loanReducer;
