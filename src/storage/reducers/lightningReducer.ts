const INITIAL_STATE = {
  workingDir: null,
  modalCount: 0,
  hideModalPermanently: false,
  webSocket: null,
  balanceMsat: 0,
  refundablesLength: 0,
};

export default (state = INITIAL_STATE, action: any) => {
  switch (action.type) {
    case 'SET_WORKING_DIR':
      return {
        ...state,
        workingDir: action.payload,
      };
    case 'INCREMENT_MODAL_COUNT':
      return {
        ...state,
        modalCount: state.modalCount + 1,
      };
    case 'SET_HIDE_MODAL_PERMANENTLY':
      return {
        ...state,
        hideModalPermanently: action.payload,
      };
    case 'SET_WEB_SOCKET':
      return {
        ...state,
        webSocket: action.payload,
      };
    case 'SET_BALANCE_MSAT':
      return {
        ...state,
        balanceMsat: action.payload,
      };
    case 'SET_REFUNDABLES_LENGTH':
      return {
        ...state,
        refundablesLength: action.payload,
      };
    default:
      return state;
  }
};
