import type {BottomSheetListData} from '@/components/BottomSheetList';

export type SheetType =
  | 'list'
  | 'wallet'
  | 'lightningHistory'
  | 'lightningSend'
  | 'keychainBackup'
  | 'onboarding'
  | 'kycIndustry'
  | 'kycSector'
  | 'kycCountry';

export type Sheet = {
  isOpen: boolean;
  data: BottomSheetListData | null;
  snapPoints: number | number[] | null;
  enableSearch?: boolean;
};

export type SheetsState = {
  [key in SheetType]: Sheet;
};
