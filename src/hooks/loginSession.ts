import {useCallback, useEffect, useRef} from 'react';
import {AppState} from 'react-native';

import {setAccessToken, setRefreshToken} from '@/storage/actions/loanActions';
import {useAppDispatch, useAppSelector} from './redux';

const SESSION_DURATION = 30 * 60 * 1000; // 30 minutes

export const useTokenExpiration = () => {
  const dispatch = useAppDispatch();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const {accessToken, refreshToken, tokenTimestamp} = useAppSelector(
    (state) => state.loan,
  );

  const clearTokens = useCallback(() => {
    dispatch(setAccessToken(null));
    dispatch(setRefreshToken(null));
    console.log('[useTokenExpiration] ⛔️ Tokens expired! ⛔️');
  }, [dispatch, accessToken, refreshToken, tokenTimestamp]);

  const checkSessionValidity = useCallback(() => {
    if (tokenTimestamp && accessToken && refreshToken) {
      const now = Date.now();
      const sessionAge = now - tokenTimestamp;
      const isExpired = sessionAge >= SESSION_DURATION;

      if (isExpired) {
        clearTokens();
      }
    }
  }, [tokenTimestamp, accessToken, refreshToken, clearTokens]);

  useEffect(() => {
    if (accessToken && refreshToken) {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      timerRef.current = setTimeout(() => {
        clearTokens();
      }, SESSION_DURATION);

      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      };
    }
  }, [accessToken, refreshToken, tokenTimestamp, dispatch, clearTokens]);

  // Check session validity when app becomes active
  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        checkSessionValidity();
      }
    });

    checkSessionValidity();

    return () => {
      subscription.remove();
    };
  }, [checkSessionValidity]);
};
