import {useEffect, useState, RefObject, useCallback} from 'react';
import {KeyboardEvent, Platform, Keyboard as RNKeyboard, TextInput} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';

const useKeyboard = (): {
  keyboardShown: boolean;
  keyboardHeight: number;
} => {
  const [keyboardShown, setKeyboardShown] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardWillShowListener = RNKeyboard.addListener(
      // ios has `keyboardWillShow`, android doesn't
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        setKeyboardShown(true);
      },
    );
    const keyboardDidShowListener = RNKeyboard.addListener(
      'keyboardDidShow',
      (event: KeyboardEvent) => {
        setKeyboardHeight(event.endCoordinates.height);
      },
    );
    const keyboardDidHideListener = RNKeyboard.addListener(
      // ios has `keyboardWillHide`, android doesn't
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setKeyboardShown(false);
        setKeyboardHeight(0);
      },
    );

    return (): void => {
      keyboardWillShowListener.remove();
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  return {
    keyboardShown,
    keyboardHeight,
  };
};

export default useKeyboard;

/**
 * Hook to focus an input when a screen comes into focus.
 */
export const useFocusInput = (inputRef: RefObject<TextInput>, delayMs = 200): void => {
  const focusInput = useCallback(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, delayMs);
  }, [inputRef, delayMs]);

  useFocusEffect(
    useCallback(() => {
      focusInput();
      return () => {};
    }, [focusInput]),
  );
};

export const Keyboard = ((): {
  dismiss: () => Promise<void>;
} => {
  let keyboardShown = false;

  RNKeyboard.addListener('keyboardDidShow', () => {
    keyboardShown = true;
  });

  // Keyboard.dismiss() that can be awaited
  const dismiss = (): Promise<void> => {
    return new Promise((p) => {
      if (keyboardShown) {
        RNKeyboard.dismiss();
      } else {
        p();
      }
    });
  };

  return {
    dismiss,
  };
})();
