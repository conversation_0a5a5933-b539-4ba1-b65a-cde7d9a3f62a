import {closeSheet, openSheet, selectSheet} from '@/storage/slices/ui';
import {useMemo} from 'react';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';

import {BottomSheetListData} from '@/components/BottomSheetList';
import type {SheetType} from '@/storage/types';
import {moderateScale, SCREEN_HEIGHT} from '@/styles/themes';
import {useAppDispatch, useAppSelector} from './redux';

export const useBottomSheet = (type: SheetType) => {
  const dispatch = useAppDispatch();
  const sheet = useAppSelector((state) => selectSheet(state, type));

  if (!sheet) {
    console.warn(`Sheet type "${type}" not found in state`);
    return {
      isOpen: false,
      data: null,
      open: () => {},
      close: () => {},
      snapPoints: 0,
      enableSearch: false,
    };
  }

  return {
    isOpen: sheet.isOpen,
    data: sheet.data,
    open: (
      data: BottomSheetListData,
      snapPoints: number | number[],
      enableSearch: boolean,
    ) =>
      dispatch(
        openSheet({
          type,
          data,
          snapPoints,
          enableSearch,
        }),
      ),
    close: () => dispatch(closeSheet(type)),
    snapPoints: sheet.snapPoints,
    enableSearch: sheet.enableSearch ?? false,
  };
};

export type SnapPointSize = 'sm' | 'md' | 'lg';

const getFixedHeight = (size: SnapPointSize): number => {
  // Adjust heights based on screen height
  const isSmallDevice = SCREEN_HEIGHT < 700;

  const heights = {
    sm: isSmallDevice ? 45 : 50,
    md: isSmallDevice ? 65 : 70,
    lg: isSmallDevice ? 85 : 90,
  };

  return heights[size];
};

export const useSnapPoints = (
  percentage: number,
  maxHeightPercentage?: number,
): number[] => {
  const {height} = useSafeAreaFrame();
  const insets = useSafeAreaInsets();

  return useMemo(() => {
    // Calculate base maxHeight in pixels
    const baseMaxHeight = height - (insets.top + 30);

    // Calculate initial snap point as a percentage of the maxHeight
    const initialSnapPoint = (percentage * baseMaxHeight) / 100;

    // Calculate final maxHeight based on maxHeightPercentage if provided
    const maxHeight = maxHeightPercentage
      ? (maxHeightPercentage * baseMaxHeight) / 100
      : initialSnapPoint;

    return [initialSnapPoint, maxHeight];
  }, [height, insets.top, percentage, maxHeightPercentage]);
};

export const useSnapPointsTwo = (size: SnapPointSize = 'md'): number[] => {
  const {height} = useSafeAreaFrame();
  const insets = useSafeAreaInsets();

  return useMemo(() => {
    // Calculate base maxHeight in pixels
    const baseMaxHeight = height - (insets.top + moderateScale(50));

    // Get fixed height percentage based on size
    const heightPercentage = getFixedHeight(size);

    // Calculate fixed height
    const fixedHeight = (heightPercentage * baseMaxHeight) / 100;

    // Return the same value twice since bottom sheet expects an array
    // but we want it to stay at a fixed height
    return [fixedHeight, fixedHeight];
  }, [height, insets.top, size]);
};
