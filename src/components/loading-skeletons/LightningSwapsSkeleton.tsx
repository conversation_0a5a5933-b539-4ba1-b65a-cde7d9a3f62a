import React from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useScreenSize} from '@/hooks/screen';
import {useSafeAreaFrame} from 'react-native-safe-area-context';

export const LightningSwapsSkeleton = () => {
  const {isSmallScreen} = useScreenSize();
  const {width, height} = useSafeAreaFrame();

  const contentWidth = width - 32;

  const qrSize = 300;
  const qrBottomOffset = isSmallScreen ? 200 : 240;
  const qrYPosition = height - qrSize - qrBottomOffset;
  const sliderYPosition = height * 0.05;

  return (
    <View style={styles.container}>
      <SkeletonPlaceholder
        speed={1500}
        backgroundColor="#E5E7EB"
        highlightColor="#F3F4F6"
      >
        <View>
          {/* Slider */}
          <View
            style={{
              position: 'absolute',
              left: (contentWidth - contentWidth / 2) / 2,
              top: sliderYPosition,
              width: contentWidth / 2,
              height: 60,
              borderRadius: 20,
            }}
          />

          {/* QR Code Box */}
          <View
            style={{
              position: 'absolute',
              left: (contentWidth - qrSize) / 2,
              top: qrYPosition,
              width: qrSize,
              height: qrSize,
              borderRadius: 8,
            }}
          />
        </View>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.gray.gray300,
    padding: 16,
  },
});
