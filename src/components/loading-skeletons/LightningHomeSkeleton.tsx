import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';
import {useSafeAreaFrame} from 'react-native-safe-area-context';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import GlobalStyles from '@/constants/GlobalStyles';
import theme from '@/styles/themes';

export const LightningHomeSkeleton = () => {
  const {width, height} = useSafeAreaFrame();

  const contentWidth = width - 40;
  const contentHeight = theme.isSmallDevice ? height - 150 : height - 90;
  const buttonsYPosition = theme.isSmallDevice ? height - 260 : height - 320;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <SkeletonPlaceholder
          speed={1500}
          backgroundColor="#E5E7EB"
          highlightColor="#F3F4F6"
        >
          <SkeletonPlaceholder.Item>
            {/* Balance */}
            <SkeletonPlaceholder.Item
              width={contentWidth * 0.6}
              height={80}
              borderRadius={16}
              alignSelf="center"
              marginTop={contentHeight * 0.15}
            />

            {/* History Item */}
            <SkeletonPlaceholder.Item
              flexDirection="row"
              alignItems="center"
              marginTop={contentHeight * 0.1}
            >
              <SkeletonPlaceholder.Item
                width={24}
                height={24}
                borderRadius={12}
                marginLeft={contentWidth * 0.35 - 12}
              />
              <SkeletonPlaceholder.Item
                width={contentWidth * 0.25}
                height={24}
                borderRadius={12}
                marginLeft={20}
              />
            </SkeletonPlaceholder.Item>

            {/* Refunds Item */}
            <SkeletonPlaceholder.Item
              flexDirection="row"
              alignItems="center"
              marginTop={40}
            >
              <SkeletonPlaceholder.Item
                width={24}
                height={24}
                borderRadius={12}
                marginLeft={contentWidth * 0.35 - 12}
              />
              <SkeletonPlaceholder.Item
                width={contentWidth * 0.25}
                height={24}
                borderRadius={12}
                marginLeft={20}
              />
            </SkeletonPlaceholder.Item>

            {/* Buttons */}
            <SkeletonPlaceholder.Item
              width={contentWidth}
              height={100}
              borderRadius={16}
              marginTop={buttonsYPosition - contentHeight * 0.45 - 64}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
});
