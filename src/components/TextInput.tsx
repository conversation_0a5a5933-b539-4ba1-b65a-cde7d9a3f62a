import React, {forwardRef} from 'react';
import {
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps,
  StyleSheet,
  Text,
  TextStyle,
  View,
  ViewStyle,
} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

type Props = Omit<RNTextInputProps, 'style'> & {
  label: string;
  error?: string;
  styles?: {
    container?: ViewStyle;
    inputContainer?: ViewStyle;
    label?: TextStyle;
    input?: ViewStyle;
  };
};

export const TextInput = forwardRef<RNTextInput, Props>(
  ({label, error, styles: customStyles, ...inputProps}, ref) => {
    return (
      <View style={[styles.container, customStyles?.container]}>
        <View style={[styles.inputContainer, customStyles?.inputContainer]}>
          <Text style={[styles.label, customStyles?.label]}>{label}</Text>
          <RNTextInput
            ref={ref}
            style={[styles.input, customStyles?.input]}
            keyboardType="default"
            placeholder="0"
            placeholderTextColor={GlobalStyles.gray.gray600}
            returnKeyType="done"
            autoCapitalize="none"
            autoComplete="off"
            autoCorrect={false}
            {...inputProps}
          />
        </View>

        {error && <Text style={styles.errorText}>{error}</Text>}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    minHeight: 56,
    backgroundColor: GlobalStyles.base.white,
    borderColor: '#E0E0E0',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 24,
    paddingBottom: 8,
    position: 'relative',
  },
  label: {
    position: 'absolute',
    top: 10,
    left: 12,
    color: GlobalStyles.gray.gray800,
    fontSize: 14,
    lineHeight: 16,
  },
  input: {
    padding: 0,
    margin: 0,
    color: GlobalStyles.base.black,
    fontSize: 16,
    fontWeight: '500',
    height: 38,
    lineHeight: 24,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 4,
    color: GlobalStyles.error.error300,
    fontSize: 12,
  },
});
