import React, {ReactElement, ReactNode} from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export const GradientView = ({
  colors,
  style,
  children,
}: {
  colors: string[];
  style?: StyleProp<ViewStyle>;
  children?: ReactNode;
}): ReactElement => (
  <View style={[styles.root, style]}>
    <LinearGradient
      colors={colors}
      start={{x: 0.5, y: 0}}
      end={{x: 0.5, y: 0.1}}
      style={StyleSheet.absoluteFill}
    />
    {children}
  </View>
);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    position: 'relative',
  },
});
