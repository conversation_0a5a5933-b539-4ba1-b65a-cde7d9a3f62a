import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View, ViewStyle} from 'react-native';

import DropdownSvg from '@/assets/icons/arrow down.svg';
import GlobalStyles from '@/constants/GlobalStyles';

const SelectOption = ({
  label,
  option,
  color = GlobalStyles.base.white,
  disabled = false,
  style,
  onPress,
  testID,
}: {
  label: string;
  option: string;
  color?: string;
  disabled?: boolean;
  style?: ViewStyle | ViewStyle[];
  onPress?: () => void;
  testID?: string;
}) => {
  const buttonStyles = [
    styles.container,
    {opacity: disabled ? 0.4 : 1, backgroundColor: color},
    style,
  ];

  return (
    <TouchableOpacity
      style={buttonStyles}
      disabled={disabled}
      onPress={onPress}
      activeOpacity={0.7}
      testID={testID}
    >
      <View style={styles.content}>
        <Text style={styles.label}>{label}</Text>

        <DropdownSvg height={25} width={25} style={styles.icon} />

        <Text style={styles.optionText}>{option}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default SelectOption;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 60,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
    borderRadius: 8,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
    position: 'relative',
    paddingHorizontal: 14,
    paddingVertical: 8,
  },
  label: {
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray800,
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 6,
  },
  optionText: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 1,
  },
  icon: {
    position: 'absolute',
    right: 14,
    top: 18,
  },
});
