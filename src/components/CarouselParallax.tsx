import React from 'react';
import {Dimensions, ImageSourcePropType, StyleSheet, View} from 'react-native';
import {useSharedValue} from 'react-native-reanimated';
import Carousel, {ICarouselInstance} from 'react-native-reanimated-carousel';

const window = Dimensions.get('window');
const PAGE_WIDTH = window.width;

interface CarouselImage {
  image: ImageSourcePropType;
  link?: string;
  title: string;
  subtitle: string;
}

import {ImageStyle, StyleProp} from 'react-native';
import {CarouselRenderItem} from 'react-native-reanimated-carousel';
import {SlideItem} from './SlideItem';

interface Options {
  rounded?: boolean;
  style?: StyleProp<ImageStyle>;
  title?: string;
  subtitle?: string;
}

export const renderItem =
  ({
    rounded = false,
    style,
    title,
    subtitle,
  }: Options = {}): CarouselRenderItem<CarouselImage> =>
  ({item, index}: {item: CarouselImage; index: number}) =>
    (
      <SlideItem
        key={index}
        index={index}
        rounded={rounded}
        style={style}
        title={title || item.title}
        subtitle={subtitle || item.subtitle}
      />
    );

interface CardsProps {
  images: CarouselImage[];
  height?: number;
  aspectRatio?: number;
}

function CarouselParallax({images, height = PAGE_WIDTH * 0.8}: CardsProps) {
  const progress = useSharedValue<number>(0);
  const ref = React.useRef<ICarouselInstance>(null);

  return (
    <View style={styles.gradientContainer}>
      <Carousel
        ref={ref}
        data={images}
        width={PAGE_WIDTH}
        height={height}
        style={{
          width: PAGE_WIDTH,
        }}
        mode="parallax"
        modeConfig={{
          parallaxScrollingScale: 0.9,
          parallaxScrollingOffset: 50,
        }}
        renderItem={renderItem({rounded: true})}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  gradientContainer: {
    alignItems: 'center',
  },
});

export default CarouselParallax;
