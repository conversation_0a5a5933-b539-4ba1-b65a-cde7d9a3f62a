import GlobalStyles from '@/constants/GlobalStyles';
import React from 'react';
import {StyleSheet, TouchableOpacity, View, ViewStyle} from 'react-native';

const IconButton = ({
  children,
  color = GlobalStyles.primary.primary400,
  disabled = false,
  style,
  onPress,
  testID,
}: {
  children: React.ReactNode;
  color?: string;
  disabled?: boolean;
  style?: ViewStyle | ViewStyle[];
  onPress?: () => void;
  testID?: string;
}) => {
  const buttonStyles = [
    styles.container,
    {opacity: disabled ? 0.6 : 1, backgroundColor: color},
    style,
  ];

  return (
    <TouchableOpacity
      style={buttonStyles}
      disabled={disabled}
      onPress={onPress}
      activeOpacity={0.7}
      testID={testID}
    >
      <View>{children}</View>
    </TouchableOpacity>
  );
};

export default IconButton;

const styles = StyleSheet.create({
  container: {
    width: 48,
    height: 48,
    borderRadius: 9999,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
