import React, {useState} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  TextStyle,
  Platform,
} from 'react-native';
import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import GlobalStyles from '@/constants/GlobalStyles';

type Props = {
  label: string;
  value: Date | null;
  onChange: (date: Date | null) => void;
  error?: string;
  placeholder?: string;
  styles?: {
    container?: ViewStyle;
    inputContainer?: ViewStyle;
    label?: TextStyle;
    input?: ViewStyle;
  };
};

export const DatePickerInput: React.FC<Props> = ({
  label,
  value,
  onChange,
  error,
  placeholder = 'Select date',
  styles: customStyles,
}) => {
  const [showPicker, setShowPicker] = useState(false);

  const handleChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowPicker(false);
    }

    if (event.type === 'set' && selectedDate) {
      onChange(selectedDate);
    }
  };

  const formatDate = (date: Date | null): string => {
    if (!date) return '';
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  return (
    <View style={[styles.container, customStyles?.container]}>
      <TouchableOpacity
        style={[styles.inputContainer, customStyles?.inputContainer]}
        onPress={() => setShowPicker(true)}
        activeOpacity={0.7}
      >
        <Text style={[styles.label, customStyles?.label]}>{label}</Text>
        <Text style={[styles.input, customStyles?.input, !value && styles.placeholder]}>
          {value ? formatDate(value) : placeholder}
        </Text>
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      {showPicker && (
        <DateTimePicker
          value={value || new Date()}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleChange}
          maximumDate={new Date()}
          minimumDate={new Date(1900, 0, 1)}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    minHeight: 56,
    backgroundColor: GlobalStyles.base.white,
    borderColor: '#E0E0E0',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingTop: 24,
    paddingBottom: 8,
    position: 'relative',
  },
  label: {
    position: 'absolute',
    top: 10,
    left: 12,
    color: GlobalStyles.gray.gray800,
    fontSize: 14,
    lineHeight: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  input: {
    padding: 0,
    margin: 0,
    color: GlobalStyles.base.black,
    fontSize: 16,
    fontWeight: '500',
    height: 38,
    lineHeight: 24,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  placeholder: {
    color: GlobalStyles.gray.gray600,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 4,
    color: GlobalStyles.error.error300,
    fontSize: 12,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
});

export default DatePickerInput;
