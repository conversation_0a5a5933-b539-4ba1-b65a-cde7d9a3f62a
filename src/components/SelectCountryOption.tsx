import React from 'react';
import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import CountryFlag from 'react-native-country-flag';

import DropdownSvg from '@/assets/icons/arrow down.svg';
import GlobalStyles from '@/constants/GlobalStyles';

type SelectCountryOptionProps = {
  countryCode: string;
  countryName: string;
  label?: string;
  color?: string;
  disabled?: boolean;
  style?: ViewStyle | ViewStyle[];
  onPress?: () => void;
  testID?: string;
};

const SelectCountryOption = ({
  label = 'Select country',
  countryCode,
  countryName,
  color = GlobalStyles.base.white,
  disabled = false,
  style,
  onPress,
  testID,
}: SelectCountryOptionProps) => {
  const buttonStyles = [
    styles.container,
    {opacity: disabled ? 0.4 : 1, backgroundColor: color},
    style,
  ];

  return (
    <TouchableOpacity
      style={buttonStyles}
      disabled={disabled}
      onPress={onPress}
      activeOpacity={0.7}
      testID={testID}
    >
      <View style={styles.content}>
        <Text style={styles.label}>{label}</Text>

        <DropdownSvg height={25} width={25} style={styles.icon} />

        <View style={styles.optionContent}>
          <CountryFlag isoCode={countryCode} size={20} style={styles.flag} />
          <Text style={styles.optionText}>{countryName}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default SelectCountryOption;

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 70,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
    borderRadius: 8,
  },
  content: {
    flex: 1,
    flexDirection: 'column',
    position: 'relative',
    paddingHorizontal: 14,
    paddingVertical: 8,
  },
  label: {
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray800,
    fontSize: 13,
    fontWeight: '500',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    marginLeft: 2,
    paddingTop: Platform.OS === 'android' ? 2 : 14,
  },
  optionText: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 16,
    fontWeight: '500',
    letterSpacing: 1,
  },
  icon: {
    position: 'absolute',
    right: 14,
    top: 24,
  },
  flag: {
    borderRadius: 2,
  },
});
