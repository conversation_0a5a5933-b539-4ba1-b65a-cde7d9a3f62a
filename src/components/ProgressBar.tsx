import React, {memo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import CheckIcon from '@/assets/icons/check.svg';
import GlobalStyles from '@/constants/GlobalStyles';

type Step = {
  label: string;
  completed: boolean;
  current: boolean;
};

type ProgressBarProps = {
  steps: Step[];
};

const ProgressBar: React.FC<ProgressBarProps> = ({steps}) => {
  return (
    <View style={styles.container}>
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            {/* Step */}
            <View style={styles.stepColumn}>
              <View
                style={[
                  styles.circle,
                  step.completed && styles.completedCircle,
                  step.current && styles.currentCircle,
                ]}
              >
                {step.completed ? (
                  <CheckIcon width={32} height={32} color={GlobalStyles.base.white} />
                ) : (
                  <Text
                    style={[styles.stepNumber, step.current && styles.currentStepNumber]}
                  >
                    {index + 1}
                  </Text>
                )}
              </View>

              <Text
                style={[
                  styles.label,
                  step.current && styles.currentLabel,
                  step.completed && styles.completedLabel,
                ]}
                numberOfLines={1}
              >
                {step.label}
              </Text>
            </View>

            {/* Connecting Line */}
            {index < steps.length - 1 && (
              <View style={[styles.line, step.completed && styles.completedLine]} />
            )}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

export default memo(ProgressBar);

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 22,
    paddingVertical: 20,
    backgroundColor: GlobalStyles.base.white,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray900,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 7,
    elevation: 4,
  },
  stepsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepColumn: {
    alignItems: 'center',
    width: 70,
  },
  circle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: GlobalStyles.base.white,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray300,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completedCircle: {
    backgroundColor: '#1A2B6B',
    borderColor: '#1A2B6B',
  },
  currentCircle: {
    backgroundColor: '#1A2B6B',
    borderColor: '#1A2B6B',
  },
  stepNumber: {
    fontSize: 13,
    color: GlobalStyles.gray.gray600,
    fontWeight: '500',
  },
  currentStepNumber: {
    color: GlobalStyles.base.white,
  },
  line: {
    width: 24,
    height: 1,
    backgroundColor: GlobalStyles.gray.gray200,
    marginHorizontal: 2,
  },
  completedLine: {
    backgroundColor: '#1A2B6B',
  },
  label: {
    fontSize: 11,
    color: GlobalStyles.gray.gray600,
    textAlign: 'center',
    marginTop: 8,
    letterSpacing: 0.5,
    width: 70,
  },
  currentLabel: {
    color: '#1A2B6B',
    fontWeight: '600',
  },
  completedLabel: {
    color: '#1A2B6B',
  },
});
