import React, {memo, useMemo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useSnapPoints} from '@/hooks/bottomSheet';
import BottomSheetWrapper from './BottomSheetWrapper';

type BottomSheetScreenProps = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description: string;
  icon?: any;
  height?: number;
};

const BottomSheetScreen = memo(({isOpen, onClose, icon = <>

    </>, title, description, height = 40}: BottomSheetScreenProps) => {
  const snapPoints = useSnapPoints(height);

  const content = useMemo(
    () => (
      <View style={styles.container}>
        {icon && <View style={styles.logoContainer}>{icon}</View>}
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
      </View>
    ),
    [title, description],
  );

  return (
    <BottomSheetWrapper isOpen={isOpen} snapPoints={snapPoints} onClose={onClose}>
      {content}
    </BottomSheetWrapper>
  );
});

export default BottomSheetScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 32,
    padding: 36,
  },
  logoContainer: {
    marginBottom: 42,
  },
  title: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 22,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 16,
  },
  description: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 18,
    color: GlobalStyles.base.black,
    lineHeight: 24,
    textAlign: 'center',
  },
  buttonContainer: {
    marginTop: 24,
  },
});
