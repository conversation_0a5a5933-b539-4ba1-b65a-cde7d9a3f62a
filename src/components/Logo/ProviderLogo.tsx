import Node from '@/assets/providers/asset=01node.svg';
import ANKR from '@/assets/providers/asset=ANKR.svg';
import Avado from '@/assets/providers/asset=Avado.svg';
import Blocks_United from '@/assets/providers/asset=Blocks United.svg';
import Blockshard from '@/assets/providers/asset=Blockshard.svg';
import Brick_Towers from '@/assets/providers/asset=Brick Towers.svg';
import Chainode_Tech from '@/assets/providers/asset=Chainode Tech.svg';
import Chorus_One from '@/assets/providers/asset=Chorus One.svg';
import Citadel_one from '@/assets/providers/asset=Citadel.one.svg';
import Coinbase_Cloud from '@/assets/providers/asset=Coinbase Cloud.svg';
import Colossus from '@/assets/providers/asset=Colossus.svg';
import Cosmostation from '@/assets/providers/asset=Cosmostation.svg';
import Decali from '@/assets/providers/asset=Decali.svg';
import Deutsche_Telekom from '@/assets/providers/asset=Deutsche Telekom.svg';
import Easy_2_Stake from '@/assets/providers/asset=Easy 2 Stake.svg';
import Everstake from '@/assets/providers/asset=Everstake.svg';
import Frax_Finance from '@/assets/providers/asset=Frax Finance.svg';
import Golden_Ratio_Staking from '@/assets/providers/asset=Golden Ratio Staking.svg';
import HashQuark from '@/assets/providers/asset=HashQuark.svg';
import InfStones from '@/assets/providers/asset=InfStones.svg';
import Lavender_Five_Nodes from '@/assets/providers/asset=Lavender.Five Nodes.svg';
import Lido_Finance from '@/assets/providers/asset=Lido Finance.svg';
import Luganodes from '@/assets/providers/asset=Luganodes.svg';
import MANTRA from '@/assets/providers/asset=MANTRA.svg';
import Moonstake from '@/assets/providers/asset=Moonstake.svg';
import Nodes_Guru from '@/assets/providers/asset=Nodes.Guru.svg';
import Oni from '@/assets/providers/asset=Oni.svg';
import P_OPS_Team from '@/assets/providers/asset=P-OPS Team.svg';
import P2P from '@/assets/providers/asset=P2P.svg';
import RockawayX_Infrastructure from '@/assets/providers/asset=RockawayX Infrastructure.svg';
import SenseiNode from '@/assets/providers/asset=SenseiNode.svg';
import Speedy_Staking from '@/assets/providers/asset=Speedy Staking.svg';
import StakeCraft from '@/assets/providers/asset=StakeCraft.svg';
import StakeTab from '@/assets/providers/asset=StakeTab.svg';
import StakeWise from '@/assets/providers/asset=StakeWise.svg';
import StakeWithUs from '@/assets/providers/asset=StakeWithUs.svg';
import Stakely from '@/assets/providers/asset=Stakely.svg';
import Stakewolle from '@/assets/providers/asset=Stakewolle.svg';
import Stakin from '@/assets/providers/asset=Stakin.svg';
import Staking_Facilities from '@/assets/providers/asset=Staking Facilities.svg';
import Staking4All from '@/assets/providers/asset=Staking4All.svg';
import ThomasBlock_iо from '@/assets/providers/asset=ThomasBlock.iо.svg';
import Valid_Blocks from '@/assets/providers/asset=Valid Blocks.svg';
import ValidatorNode from '@/assets/providers/asset=ValidatorNode.svg';
import Validatrium from '@/assets/providers/asset=Validatrium.svg';
import Vault_Staking from '@/assets/providers/asset=Vault Staking.svg';
import A41 from '@/assets/providers/asset=a41.svg';
import Asset90 from '@/assets/providers/asset=asset90.svg';
import Danku_zone_w__DAIC from '@/assets/providers/asset=danku_zone_w__DAIC.svg';
import Allnodes from '@/assets/providers/asset=logo_Allnodes.svg';
import Stake2earn from '@/assets/providers/asset=stake2earn.svg';
import Stakeseeker from '@/assets/providers/asset=stakeseeker.svg';

const NodeSVG: React.FC = () => <Node height={'150%'} style={{alignSelf: 'center'}} />;
const ANKRSVG: React.FC = () => <ANKR height={'150%'} style={{alignSelf: 'center'}} />;
const AvadoSVG: React.FC = () => <Avado height={'150%'} style={{alignSelf: 'center'}} />;
const Blocks_UnitedSVG: React.FC = () => (
  <Blocks_United height={'150%'} style={{alignSelf: 'center'}} />
);
const BlockshardSVG: React.FC = () => (
  <Blockshard height={'150%'} style={{alignSelf: 'center'}} />
);
const Brick_TowersSVG: React.FC = () => (
  <Brick_Towers height={'150%'} style={{alignSelf: 'center'}} />
);
const Chainode_TechSVG: React.FC = () => (
  <Chainode_Tech height={'150%'} style={{alignSelf: 'center'}} />
);
const Chorus_OneSVG: React.FC = () => (
  <Chorus_One height={'150%'} style={{alignSelf: 'center'}} />
);
const Citadel_oneSVG: React.FC = () => (
  <Citadel_one height={'150%'} style={{alignSelf: 'center'}} />
);
const Coinbase_CloudSVG: React.FC = () => (
  <Coinbase_Cloud height={'150%'} style={{alignSelf: 'center'}} />
);
const ColossusSVG: React.FC = () => (
  <Colossus height={'150%'} style={{alignSelf: 'center'}} />
);
const CosmostationSVG: React.FC = () => (
  <Cosmostation height={'150%'} style={{alignSelf: 'center'}} />
);
const DecaliSVG: React.FC = () => (
  <Decali height={'150%'} style={{alignSelf: 'center'}} />
);
const Deutsche_TelekomSVG: React.FC = () => (
  <Deutsche_Telekom height={'150%'} style={{alignSelf: 'center'}} />
);
const Easy_2_StakeSVG: React.FC = () => (
  <Easy_2_Stake height={'150%'} style={{alignSelf: 'center'}} />
);
const EverstakeSVG: React.FC = () => (
  <Everstake height={'150%'} style={{alignSelf: 'center'}} />
);
const Frax_FinanceSVG: React.FC = () => (
  <Frax_Finance height={'150%'} style={{alignSelf: 'center'}} />
);
const Golden_Ratio_StakingSVG: React.FC = () => (
  <Golden_Ratio_Staking height={'150%'} style={{alignSelf: 'center'}} />
);
const HashQuarkSVG: React.FC = () => (
  <HashQuark height={'150%'} style={{alignSelf: 'center'}} />
);
const InfStonesSVG: React.FC = () => (
  <InfStones height={'150%'} style={{alignSelf: 'center'}} />
);
const Lavender_Five_NodesSVG: React.FC = () => (
  <Lavender_Five_Nodes height={'150%'} style={{alignSelf: 'center'}} />
);
const Lido_FinanceSVG: React.FC = () => (
  <Lido_Finance height={'150%'} style={{alignSelf: 'center'}} />
);
const LuganodesSVG: React.FC = () => (
  <Luganodes height={'150%'} style={{alignSelf: 'center'}} />
);
const MANTRASVG: React.FC = () => (
  <MANTRA height={'150%'} style={{alignSelf: 'center'}} />
);
const MoonstakeSVG: React.FC = () => (
  <Moonstake height={'150%'} style={{alignSelf: 'center'}} />
);
const Nodes_GuruSVG: React.FC = () => (
  <Nodes_Guru height={'150%'} style={{alignSelf: 'center'}} />
);
const OniSVG: React.FC = () => <Oni height={'150%'} style={{alignSelf: 'center'}} />;
const P_OPS_TeamSVG: React.FC = () => (
  <P_OPS_Team height={'150%'} style={{alignSelf: 'center'}} />
);
const P2P_orgSVG: React.FC = () => <P2P height={'150%'} style={{alignSelf: 'center'}} />;
const RockawayX_InfrastructureSVG: React.FC = () => (
  <RockawayX_Infrastructure height={'150%'} style={{alignSelf: 'center'}} />
);
const SenseiNodeSVG: React.FC = () => (
  <SenseiNode height={'150%'} style={{alignSelf: 'center'}} />
);
const Speedy_StakingSVG: React.FC = () => (
  <Speedy_Staking height={'150%'} style={{alignSelf: 'center'}} />
);
const StakeCraftSVG: React.FC = () => (
  <StakeCraft height={'150%'} style={{alignSelf: 'center'}} />
);
const StakeTabSVG: React.FC = () => (
  <StakeTab height={'150%'} style={{alignSelf: 'center'}} />
);
const StakeWiseSVG: React.FC = () => (
  <StakeWise height={'150%'} style={{alignSelf: 'center'}} />
);
const StakeWithUsSVG: React.FC = () => (
  <StakeWithUs height={'150%'} style={{alignSelf: 'center'}} />
);
const StakelySVG: React.FC = () => (
  <Stakely height={'150%'} style={{alignSelf: 'center'}} />
);
const StakewolleSVG: React.FC = () => (
  <Stakewolle height={'150%'} style={{alignSelf: 'center'}} />
);
const StakinSVG: React.FC = () => (
  <Stakin height={'150%'} style={{alignSelf: 'center'}} />
);
const Staking_FacilitiesSVG: React.FC = () => (
  <Staking_Facilities height={'150%'} style={{alignSelf: 'center'}} />
);
const Staking4AllSVG: React.FC = () => (
  <Staking4All height={'150%'} style={{alignSelf: 'center'}} />
);
const ThomasBlock_iоSVG: React.FC = () => (
  <ThomasBlock_iо height={'150%'} style={{alignSelf: 'center'}} />
);
const Valid_BlocksSVG: React.FC = () => (
  <Valid_Blocks height={'150%'} style={{alignSelf: 'center'}} />
);
const ValidatorNodeSVG: React.FC = () => (
  <ValidatorNode height={'150%'} style={{alignSelf: 'center'}} />
);
const ValidatriumSVG: React.FC = () => (
  <Validatrium height={'150%'} style={{alignSelf: 'center'}} />
);
const Vault_StakingSVG: React.FC = () => (
  <Vault_Staking height={'150%'} style={{alignSelf: 'center'}} />
);
const a41SVG: React.FC = () => <A41 height={'150%'} style={{alignSelf: 'center'}} />;
const asset90SVG: React.FC = () => (
  <Asset90 height={'150%'} style={{alignSelf: 'center'}} />
);
const danku_zone_w__DAICSVG: React.FC = () => (
  <Danku_zone_w__DAIC height={'150%'} style={{alignSelf: 'center'}} />
);
const AllnodesSVG: React.FC = () => (
  <Allnodes height={'150%'} style={{alignSelf: 'center'}} />
);
const stake2earnSVG: React.FC = () => (
  <Stake2earn height={'150%'} style={{alignSelf: 'center'}} />
);
const stakeseekerSVG: React.FC = () => (
  <Stakeseeker height={'150%'} style={{alignSelf: 'center'}} />
);

type LogoProps = {
  name: string;
};

const Logo: React.FC<LogoProps> = ({name}) => {
  const componentNames: {[key: string]: React.FC} = {
    NodeSVG,
    ANKRSVG,
    AvadoSVG,
    Blocks_UnitedSVG,
    BlockshardSVG,
    Brick_TowersSVG,
    Chainode_TechSVG,
    Chorus_OneSVG,
    Citadel_oneSVG,
    Coinbase_CloudSVG,
    ColossusSVG,
    CosmostationSVG,
    DecaliSVG,
    Deutsche_TelekomSVG,
    Easy_2_StakeSVG,
    EverstakeSVG,
    Frax_FinanceSVG,
    Golden_Ratio_StakingSVG,
    HashQuarkSVG,
    InfStonesSVG,
    Lavender_Five_NodesSVG,
    Lido_FinanceSVG,
    LuganodesSVG,
    MANTRASVG,
    MoonstakeSVG,
    Nodes_GuruSVG,
    OniSVG,
    P_OPS_TeamSVG,
    P2P_orgSVG,
    RockawayX_InfrastructureSVG,
    SenseiNodeSVG,
    Speedy_StakingSVG,
    StakeCraftSVG,
    StakeTabSVG,
    StakeWiseSVG,
    StakeWithUsSVG,
    StakelySVG,
    StakewolleSVG,
    StakinSVG,
    Staking_FacilitiesSVG,
    Staking4AllSVG,
    ThomasBlock_iоSVG,
    Valid_BlocksSVG,
    ValidatorNodeSVG,
    ValidatriumSVG,
    Vault_StakingSVG,
    a41SVG,
    asset90SVG,
    danku_zone_w__DAICSVG,
    AllnodesSVG,
    stake2earnSVG,
    stakeseekerSVG,
  };
  const Component = componentNames[name] || componentNames.NodeSVG;

  if (componentNames[name] === undefined) {
    console.log(`Logo component with name ${name} does not exist`);
  }
  return <Component />;
};

export default Logo;
