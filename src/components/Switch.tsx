import React, {memo} from 'react';
import {Switch as RNSwitch, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {BodySB} from '@/styles/styled-components';
import {openLink} from '@/utils/in-app-browser';

type Props = {
  label: string;
  handleSwitch: () => void;
  isEnabled: boolean;
  restContainerStyles?: any;
  link?: string;
};

const Switch: React.FC<Props> = ({
  label,
  handleSwitch,
  isEnabled,
  link,
  restContainerStyles,
}) => {
  const handleLinkPress = async () => {
    if (link) {
      await openLink(link);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.switchWithLabelContainer, restContainerStyles]}
      onPress={link ? handleLinkPress : handleSwitch}
      activeOpacity={0.7}
    >
      <RNSwitch
        value={isEnabled}
        onValueChange={handleSwitch}
        trackColor={{
          false: GlobalStyles.gray.gray600,
          true: GlobalStyles.success.success400,
        }}
        thumbColor={GlobalStyles.base.white}
        ios_backgroundColor={GlobalStyles.gray.gray600}
      />

      <View style={styles.labelContainer}>
        {link ? <Text style={styles.linkLabel}>{label}</Text> : <BodySB>{label}</BodySB>}
      </View>
    </TouchableOpacity>
  );
};

export default memo(Switch);

const styles = StyleSheet.create({
  switchWithLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 12,
    gap: 16,
  },
  labelContainer: {
    flex: 1,
  },
  linkLabel: {
    color: '#3c699e',
    textDecorationLine: 'underline',
  },
});
