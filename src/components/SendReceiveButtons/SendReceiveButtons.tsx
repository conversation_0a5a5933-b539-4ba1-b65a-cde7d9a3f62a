import React, {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';

import ChevronDownFilled from '@/assets/icons/chevronDownFilled.svg';
import ChevronPlusFilled from '@/assets/icons/chevronPlusFilled.svg';
import ChevronUpFilled from '@/assets/icons/chevronUpFilled.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import SendReceiveButton from './SendReceiveButton';

const ChevronUp = () => <ChevronUpFilled width={32} height={32} />;
const ChevronDown = () => <ChevronDownFilled width={32} height={32} />;
const ChevronPlus = () => <ChevronPlusFilled width={32} height={32} />;

type SendReceiveButtonsProps = {
  handleSendPress: () => void;
  handleReceivePress: () => void;
  handleBuyPress?: () => void;
  buyButtonTitle?: string;
};

const SendReceiveButtons = ({
  handleSendPress,
  handleReceivePress,
  handleBuyPress,
  buyButtonTitle,
}: SendReceiveButtonsProps) => {
  const {t} = useTranslation();

  const buyButton = handleBuyPress ? true : false;

  const borderLeftEdge = StyleSheet.flatten([
    styles.borderNoEdge,
    {borderTopLeftRadius: 8, borderBottomLeftRadius: 8},
  ]);
  const borderRightEdge = StyleSheet.flatten([
    styles.borderNoEdge,
    {borderTopRightRadius: 8, borderBottomRightRadius: 8},
  ]);

  return (
    <View style={styles.buttonsContainer}>
      <SendReceiveButton
        onPress={handleSendPress}
        title={t('wallet.send')}
        textColor={GlobalStyles.primary.primary500}
        icon={<ChevronUp />}
        disabled={false}
        borders={{...borderLeftEdge, ...{width: buyButton ? '32%' : '49%'}}}
      />
      <SendReceiveButton
        onPress={handleReceivePress}
        title={t('wallet.receive')}
        textColor={GlobalStyles.primary.primary500}
        icon={<ChevronDown />}
        disabled={false}
        borders={
          handleBuyPress
            ? {...styles.borderNoEdge, ...{width: '32%'}}
            : {...borderRightEdge, ...{width: '49%'}}
        }
      />
      {handleBuyPress && (
        <SendReceiveButton
          onPress={handleBuyPress}
          title={buyButtonTitle || 'Buy'}
          textColor={GlobalStyles.primary.primary500}
          icon={<ChevronPlus />}
          disabled={false}
          borders={{...borderRightEdge, ...{width: '32%'}}}
        />
      )}
    </View>
  );
};

export default memo(SendReceiveButtons);

const styles = StyleSheet.create({
  buttonsContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
  },
  borderNoEdge: {
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: GlobalStyles.primary.primary50,
  },
});
