import React, {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import {useTheme} from 'styled-components/native';

import {ArrowUpIcon, ArrowDownIcon, PlusIcon} from 'react-native-heroicons/solid';
import GlobalStyles from '@/constants/GlobalStyles';
import {ITheme} from '@/styles/themes';
import SendReceiveButton from './SendReceiveButton';

const createThemedIcons = (theme: ITheme) => ({
  ChevronUp: () => <ArrowUpIcon width={32} height={32} color={theme.colors.primary} />,
  ChevronDown: () => (
    <ArrowDownIcon width={32} height={32} color={theme.colors.primary} />
  ),
  ChevronPlus: () => <PlusIcon width={32} height={32} color={theme.colors.primary} />,
});

type SendReceiveButtonsProps = {
  handleSendPress: () => void;
  handleReceivePress: () => void;
  handleBuyPress?: () => void;
  buyButtonTitle?: string;
};

const SendReceiveButtons = ({
  handleSendPress,
  handleReceivePress,
  handleBuyPress,
  buyButtonTitle,
}: SendReceiveButtonsProps) => {
  const {t} = useTranslation();
  const currentTheme = useTheme() as ITheme;
  const icons = createThemedIcons(currentTheme);

  const buyButton = handleBuyPress ? true : false;

  const borderLeftEdge = StyleSheet.flatten([
    styles.borderNoEdge,
    {borderTopLeftRadius: 8, borderBottomLeftRadius: 8},
  ]);
  const borderRightEdge = StyleSheet.flatten([
    styles.borderNoEdge,
    {borderTopRightRadius: 8, borderBottomRightRadius: 8},
  ]);

  return (
    <View style={styles.buttonsContainer}>
      <SendReceiveButton
        onPress={handleSendPress}
        title={t('wallet.send')}
        textColor={currentTheme.colors.primary}
        icon={<icons.ChevronUp />}
        disabled={false}
        borders={{...borderLeftEdge, ...{width: buyButton ? '32%' : '49%'}}}
      />
      <SendReceiveButton
        onPress={handleReceivePress}
        title={t('wallet.receive')}
        textColor={currentTheme.colors.primary}
        icon={<icons.ChevronDown />}
        disabled={false}
        borders={
          handleBuyPress
            ? {...styles.borderNoEdge, ...{width: '32%'}}
            : {...borderRightEdge, ...{width: '49%'}}
        }
      />
      {handleBuyPress && (
        <SendReceiveButton
          onPress={handleBuyPress}
          title={buyButtonTitle || 'Buy'}
          textColor={currentTheme.colors.primary}
          icon={<icons.ChevronPlus />}
          disabled={false}
          borders={{...borderRightEdge, ...{width: '32%'}}}
        />
      )}
    </View>
  );
};

export default memo(SendReceiveButtons);

const styles = StyleSheet.create({
  buttonsContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginTop: 8,
  },
  borderNoEdge: {
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: GlobalStyles.primary.primary50,
  },
});
