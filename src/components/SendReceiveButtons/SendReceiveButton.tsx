import {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

type SendReceiveButtonProps = {
  onPress: () => void;
  title: string;
  textColor: string;
  icon: React.ReactNode;
  disabled: boolean;
  borders: object;
};

const SendReceiveButton = ({
  onPress,
  title,
  textColor,
  icon,
  disabled,
  borders,
}: SendReceiveButtonProps) => {
  const buttonStyle = StyleSheet.flatten([styles.sendReceiveButton, borders]);
  const iconStyle = StyleSheet.flatten([styles.icon, icon ? styles.show : styles.hide]);
  const textStyle = StyleSheet.flatten([styles.textStyle]);

  return (
    <TouchableOpacity onPress={onPress} style={buttonStyle} disabled={disabled}>
      <View style={iconStyle}>{icon && <View>{icon}</View>}</View>
      <Text style={textStyle}>{title}</Text>
    </TouchableOpacity>
  );
};

export default memo(SendReceiveButton);

const styles = StyleSheet.create({
  hide: {
    marginRight: 0,
    display: 'none',
  },
  show: {
    display: 'flex',
  },
  icon: {
    alignItems: 'center',
    resizeMode: 'contain',
  },
  sendReceiveButton: {
    position: 'relative',
    alignItems: 'center',
    marginVertical: 10,
    alignSelf: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: GlobalStyles.base.white,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  textStyle: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: -5,
    marginTop: 10,
  },
});
