import React, {memo} from 'react';
import {
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

type Props = {
  label: string;
  selected: boolean;
  onPress: () => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
};

const RadioButton: React.FC<Props> = ({
  label,
  selected,
  onPress,
  containerStyle,
  labelStyle,
}) => {
  return (
    <TouchableOpacity
      style={[styles.radioOption, containerStyle]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.radioButton, selected && styles.radioButtonSelected]}>
        <View style={selected ? styles.radioButtonInnerSelected : undefined} />
      </View>
      <Text style={[styles.radioText, labelStyle]}>{label}</Text>
    </TouchableOpacity>
  );
};

export default memo(RadioButton);

const styles = StyleSheet.create({
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: GlobalStyles.gray.gray600,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  radioButtonSelected: {
    borderColor: GlobalStyles.primary.primary500,
  },
  radioButtonInnerSelected: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: GlobalStyles.primary.primary500,
  },
  radioText: {
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
});
