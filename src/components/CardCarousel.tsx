import {NavigationProp, useNavigation} from '@react-navigation/native';
import React, {useCallback, useRef, useState} from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  ImageBackground,
  ListRenderItem,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewToken,
} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
const {width} = Dimensions.get('window');

type RootStackParamList = {
  Subscribe: object;
  Rewards: object;
  Trade: object;
};

interface CardItem {
  id: string;
  title: string;
  subtitle?: string;
  backgroundImage?: any;
  backgroundColor?: string;
  navigationTarget: keyof RootStackParamList;
  navigationParams?: object;
}

interface CardCarouselProps {
  activeCardIndex?: number;
  onCardChange?: (index: number) => void;
  data?: CardItem[];
}

const CARD_WIDTH = width * 0.9;
const CARD_SPACING = 10;
const BORDER_RADIUS = 10;

const DEFAULT_CARDS: CardItem[] = [
  {
    id: '1',
    title: 'Need a loan?',
    subtitle:
      'Get a crypto loan with low rates and personalized terms to fit your needs.',
    backgroundImage: require('../../assets/logo/homeFrame.png'),
    navigationTarget: 'Subscribe',
  },
  {
    id: '2',
    title: 'Get Started',
    subtitle:
      'Kickstart your crypto journey today with a platform tailored to adapt and meet your every need.',
    navigationTarget: 'Rewards',
  },
];

const CardContent = ({item}: {item: CardItem}) => (
  <View style={styles.cardInner}>
    <Text style={[styles.cardTitle, item.backgroundImage && styles.imageCardText]}>
      {item.title}
    </Text>
    {item.subtitle && (
      <Text style={[styles.cardSubtitle, item.backgroundImage && styles.imageCardText]}>
        {item.subtitle}
      </Text>
    )}
  </View>
);

const CardCarousel: React.FC<CardCarouselProps> = ({
  activeCardIndex = 0,
  onCardChange,
  data = DEFAULT_CARDS,
}) => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const [currentIndex, setCurrentIndex] = useState(activeCardIndex);
  const flatListRef = useRef<FlatList>(null);

  const handleViewableItemsChanged = useCallback(
    ({viewableItems}: {viewableItems: ViewToken[]}) => {
      if (viewableItems.length > 0 && typeof viewableItems[0]?.index === 'number') {
        setCurrentIndex(viewableItems[0].index);
        onCardChange?.(viewableItems[0].index);
      }
    },
    [onCardChange],
  );

  const viewabilityConfig = useRef({
    itemVisiblePercentThreshold: 50,
  }).current;

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: CARD_WIDTH + CARD_SPACING,
      offset: (CARD_WIDTH + CARD_SPACING) * index,
      index,
    }),
    [],
  );

  const handleCardPress = useCallback(
    (item: CardItem) => {
      navigation.navigate(item.navigationTarget, item.navigationParams || {});
    },
    [navigation],
  );

  const renderCard: ListRenderItem<CardItem> = useCallback(
    ({item}) => (
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={() => handleCardPress(item)}
        style={[
          styles.cardContainer,
          !item.backgroundImage && {
            backgroundColor: item.backgroundColor || '#fea659',
            height: 132,
            marginBottom: 3,
            borderRadius: 5,
          },
        ]}
      >
        {item.backgroundImage ? (
          <View style={{borderRadius: BORDER_RADIUS, overflow: 'hidden'}}>
            <ImageBackground
              source={item.backgroundImage}
              style={styles.backgroundImage}
              imageStyle={styles.imageStyle}
              resizeMode="cover"
            >
              <CardContent item={item} />
            </ImageBackground>
          </View>
        ) : (
          <CardContent item={item} />
        )}
      </TouchableOpacity>
    ),
    [handleCardPress, CardContent],
  );

  const renderPaginationDots = useCallback(
    () => (
      <View style={styles.pagination}>
        {data.map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              index === currentIndex && styles.paginationDotActive,
            ]}
          />
        ))}
      </View>
    ),
    [currentIndex, data],
  );

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={data}
        renderItem={renderCard}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        snapToInterval={CARD_WIDTH + CARD_SPACING}
        snapToAlignment="center"
        decelerationRate="fast"
        contentContainerStyle={styles.carouselContainer}
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
        getItemLayout={getItemLayout}
        initialScrollIndex={activeCardIndex}
        removeClippedSubviews={true}
        maxToRenderPerBatch={2}
        windowSize={3}
      />
      {renderPaginationDots()}
    </View>
  );
};

export default CardCarousel;

const styles = StyleSheet.create({
  container: {
    height: 180,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  carouselContainer: {
    paddingHorizontal: (width - CARD_WIDTH) / 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardContainer: {
    width: CARD_WIDTH,
    height: 155,
    marginHorizontal: CARD_SPACING / 2,
    borderRadius: BORDER_RADIUS,
    overflow: 'hidden',
  },
  backgroundImage: {
    width: '100%',
    height: '100%',
    borderRadius: BORDER_RADIUS,
    overflow: 'hidden',
  },
  imageStyle: {
    borderRadius: BORDER_RADIUS,
  },
  cardInner: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    zIndex: 1,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 15,
    fontWeight: '500',
    color: '#F3F4F6',
    lineHeight: 20,
    marginBottom: 6,
  },
  pagination: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 0,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: GlobalStyles.gray.gray900,
  },
  imageCardText: {
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: {width: 0, height: 1},
    textShadowRadius: 2,
  },
});
