import React, {memo, ReactElement} from 'react';
import {Image, ImageSourcePropType, StyleSheet, View} from 'react-native';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {BodyM, Headline} from '@styles/styled-components';

type Props = {
  title: string | ReactElement;
  description: string | ReactElement;
  image: ImageSourcePropType | ReactElement;
  continueText: string;
  cancelText?: string;
  isLoading?: boolean;
  onCancel?: () => void;
  onContinue: () => void;
};

const BottomSheetScreen = ({
  title,
  description,
  image,
  continueText,
  cancelText,
  isLoading,
  onCancel = () => {},
  onContinue,
}: Props): ReactElement => {
  const renderTitle = () => {
    if (typeof title === 'string') {
      return (
        <View style={styles.titleContainer}>
          <Headline style={styles.titleText}>{title}</Headline>
        </View>
      );
    }
    return title;
  };

  const renderDescription = () => {
    if (typeof description === 'string') {
      return <BodyM style={styles.descriptionText}>{description}</BodyM>;
    }
    return description;
  };

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <View style={styles.imageContainer}>
          {React.isValidElement(image) ? (
            image
          ) : (
            <Image style={styles.image} source={image as ImageSourcePropType} />
          )}
        </View>

        <View style={styles.textContainer}>
          {renderTitle()}

          {renderDescription()}
        </View>

        <View style={styles.buttonContainer}>
          {cancelText && (
            <MButton
              text={cancelText}
              onPress={onCancel}
              variant="secondary"
              containerStyle={styles.button}
              textStyle={{
                color: GlobalStyles.base.black,
              }}
            />
          )}
          <MButton
            text={continueText}
            onPress={onContinue}
            isLoading={isLoading}
            containerStyle={styles.button}
            textStyle={{
              color: GlobalStyles.base.white,
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default memo(BottomSheetScreen);

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 32,
    paddingVertical: 22,
    alignItems: 'center',
    gap: 28,
  },
  imageContainer: {
    alignItems: 'center',
  },
  image: {
    resizeMode: 'contain',
  },
  textContainer: {
    width: '100%',
    alignItems: 'flex-start',
  },
  titleContainer: {
    width: '100%',
    marginBottom: 16,
    alignItems: 'flex-start',
    paddingRight: 111,
  },
  titleText: {
    textAlign: 'left',
    flexWrap: 'wrap',
  },
  descriptionText: {
    textAlign: 'left',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  button: {
    flex: 1,
  },
});
