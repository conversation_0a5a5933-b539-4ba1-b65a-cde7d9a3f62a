import React, {memo} from 'react';
import {
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

type Props = {
  label: string;
  checked: boolean;
  onPress: () => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
};

const Checkbox: React.FC<Props> = ({
  label,
  checked,
  onPress,
  containerStyle,
  labelStyle,
}) => {
  return (
    <TouchableOpacity
      style={[styles.checkboxContainer, containerStyle]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.checkbox, checked && styles.checkboxSelected]}>
        {checked && <View style={styles.checkboxInner} />}
      </View>
      <Text style={[styles.checkboxText, labelStyle]}>{label}</Text>
    </TouchableOpacity>
  );
};

export default memo(Checkbox);

const styles = StyleSheet.create({
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderWidth: 2,
    borderColor: GlobalStyles.gray.gray600,
    marginRight: 12,
    marginTop: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    borderColor: GlobalStyles.primary.primary500,
    backgroundColor: GlobalStyles.primary.primary500,
  },
  checkboxInner: {
    width: 14,
    height: 14,
    backgroundColor: GlobalStyles.base.white,
  },
  checkboxText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 24,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
});
