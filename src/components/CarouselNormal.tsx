import React, {useState} from 'react';
import {StyleSheet, View, ViewStyle} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import {useSafeAreaFrame} from 'react-native-safe-area-context';

import GlobalStyles from '@/constants/GlobalStyles';

type Props<T> = {
  data: T[];
  renderItem: (info: {item: T; index: number}) => React.ReactElement;
  onSlideChange?: (index: number) => void;
  style?: ViewStyle;
  loop?: boolean;
};

const CarouselNormal = <T extends unknown>({
  data,
  renderItem,
  onSlideChange,
  style,
  loop = false,
}: Props<T>): React.ReactElement => {
  const {width} = useSafeAreaFrame();

  const [currentIndex, setCurrentIndex] = useState(0);

  const handleSlideChange = (index: number) => {
    setCurrentIndex(index);
    onSlideChange?.(index);
  };

  return (
    <View style={[styles.container, style]}>
      <Carousel
        width={width}
        data={data}
        renderItem={renderItem}
        onSnapToItem={handleSlideChange}
        loop={loop}
        scrollAnimationDuration={500}
      />
      <CarouselPagination data={data} activeIndex={currentIndex} />
    </View>
  );
};

export const CarouselPagination = ({
  data,
  activeIndex,
}: {
  data: any[];
  activeIndex: number;
}) => {
  if (data.length === 0) {
    return null;
  }

  return (
    <View style={styles.pagination}>
      {data.map((_, index) => (
        <View
          key={index}
          style={[
            styles.dot,
            index === activeIndex ? styles.activeDot : styles.inactiveDot,
          ]}
        />
      ))}
    </View>
  );
};

export default CarouselNormal;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: 16,
    paddingBottom: 4,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 6,
  },
  activeDot: {
    backgroundColor: GlobalStyles.primary.primary400,
    width: 24,
  },
  inactiveDot: {
    backgroundColor: GlobalStyles.gray.gray900,
    opacity: 0.2,
  },
});
