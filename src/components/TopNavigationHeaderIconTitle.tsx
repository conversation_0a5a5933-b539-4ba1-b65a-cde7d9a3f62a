import {memo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

const TopNavigationHeaderIconTitle = ({
  title,
  icon: Icon,
}: {
  title: string;
  icon?: React.ComponentType<any>;
}) => (
  <View style={styles.headerContainer}>
    {Icon && <Icon width={32} height={32} />}
    <Text style={styles.title}>{title}</Text>
  </View>
);

export default memo(TopNavigationHeaderIconTitle);

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontSize: 19,
  },
});
