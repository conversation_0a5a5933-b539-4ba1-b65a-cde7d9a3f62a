import {Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {GradientView} from '@/components/GradientView';
import styles from './styles';

type NavButtonProps = {
  title: string;
  onPress: () => void;
  selected: boolean;
};

const NavButton = ({title, onPress, selected}: NavButtonProps) => {
  return (
    <View style={styles.container}>
      {selected ? (
        <TouchableOpacity style={{flex: 1}} onPress={onPress}>
          <GradientView
            colors={[GlobalStyles.primary.primary500, GlobalStyles.primary.primary400]}
            style={selected ? styles.selectedContainer : styles.notSelectedContainer}
          >
            <Text style={styles.title}>{title}</Text>
          </GradientView>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity onPress={onPress}>
          <View style={styles.notSelectedContainer}>
            <Text style={styles.notSelectedTitle}>{title}</Text>
          </View>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default NavButton;
