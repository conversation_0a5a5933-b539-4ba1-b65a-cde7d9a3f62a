import {StyleSheet} from 'react-native';
import GlobalStyles from '@/constants/GlobalStyles';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginLeft: 10,
  },
  selectedContainer: {
    borderRadius: 200,
    borderStyle: 'solid',
    alignItems: 'center',
    paddingHorizontal: 16,
    alignSelf: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    overflow: 'hidden',
  },
  title: {
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.white,
    fontSize: 13,
    fontWeight: '500',
    lineHeight: 18,
  },
  notSelectedTitle: {
    color: GlobalStyles.base.black,
    fontSize: 13,
    fontWeight: '500',
    lineHeight: 18,
    fontFamily: GlobalStyles.fonts.sfPro,
  },

  notSelectedContainer: {
    borderRadius: 200,
    borderStyle: 'solid',
    alignItems: 'center',
    paddingHorizontal: 16,
    alignSelf: 'center',
    justifyContent: 'center',
    backgroundColor: GlobalStyles.base.white,
    paddingVertical: 10,
  },
});

export default styles;
