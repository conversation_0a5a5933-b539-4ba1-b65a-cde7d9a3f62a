import React from 'react';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import theme from '@/styles/themes';

interface SummaryField {
  label: string;
  value: string;
}

interface TxSummaryProps {
  title?: string;
  fields: SummaryField[];
}

const TxSummary: React.FC<TxSummaryProps> = ({title = 'Review', fields}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      <View style={styles.divider} />

      <View style={styles.fieldsContainer}>
        {fields.map((field, index) => (
          <View
            key={index}
            style={[styles.row, index === fields.length - 1 && {borderBottomWidth: 0}]}
          >
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{field.label}</Text>
            </View>
            <View style={styles.valueContainer}>
              <Text style={styles.value}>{field.value}</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

export default TxSummary;

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 16,
    padding: theme.layout.ph.screen,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: GlobalStyles.gray.gray800,
    marginBottom: 8,
    textAlign: 'center',
    letterSpacing: 0.5,
    marginLeft: 14,
  },
  divider: {
    height: 1,
    backgroundColor: GlobalStyles.gray.gray600,
    marginVertical: 14,
  },
  fieldsContainer: {
    marginTop: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray400,
  },
  labelContainer: {
    flex: 1,
    paddingRight: 12,
  },
  valueContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  label: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 15,
    color: GlobalStyles.gray.gray800,
    fontWeight: '500',
  },
  value: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 15,
    color: GlobalStyles.base.black,
    fontWeight: '600',
    textAlign: 'right',
  },
});
