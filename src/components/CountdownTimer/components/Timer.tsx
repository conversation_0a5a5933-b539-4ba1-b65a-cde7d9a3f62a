import {useEffect, useRef} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';

import GlobalStyles from '@/constants/GlobalStyles';
import {getFontSize} from '@/utils/parsing';
import {formatTime} from '../utils';

export const Timer: React.FC<any> = ({timeLeft, onTick, onCompletion}) => {
  const intervalRef = useRef<number | null>(null);
  const isPaused = useSelector((state: any) => state.exchange.isPaused);

  useEffect(() => {
    if (intervalRef.current) clearInterval(intervalRef.current);

    if (isPaused) {
      clearInterval(intervalRef.current!);
      return;
    }

    intervalRef.current = setInterval(() => {
      if (timeLeft < 1) {
        clearInterval(intervalRef.current!);
        onCompletion();
      } else {
        const newTime = timeLeft - 1;
        onTick(newTime);
      }
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [timeLeft, onCompletion, onTick, isPaused]);
  return (
    <View style={styles.root}>
      <Text style={styles.timeText}>{formatTime(timeLeft)}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    paddingLeft: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  timeText: {
    color: GlobalStyles.base.black,
    fontSize: getFontSize(15),
    fontWeight: 'bold',
    letterSpacing: 1,
  },
});
