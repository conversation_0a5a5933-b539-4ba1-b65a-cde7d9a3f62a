import {useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {setTimeLeft} from '@/storage/actions/exchangeActions';
import {ChronometerUI} from './CronometerUI';
import {Timer} from './Timer';

const CountdownTimer: React.FC<any> = ({timeDuration, onCompletion}) => {
  const timeLeft = useSelector((state: any) => state.exchange.timeLeft);
  const dispatch = useDispatch();

  const onTick = (newTimeLeft: number) => dispatch(setTimeLeft(newTimeLeft));

  const memoizedChronometer = useMemo(
    () => <ChronometerUI initialDurationInSeconds={timeDuration} timeLeft={timeLeft} />,
    [timeDuration, timeLeft],
  );

  return (
    <View style={styles.root}>
      {memoizedChronometer}
      <Timer
        timeDuration={timeDuration}
        timeLeft={timeLeft}
        onTick={onTick}
        onCompletion={onCompletion}
      />
    </View>
  );
};

export default CountdownTimer;

const styles = StyleSheet.create({
  root: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
