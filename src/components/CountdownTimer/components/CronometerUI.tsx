import React, {useMemo} from 'react';
import {StyleSheet, View} from 'react-native';
import Svg, {Circle} from 'react-native-svg';

import GlobalStyles from '@/constants/GlobalStyles';
import {TChronometerUIProps} from '../types';

const RADIUS = 7.5;
const CENTER = 10;
const CIRCUMFERENCE = 2 * Math.PI * RADIUS;

const MATCHING_SIZE = 20;

export const ChronometerUI: React.FC<TChronometerUIProps> = ({
  initialDurationInSeconds,
  timeLeft,
}) => {
  const progress = timeLeft / initialDurationInSeconds;

  // Memoize strokeDashoffset calculation
  const strokeDashoffset = useMemo(() => CIRCUMFERENCE * progress, [progress]);

  return (
    <View style={styles.root}>
      <Svg
        height={MATCHING_SIZE}
        width={MATCHING_SIZE}
        viewBox={`0 0 ${MATCHING_SIZE} ${MATCHING_SIZE}`}
      >
        <Circle
          cx={CENTER}
          cy={CENTER}
          r={RADIUS}
          fill="none"
          stroke={GlobalStyles.gray.gray400}
          strokeWidth={RADIUS * 2}
          strokeDasharray={CIRCUMFERENCE}
          strokeDashoffset={strokeDashoffset}
          transform={`rotate(-90 ${CENTER} ${CENTER})`}
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    height: MATCHING_SIZE,
    width: MATCHING_SIZE,
    borderRadius: 10, // Always half of `MATCHING_SIZE`
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
    backgroundColor: GlobalStyles.success.success800,
    marginLeft: 8,
  },
});
