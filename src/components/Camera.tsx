import {useIsFocused} from '@react-navigation/native';
import React, {ReactElement, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {Camera as CameraKit, CameraType} from 'react-native-camera-kit';

const Camera = ({
  children,
  torchMode = false,
  onBarcodeRead,
}: {
  children?: ReactElement;
  torchMode?: boolean;
  bottomSheet?: boolean;
  onBarcodeRead: (data: string) => void;
}): ReactElement => {
  const isFocused = useIsFocused();
  const [_data, setData] = useState('');

  const handleCodeRead = (event): void => {
    const {codeStringValue} = event.nativeEvent;
    if (_data !== codeStringValue) {
      setData(codeStringValue);
      onBarcodeRead(codeStringValue);
    }
  };

  if (!isFocused) {
    return <View style={styles.container} />;
  }

  return (
    <View style={styles.container}>
      <CameraKit
        style={styles.camera}
        scanBarcode={true}
        onReadCode={handleCodeRead}
        torchMode={torchMode ? 'on' : 'off'}
        cameraType={CameraType.Back}
      />
      {children}
    </View>
  );
};

export default Camera;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  camera: {
    ...StyleSheet.absoluteFillObject,
  },
});
