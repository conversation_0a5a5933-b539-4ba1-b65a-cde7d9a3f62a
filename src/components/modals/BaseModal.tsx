import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProps,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import React, {forwardRef, memo, ReactNode, useCallback} from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {XMarkIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {Title} from '@/styles/styled-components';
import theme from '@/styles/themes';

interface BaseModalProps extends Partial<BottomSheetModalProps> {
  children: ReactNode;
  title?: string;
  snapPoints?: string[];
  onClose?: () => void;
}

const BaseModal = forwardRef<BottomSheetModal, BaseModalProps>(
  ({children, title, snapPoints = ['50%', '90%'], ...props}, ref) => {
    const renderBackdrop = (props: BottomSheetBackdropProps) => {
      return <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />;
    };

    const renderHandleComponent = useCallback(() => {
      return (
        <View style={styles.handleContainer}>
          <View style={styles.titleContainer}>
            <Title style={styles.title}>{title}</Title>
          </View>
          <Pressable
            style={({pressed}) => [
              styles.closeIconContainer,
              {
                opacity: pressed ? 0.2 : 1,
              },
            ]}
            onPress={() => {
              if (ref && 'current' in ref) {
                ref.current?.close();
              }
            }}
            hitSlop={12}
          >
            <XMarkIcon color={GlobalStyles.base.black} size={28} />
          </Pressable>
        </View>
      );
    }, [ref, title]);

    return (
      <BottomSheetModal
        ref={ref}
        snapPoints={snapPoints}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        handleComponent={renderHandleComponent}
        {...props}
      >
        <BottomSheetView style={styles.content}>{children}</BottomSheetView>
      </BottomSheetModal>
    );
  },
);

export default memo(BaseModal);

const styles = StyleSheet.create({
  content: {
    flex: 1,
    marginBottom: 16,
  },
  handleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 12,
  },
  closeIconContainer: {
    padding: 6,
    borderRadius: 20,
  },
  titleContainer: {
    flex: 1,
    marginLeft: theme.spacing.lg,
  },
  title: {
    fontSize: 16,
  },
});
