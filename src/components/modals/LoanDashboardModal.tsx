import {BottomSheetModal} from '@gorhom/bottom-sheet';
import React, {forwardRef, memo} from 'react';

import LoanDashboard from '@/screens/Loan/screens/manage-loan/LoanDashboard';
import BaseModal from './BaseModal';

interface LoanDashboardModalProps {
  loanId: string;
}

const LoanDashboardModal = forwardRef<BottomSheetModal, LoanDashboardModalProps>(
  ({loanId}, ref) => {
    return (
      <BaseModal ref={ref} snapPoints={['90%']} title="Loan Overview">
        <LoanDashboard loanId={loanId} />
      </BaseModal>
    );
  },
);

export default memo(LoanDashboardModal);
