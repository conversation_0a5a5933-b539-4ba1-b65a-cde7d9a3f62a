import {BottomSheetModal} from '@gorhom/bottom-sheet';
import React, {forwardRef, memo} from 'react';
import {ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {ClipboardIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import theme from '@/styles/themes';
import {handleCopy} from '@/utils';
import {ILoanState, Loan} from '@/screens/Loan/utils/loan-types';
import {getLoanStatusText, getStatusColor} from '@/screens/Loan/components/utils';
import BaseModal from './BaseModal';
import MButton from '../MButton';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';

interface PendingLoanModalProps {
  loan: Loan | null;
}

const PendingLoanModal = forwardRef<BottomSheetModal, PendingLoanModalProps>(
  ({loan}, ref) => {
    if (!loan) return null;

    const formattedDate = new Date(loan.createdAt).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    const statusColors = getStatusColor(loan.state);
    const statusText = getLoanStatusText(loan.state);

    const handleCopyAddress = () => {
      if (loan.terms.depositAddress) {
        handleCopy(loan.terms.depositAddress);
      }
    };

    const getNextStepMessage = (state: ILoanState): string => {
      switch (state) {
        case ILoanState.PENDING_BANK_TERMS_APPROVAL:
          return 'Your loan application is being reviewed by our banking partner. You will receive a notification once approved.';
        case ILoanState.PENDING_WALLET_CREATION:
          return 'You must create yourself an MPC wallet for your collateral. Just click on the button below to create one.';
        case ILoanState.PENDING_COLLATERAL_DEPOSIT_TRANSACTION:
          return 'Please deposit your collateral to the address below to activate your loan.';
        case ILoanState.PENDING_COLLATERAL_DEPOSIT_CONFIRMATION:
          return 'Your collateral deposit is being confirmed on the blockchain. This may take several minutes.';
        case ILoanState.PENDING_COLLATERAL_WITHDRAWAL_APPROVAL:
          return 'Your collateral withdrawal request is being processed for approval.';
        case ILoanState.PENDING_COLLATERAL_WITHDRAWAL_TRANSACTION:
          return 'Please complete the collateral withdrawal transaction.';
        case ILoanState.PENDING_COLLATERAL_WITHDRAWAL_CONFIRMATION:
          return 'Your collateral withdrawal is being confirmed on the blockchain.';
        default:
          return 'Your loan is being processed. Please check back later for updates.';
      }
    };

    const handleCreateMPCWallet = () => {
      console.log('Creating MPC wallet', loan);
      navigateViaBottomTabs('Loan', 'LoanMpc', {loanID: loan._id});
      ref.current?.close();
    };

    const showDepositAddress =
      loan.state === ILoanState.PENDING_COLLATERAL_DEPOSIT_TRANSACTION;

    return (
      <BaseModal ref={ref} snapPoints={['70%']} title="Loan Details">
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Status Section */}
          <View
            style={[
              styles.statusContainer,
              {backgroundColor: GlobalStyles.orange.orange50},
            ]}
          >
            <View style={[styles.statusBadge, {backgroundColor: statusColors.badge}]}>
              <Text style={styles.statusText}>{statusText}</Text>
            </View>
            <Text style={styles.statusMessage}>{getNextStepMessage(loan.state)}</Text>
          </View>

          {/* Loan Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Loan Information</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date Created</Text>
              <Text style={styles.infoValue}>{formattedDate}</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Fiat Amount</Text>
              <Text style={[styles.infoValue, styles.principalAmount]}>
                {loan.terms.fiat.amount.toLocaleString()} {loan.terms.fiat.currency}
              </Text>
            </View>
          </View>

          {/* Loan Terms */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Loan Terms</Text>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Collateral</Text>
              <Text style={styles.infoValue}>
                {loan.terms.collateral.amount} {loan.terms.collateral.currency}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Loan-to-Value (LTV)</Text>
              <Text style={styles.infoValue}>{loan.terms.ltv}%</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Term</Text>
              <Text style={styles.infoValue}>{loan.terms.term} months</Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Interest Payment</Text>
              <Text style={styles.infoValue}>{loan.terms.interest}</Text>
            </View>
          </View>

          {/* Deposit Address Section */}
          {showDepositAddress && loan.terms.depositAddress && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Deposit Information</Text>
              <Text style={styles.depositInstructions}>
                Send your collateral to the address below to activate your loan:
              </Text>
              <View style={styles.depositAddressContainer}>
                <Text style={styles.depositAddressLabel}>
                  {loan.terms.collateral.currency} Deposit Address
                </Text>
                <View style={styles.depositAddressRow}>
                  <Text style={styles.depositAddress} numberOfLines={2}>
                    {loan.terms.depositAddress}
                  </Text>
                  <TouchableOpacity
                    style={styles.copyButton}
                    onPress={handleCopyAddress}
                    accessibilityLabel="Copy deposit address"
                  >
                    <ClipboardIcon color={GlobalStyles.primary.primary500} size={24} />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          {loan.state === ILoanState.PENDING_WALLET_CREATION && (
            <MButton onPress={handleCreateMPCWallet} text="Create MPC Wallet" />
          )}
        </ScrollView>
      </BaseModal>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 26,
    paddingBottom: 32,
  },
  statusContainer: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    position: 'relative',
  },
  statusBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 12,
  },
  statusText: {
    color: GlobalStyles.base.white,
    fontSize: 12,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  statusMessage: {
    fontSize: 16,
    lineHeight: 24,
    color: GlobalStyles.gray.gray800,
    marginTop: 20,
    paddingRight: 80,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
    paddingVertical: 4,
  },
  infoLabel: {
    fontSize: 14,
    color: GlobalStyles.gray.gray900,
    flex: 1,
    marginRight: 16,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    flex: 1,
    textAlign: 'right',
  },
  principalAmount: {
    fontSize: 16,
    fontWeight: '700',
    color: GlobalStyles.base.black,
  },
  depositInstructions: {
    fontSize: 14,
    color: GlobalStyles.gray.gray700,
    marginBottom: 16,
    lineHeight: 20,
  },
  depositAddressContainer: {
    backgroundColor: GlobalStyles.gray.gray100,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: GlobalStyles.primary.primary200,
  },
  depositAddressLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: GlobalStyles.gray.gray800,
    marginBottom: 8,
    textTransform: 'uppercase',
  },
  depositAddressRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  depositAddress: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'monospace',
    color: GlobalStyles.base.black,
    marginRight: 12,
    lineHeight: 20,
  },
  copyButton: {
    padding: 4,
  },
  noticeContainer: {
    backgroundColor: GlobalStyles.orange.orange50,
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: GlobalStyles.orange.orange400,
  },
  noticeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.orange.orange800,
    marginBottom: 8,
  },
  noticeText: {
    fontSize: 13,
    color: GlobalStyles.orange.orange700,
    lineHeight: 18,
  },
});

export default memo(PendingLoanModal);
