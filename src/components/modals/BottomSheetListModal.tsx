import {BottomSheetFlatList, BottomSheetModal} from '@gorhom/bottom-sheet';
import React, {forwardRef, useCallback, useMemo, useState} from 'react';
import {StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {CheckCircleIcon} from 'react-native-heroicons/solid';

import SearchIcon from '@/assets/icons/magnifying glass.svg';
import type {BaseOption, CurrencyOption} from '@/components/BottomSheetList';
import CurrencyIcon from '@/components/CurrencyIcon';
import SafeAreaInset from '@/components/SafeAreaInset';
import GlobalStyles from '@/constants/GlobalStyles';
import {capitalizeAll} from '@/utils';
import BaseModal from './BaseModal';

interface BottomSheetListModalProps {
  data: (BaseOption | CurrencyOption)[];
  onSelect: (item: BaseOption | CurrencyOption) => void;
  selectedValue?: string;
  enableSearch?: boolean;
  title?: string;
  snapPoints?: string[];
}

const BottomSheetListModal = forwardRef<BottomSheetModal, BottomSheetListModalProps>(
  (
    {
      data,
      onSelect,
      selectedValue,
      enableSearch = false,
      title = '',
      snapPoints = ['50%', '90%'],
    },
    ref,
  ) => {
    const [searchQuery, setSearchQuery] = useState('');

    const isCurrencyOption = (
      item: CurrencyOption | BaseOption,
    ): item is CurrencyOption => {
      return 'fullName' in item && 'type' in item;
    };

    const filteredData = useMemo(() => {
      if (!enableSearch || !searchQuery) return data;

      const query = searchQuery.toLowerCase();
      return data.filter((item) => {
        if (isCurrencyOption(item)) {
          return (
            item.label.toLowerCase().includes(query) ||
            item.fullName.toLowerCase().includes(query)
          );
        }
        return item.label.toLowerCase().includes(query);
      });
    }, [data, searchQuery, enableSearch]);

    const handleSelect = useCallback(
      (item: CurrencyOption | BaseOption) => {
        onSelect(item);
        (ref as any)?.current?.dismiss();
        setSearchQuery('');
      },
      [onSelect, ref],
    );

    const renderItem = useCallback(
      ({item}: {item: CurrencyOption | BaseOption}) => {
        const isSelected = selectedValue === item.label;

        return (
          <TouchableOpacity
            style={[styles.optionButton, isSelected && styles.selectedOption]}
            onPress={() => handleSelect(item)}
            activeOpacity={0.7}
          >
            {isCurrencyOption(item) ? (
              <View style={styles.optionContainer}>
                <View style={styles.optionIconContainer}>
                  <CurrencyIcon currency={item} />
                </View>
                <View style={styles.optionTextContainer}>
                  <Text
                    style={[styles.optionFullName, isSelected && styles.selectedText]}
                  >
                    {item.fullName}
                  </Text>
                  <Text
                    style={[
                      styles.optionAbbreviation,
                      isSelected && styles.selectedSubText,
                    ]}
                  >
                    {capitalizeAll(item.label)}
                  </Text>
                </View>
                {isSelected && (
                  <View style={styles.checkIconContainer}>
                    <CheckCircleIcon
                      width={28}
                      height={28}
                      color={GlobalStyles.success.success600}
                    />
                  </View>
                )}
              </View>
            ) : (
              <View style={styles.simpleOptionContainer}>
                <Text style={[styles.optionText, isSelected && styles.selectedText]}>
                  {item.label}
                </Text>
                {isSelected && (
                  <View style={styles.checkIconContainer}>
                    <CheckCircleIcon
                      width={28}
                      height={28}
                      color={GlobalStyles.success.success600}
                    />
                  </View>
                )}
              </View>
            )}
          </TouchableOpacity>
        );
      },
      [selectedValue, handleSelect],
    );

    const keyExtractor = useCallback(
      (item: BaseOption | CurrencyOption) => `${item.value}-${item.label}`,
      [],
    );

    return (
      <BaseModal ref={ref} title={title} snapPoints={snapPoints}>
        <View style={styles.container}>
          {enableSearch && (
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <SearchIcon width={20} height={20} style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>
          )}

          <BottomSheetFlatList
            data={filteredData}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContent}
          />

          <SafeAreaInset type="bottom" />
        </View>
      </BaseModal>
    );
  },
);

export default BottomSheetListModal;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  titleContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
    textAlign: 'center',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray300,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: GlobalStyles.gray.gray100,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 50,
  },
  searchIcon: {
    marginRight: 8,
    color: GlobalStyles.gray.gray600,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  optionButton: {
    padding: 18,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  selectedOption: {
    backgroundColor: GlobalStyles.gray.gray50,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  simpleOptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  optionIconContainer: {
    marginRight: 15,
    width: 30,
    alignItems: 'center',
  },
  optionTextContainer: {
    flex: 1,
  },
  checkIconContainer: {
    marginLeft: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionText: {
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
  },
  selectedText: {
    color: GlobalStyles.primary.primary500,
    fontWeight: '600',
  },
  optionFullName: {
    fontSize: 14,
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
  },
  selectedSubText: {
    color: GlobalStyles.primary.primary400,
  },
  optionAbbreviation: {
    fontSize: 12,
    color: GlobalStyles.gray.gray800,
    marginTop: 2,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  listContent: {
    padding: 4,
  },
});
