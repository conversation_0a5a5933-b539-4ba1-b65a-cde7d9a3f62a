import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import Copy from '@/assets/icons/copy.svg';
import GlobalStyles from '@/constants/GlobalStyles';

const CopyPasteButton: React.FC<{clipboardAction: any; title: string}> = ({
  clipboardAction,
  title,
}) => {
  return (
    <TouchableOpacity onPress={clipboardAction}>
      <View style={styles.container}>
        <Copy
          width={40}
          height={40}
          style={styles.icon}
          fill={GlobalStyles.primary.primary500}
        />
        <Text style={styles.text}>{title}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default CopyPasteButton;

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  text: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 16,
    color: GlobalStyles.primary.primary500,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  icon: {
    display: 'flex',
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
