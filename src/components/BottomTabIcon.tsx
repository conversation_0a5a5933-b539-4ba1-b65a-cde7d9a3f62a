import React, {memo} from 'react';
import {StyleProp, StyleSheet, TouchableOpacity, View, ViewStyle} from 'react-native';

const BottomTabIcon = ({
  IconComponent,
  onPress,
  style,
}: {
  IconComponent: React.ReactElement;
  onPress: () => void;
  style: StyleProp<ViewStyle>;
  testID?: string;
}) => (
  <TouchableOpacity
    style={[styles.tabContainer, style]}
    onPress={onPress}
    activeOpacity={0.8}
  >
    <View>{IconComponent}</View>
  </TouchableOpacity>
);

export default memo(BottomTabIcon);

const styles = StyleSheet.create({
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '20%',
  },
});
