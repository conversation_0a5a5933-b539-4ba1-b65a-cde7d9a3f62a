import React, {memo} from 'react';
import ContentLoader, {Rect} from 'react-content-loader/native';
import {StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useScreenSize} from '@/hooks/screen';
import {useSafeAreaFrame} from 'react-native-safe-area-context';

const LightningSwapsSkeleton = () => {
  const {isSmallScreen} = useScreenSize();
  const {width, height} = useSafeAreaFrame();

  const contentWidth = width - 32;

  const qrSize = 300;
  const qrBottomOffset = isSmallScreen ? 200 : 220;
  const qrYPosition = height - qrSize - qrBottomOffset;
  const sliderYPosition = height * 0.05;

  return (
    <View style={styles.container}>
      <ContentLoader
        speed={1}
        width={contentWidth}
        height={height}
        backgroundColor="#E5E7EB"
        foregroundColor="#F3F4F6"
        animate
        interval={0}
      >
        {/* Slider */}
        <Rect
          x={(contentWidth - contentWidth / 2) / 2}
          y={sliderYPosition}
          rx="20"
          ry="20"
          width={contentWidth / 2}
          height="60"
        />

        {/* QR Code Box */}
        <Rect
          x={(contentWidth - qrSize) / 2}
          y={qrYPosition}
          rx="8"
          ry="8"
          width={qrSize}
          height={qrSize}
        />
      </ContentLoader>
    </View>
  );
};

export default memo(LightningSwapsSkeleton);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.gray.gray300,
    padding: 16,
  },
});
