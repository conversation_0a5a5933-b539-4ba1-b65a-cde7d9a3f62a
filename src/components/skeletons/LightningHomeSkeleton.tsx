import React, {memo} from 'react';
import ContentLoader, {Circle, Rect} from 'react-content-loader/native';
import {StyleSheet, View} from 'react-native';
import {useSafeAreaFrame} from 'react-native-safe-area-context';

import GlobalStyles from '@/constants/GlobalStyles';
import {useScreenSize} from '@/hooks/screen';

const LightningHomeSkeleton = () => {
  const {width, height} = useSafeAreaFrame();
  const {isSmallScreen} = useScreenSize();

  const contentWidth = width - 40;
  const contentHeight = isSmallScreen ? height - 150 : height - 90;
  const buttonsYPosition = isSmallScreen ? height - 260 : height - 320;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <ContentLoader
          speed={1}
          width={contentWidth}
          height={contentHeight}
          backgroundColor="#E5E7EB"
          foregroundColor="#F3F4F6"
          style={styles.loader}
          animate
          interval={0}
        >
          {/* Balance */}
          <Rect
            x={contentWidth * 0.2}
            y={contentHeight * 0.15}
            rx="16"
            ry="16"
            width={contentWidth * 0.6}
            height="80"
          />

          {/* History Item */}
          <Circle cx={contentWidth * 0.35} cy={contentHeight * 0.35} r="12" />
          <Rect
            x={contentWidth * 0.4}
            y={contentHeight * 0.35 - 12}
            rx="12"
            ry="12"
            width={contentWidth * 0.25}
            height="24"
          />

          {/* Refunds Item */}
          <Circle cx={contentWidth * 0.35} cy={contentHeight * 0.45} r="12" />
          <Rect
            x={contentWidth * 0.4}
            y={contentHeight * 0.45 - 12}
            rx="12"
            ry="12"
            width={contentWidth * 0.25}
            height="24"
          />

          {/* Buttons */}
          <Rect
            x="0"
            y={buttonsYPosition}
            rx="16"
            ry="16"
            width={contentWidth}
            height="100"
          />
        </ContentLoader>
      </View>
    </View>
  );
};

export default memo(LightningHomeSkeleton);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  loader: {
    flex: 1,
  },
});
