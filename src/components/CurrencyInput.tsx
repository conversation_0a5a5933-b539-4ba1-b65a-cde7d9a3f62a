import React, {memo} from 'react';
import {
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TextInputProps,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {NumericFormat} from 'react-number-format';

import DropdownIcon from '@/assets/icons/arrow down.svg';
import type {CurrencyOption} from '@/components/BottomSheetList';
import GlobalStyles from '@/constants/GlobalStyles';
import type {ExchangeCurrencyOption} from '@/screens/Exchange/_types';

type CurrencyInputProps = Omit<TextInputProps, 'style'> & {
  label: string;
  selectedOption: CurrencyOption | ExchangeCurrencyOption;
  onOptionPress: () => void;
  onChangeText: (value: string) => void;
  onBlur?: (value: string) => void;
  value?: string;
  error?: string;
  decimals?: number;
  optionsDisabled?: boolean;
  styles?: {
    container?: ViewStyle;
    input?: ViewStyle;
    inputContainer?: ViewStyle;
    optionSelector?: ViewStyle;
    optionText?: TextStyle;
    label?: TextStyle;
  };
};

const CurrencyInput = ({
  label,
  selectedOption,
  onOptionPress,
  onChangeText,
  onBlur,
  value,
  error,
  decimals = 8,
  optionsDisabled = false,
  styles: customStyles,
  ...inputProps
}: CurrencyInputProps) => {
  const renderOptionContent = () => (
    <View style={styles.optionTextWrapper}>
      <Text
        style={[styles.optionFullName, customStyles?.optionText]}
        numberOfLines={1}
        ellipsizeMode="tail"
      >
        {selectedOption.fullName}
      </Text>
      <Text style={[styles.optionLabel, customStyles?.optionText]}>
        {selectedOption.label}
      </Text>
    </View>
  );

  const handleOnChange = (values: string) => {
    const withoutLeadingZeros = values.replace(/^0+(?=\d)/, '');
    onChangeText(withoutLeadingZeros);
  };

  return (
    <View style={[styles.container, customStyles?.container]}>
      <View style={[styles.inputContainer, customStyles?.inputContainer]}>
        <View style={styles.inputWrapper}>
          <Text style={[styles.label, customStyles?.label]}>{label}</Text>
          <NumericFormat
            value={value}
            displayType="text"
            thousandSeparator=","
            decimalSeparator="."
            decimalScale={decimals}
            allowNegative={false}
            allowLeadingZeros={false}
            fixedDecimalScale={false}
            onValueChange={(values) => {
              const {value: newValue} = values;
              handleOnChange(newValue);
            }}
            renderText={(formattedValue) => (
              <TextInput
                value={formattedValue}
                onChangeText={onChangeText}
                style={[styles.input, customStyles?.input]}
                keyboardType={decimals === 0 ? 'number-pad' : 'decimal-pad'}
                placeholder="0"
                placeholderTextColor={GlobalStyles.gray.gray700}
                autoComplete="off"
                autoCorrect={false}
                onBlur={onBlur}
                {...inputProps}
              />
            )}
          />
        </View>

        <TouchableOpacity
          onPress={onOptionPress}
          style={[
            styles.optionSelector,
            customStyles?.optionSelector,
            optionsDisabled && styles.disabledOption,
          ]}
          activeOpacity={0.7}
          disabled={optionsDisabled}
        >
          {renderOptionContent()}

          {!optionsDisabled && (
            <DropdownIcon height={25} width={25} style={styles.dropdownIcon} />
          )}
        </TouchableOpacity>
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

export default memo(CurrencyInput);

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputWrapper: {
    flex: 1,
    height: Platform.OS === 'android' ? 48 : 45,
    justifyContent: 'center',
    paddingBottom: Platform.OS === 'android' ? 0 : undefined,
  },
  label: {
    position: 'absolute',
    top: -2,
    left: -5,
    paddingHorizontal: 4,
    color: GlobalStyles.gray.gray700,
    fontSize: 14,
    ...Platform.select({
      android: {
        lineHeight: 18,
      },
    }),
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: GlobalStyles.base.white,
    borderColor: '#E0E0E0',
    borderWidth: 1,
    borderRadius: 8,
    minHeight: Platform.OS === 'android' ? 72 : undefined,
  },
  input: {
    flex: 1,
    paddingRight: 10,
    paddingTop: Platform.OS === 'android' ? 26 : 24,
    paddingBottom: Platform.OS === 'android' ? 0 : undefined,
    color: GlobalStyles.base.black,
    fontSize: 18,
    fontWeight: 'bold',
    height: Platform.OS === 'android' ? 48 : undefined,
    ...Platform.select({
      android: {
        includeFontPadding: false,
        textAlignVertical: 'center',
        paddingLeft: 0,
      },
    }),
  },
  optionSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 10,
    borderLeftColor: GlobalStyles.gray.gray600,
    borderLeftWidth: 1,
  },
  optionTextWrapper: {
    alignItems: 'center',
    marginRight: 3,
  },
  optionFullName: {
    color: GlobalStyles.gray.gray700,
    fontSize: 14,
    maxWidth: 100,
  },
  optionLabel: {
    color: GlobalStyles.base.black,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledOption: {
    opacity: 0.5,
  },
  dropdownIcon: {
    marginLeft: 5,
  },
  errorText: {
    marginTop: 5,
    marginLeft: 4,
    color: GlobalStyles.error.error300,
    fontSize: 12,
  },
});
