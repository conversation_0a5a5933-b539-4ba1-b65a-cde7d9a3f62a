import React, {useCallback, useEffect, useMemo} from 'react';
import {
  Dimensions,
  FlatList,
  InputAccessoryView,
  ListRenderItemInfo,
  Platform,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {KeyboardAccessoryView} from 'react-native-keyboard-accessory';
import rnAndroidKeyboardAdjust from 'rn-android-keyboard-adjust';

import GlobalStyles from '@/constants/GlobalStyles';

interface WordProps {
  word: string;
  onPress: (word: string) => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  index: number;
  itemWidth: number;
}

const Word: React.FC<WordProps> = React.memo(
  ({word, onPress, style, textStyle, index, itemWidth}) => (
    <TouchableOpacity
      onPress={() => onPress(word)}
      style={[
        styles.wordContainer,
        {width: itemWidth},
        index % 2 === 0 ? styles.evenWord : styles.oddWord,
        style,
      ]}
    >
      <Text style={[styles.wordText, textStyle]} numberOfLines={1} ellipsizeMode="tail">
        {word}
      </Text>
    </TouchableOpacity>
  ),
);

interface WordSuggestionAccessoryProps {
  suggestions: string[];
  onWordPress: (word: string) => void;
  placeholder: string;
  inputAccessoryViewID: string;
  maxVisibleWords?: number;
  containerStyle?: ViewStyle;
  wordStyle?: ViewStyle;
  wordTextStyle?: TextStyle;
  placeholderStyle?: TextStyle;
}

const WordSuggestionAccessory: React.FC<WordSuggestionAccessoryProps> = ({
  suggestions,
  onWordPress,
  placeholder,
  inputAccessoryViewID,
  maxVisibleWords = 6,
  containerStyle,
  wordStyle,
  wordTextStyle,
  placeholderStyle,
}) => {
  const {width: screenWidth} = Dimensions.get('window');
  const itemWidth = useMemo(
    () => screenWidth / maxVisibleWords,
    [screenWidth, maxVisibleWords],
  );

  useEffect(() => {
    rnAndroidKeyboardAdjust.setAdjustResize();

    return (): void => {
      rnAndroidKeyboardAdjust.setAdjustPan();
    };
  }, []);

  const renderItem = useCallback(
    ({item, index}: ListRenderItemInfo<string>) => (
      <Word
        word={item}
        onPress={onWordPress}
        index={index}
        itemWidth={itemWidth}
        style={wordStyle}
        textStyle={wordTextStyle}
      />
    ),
    [onWordPress, itemWidth, wordStyle, wordTextStyle],
  );

  const keyExtractor = useCallback(
    (item: string, index: number) => `${item}-${index}`,
    [],
  );

  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: itemWidth,
      offset: itemWidth * index,
      index,
    }),
    [itemWidth],
  );

  const renderSuggestions = () => (
    <View style={[styles.suggestionsContainer, containerStyle]}>
      {suggestions.length > 0 ? (
        <FlatList<string>
          data={suggestions}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
          keyboardShouldPersistTaps="always"
          getItemLayout={getItemLayout}
          removeClippedSubviews={true}
          initialNumToRender={maxVisibleWords}
          maxToRenderPerBatch={maxVisibleWords * 2}
          windowSize={5}
          decelerationRate="fast"
        />
      ) : (
        <Text style={[styles.placeholder, placeholderStyle]}>{placeholder}</Text>
      )}
    </View>
  );

  if (Platform.OS === 'ios') {
    return (
      <InputAccessoryView nativeID={inputAccessoryViewID}>
        {renderSuggestions()}
      </InputAccessoryView>
    );
  }

  return (
    <KeyboardAccessoryView alwaysVisible={true} avoidKeyboard={true}>
      {renderSuggestions()}
    </KeyboardAccessoryView>
  );
};

const styles = StyleSheet.create({
  suggestionsContainer: {
    backgroundColor: GlobalStyles.gray.gray600,
    borderTopWidth: 1,
    borderTopColor: '#ccc',
    paddingVertical: 10,
    width: '100%',
  },
  flatListContent: {
    paddingHorizontal: 5,
  },
  wordContainer: {
    padding: 8,
    marginHorizontal: 2,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    height: 40,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  evenWord: {
    backgroundColor: '#e0e0e0',
  },
  oddWord: {
    backgroundColor: '#d0d0d0',
  },
  wordText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  placeholder: {
    textAlign: 'center',
    color: '#888',
    fontSize: 14,
    paddingHorizontal: 10,
  },
});

export default WordSuggestionAccessory;
