import React from 'react';
import CountryFlag from 'react-native-country-flag';
// SVGs
import AVAXSVG from '@/assets/assets/asset=AVAX.svg';
import BCHSVG from '@/assets/assets/asset=BCH.svg';
import BTCSVG from '@/assets/assets/asset=BITCOIN.svg';
import BNBSVG from '@/assets/assets/asset=BNB.svg';
import DOGESVG from '@/assets/assets/asset=DOGE.svg';
import ETHSVG from '@/assets/assets/asset=ETHEREUM.svg';
import KASSVG from '@/assets/assets/asset=KAS.svg';
import LTCSVG from '@/assets/assets/asset=LTC.svg';
import SOLSVG from '@/assets/assets/asset=SOL.svg';
import TRXSVG from '@/assets/assets/asset=TRX.svg';
import XRPSVG from '@/assets/assets/asset=XRP.svg';
// ETH
import USDETH from '@/assets/assets/stable-ETH/asset=USDT.svg';
import USDCETH from '@/assets/assets/stable-ETH/asset=USDC.svg';
import TUSDETH from '@/assets/assets/stable-ETH/asset=TUSD.svg';
import DAIETH from '@/assets/assets/stable-ETH/asset=DAI.svg';
// BSC
import USDTBNB from '@/assets/assets/stable-BSC/asset=USDT.svg';
import USDCBNB from '@/assets/assets/stable-BSC/asset=USDC.svg';
import TUSDBNB from '@/assets/assets/stable-BSC/asset=TUSD.svg';
import DAIBNB from '@/assets/assets/stable-BSC/asset=DAI.svg';
// TRX
import USDTRX from '@/assets/assets/stable-TRX/asset=USDT.svg';
import USDCTRX from '@/assets/assets/stable-TRX/asset=USDC.svg';
import TUSDTRX from '@/assets/assets/stable-TRX/asset=TUSD.svg';
// AVAX
import USDTAVAX from '@/assets/assets/stable-AVAX/asset=USDT.svg';
import USDCAVAX from '@/assets/assets/stable-AVAX/asset=USDC.svg';
import DAIAVAX from '@/assets/assets/stable-AVAX/asset=DAI.svg';
import TUSDAVAX from '@/assets/assets/stable-AVAX/asset=TUSD.svg';
// Solana
import USDTSOL from '@/assets/assets/stable-SOL/asset=USDT.svg';
import USDCSOL from '@/assets/assets/stable-SOL/asset=USDC.svg';
import USDSSOL from '@/assets/assets/stable-SOL/asset=USDS.svg';
import JUPSOL from '@/assets/assets/stable-SOL/asset=JUP.svg';
import RAYSOL from '@/assets/assets/stable-SOL/asset=RAY.svg';

const symbolToSvg = (symbol: string) => {
  const mapping: {[key: string]: React.FC} = {
    btc: BTCSVG,
    eth: ETHSVG,
    sol: SOLSVG,
    trx: TRXSVG,
    bnbbsc: BNBSVG,
    xrp: XRPSVG,
    avax: AVAXSVG,
    bch: BCHSVG,
    ltc: LTCSVG,
    doge: DOGESVG,
    kas: KASSVG,
    usdt20: USDETH,
    usdceth: USDCETH,
    tusdeth: TUSDETH,
    daieth: DAIETH,
    usdtbsc: USDTBNB,
    usdcbsc: USDCBNB,
    tusdbsc: TUSDBNB,
    daibsc: DAIBNB,
    usdttrx: USDTRX,
    usdctrx: USDCTRX,
    tusdtrx: TUSDTRX,
    usdtavax: USDTAVAX,
    usdcavax: USDCAVAX,
    daiavax: DAIAVAX,
    tusdavax: TUSDAVAX,
    usdtsol: USDTSOL,
    usdcsol: USDCSOL,
    usdssol: USDSSOL,
    jupsol: JUPSOL,
    raysol: RAYSOL,
  };

  return mapping[symbol.toLowerCase()] || BNBSVG;
};

const CryptoIcon: React.FC<{label: string}> = ({label}) => {
  const IconComponent = symbolToSvg(label.toString());
  return <IconComponent />;
};

const CurrencyIcon: React.FC<any> = ({currency}) => {
  if (currency.type === 'fiat') {
    return <CountryFlag isoCode={currency.value} size={20} />;
  } else {
    return <CryptoIcon label={currency.label} />;
  }
};

export default CurrencyIcon;
