import React, {memo, useEffect} from 'react';
import {Dimensions, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import GlobalStyles from '@/constants/GlobalStyles';
import {capitalizeAll} from '@/utils';

const SLIDER_WIDTH = Dimensions.get('window').width * 0.75;
const SLIDER_HEIGHT = 44;
const TAB_WIDTH = SLIDER_WIDTH / 2;

type SliderProps = {
  tabs: [string, string];
  activeTab: string;
  onTabClick: (tabName: string) => void;
};

const Slider: React.FC<SliderProps> = ({tabs, activeTab, onTabClick}) => {
  const translateX = useSharedValue(0);

  useEffect(() => {
    translateX.value = withSpring(activeTab === tabs[0] ? 0 : TAB_WIDTH, {
      damping: 15,
      stiffness: 150,
    });
  }, [activeTab, tabs, translateX]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{translateX: translateX.value}],
    backgroundColor: '#633c61',
  }));

  return (
    <View style={styles.root}>
      <View style={[styles.slider, {width: SLIDER_WIDTH, height: SLIDER_HEIGHT}]}>
        <Animated.View style={[styles.sliderThumb, animatedStyle]} />
        <View style={styles.tabsContainer}>
          {tabs.map((tab) => (
            <TouchableOpacity
              key={tab}
              style={styles.tabItem}
              onPress={() => onTabClick(tab)}
              activeOpacity={0.7}
            >
              <Text
                style={[styles.tabText, activeTab === tab && styles.tabTextActive]}
                numberOfLines={1}
                adjustsFontSizeToFit
              >
                {capitalizeAll(tab)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
};

export default memo(Slider);

const styles = StyleSheet.create({
  root: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 14,
  },
  slider: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 50 / 2,
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
    overflow: 'hidden',
  },
  sliderThumb: {
    position: 'absolute',
    width: '50%',
    height: '100%',
    borderRadius: 50 / 2,
  },
  tabsContainer: {
    flexDirection: 'row',
    width: '100%',
    height: '100%',
  },
  tabItem: {
    width: '50%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  tabText: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 13,
    fontWeight: 'bold',
    textAlign: 'center',
    color: GlobalStyles.gray.gray900,
    marginLeft: 6,
  },
  tabTextActive: {
    color: GlobalStyles.base.white,
  },
});
