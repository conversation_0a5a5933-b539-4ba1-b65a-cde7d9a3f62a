import React, {memo} from 'react';
import {
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {useTheme} from 'styled-components/native';

import {ArrowLeftIcon, XMarkIcon} from 'react-native-heroicons/outline';
import SafeAreaInset from '@/components/SafeAreaInset';
import GlobalStyles from '@/constants/GlobalStyles';
import {ITheme} from '@/styles/themes';

const createThemedIcons = (theme: ITheme) => ({
  ArrowLeftSvg: () => (
    <ArrowLeftIcon
      width={30}
      height={30}
      color={theme.colors.text}
      style={{marginLeft: 5}}
    />
  ),
  XSvg: () => <XMarkIcon width={30} height={30} color={theme.colors.text} />,
});

type TopNavigationProps = {
  screenTitle: any;
  leftIcon?: boolean;
  leftIconAction?: () => void;
  rightIcon?: boolean;
  rightIconAction?: () => void;
  titleContainerStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
};

const TopNavigation: React.FC<TopNavigationProps> = ({
  screenTitle,
  leftIcon,
  rightIcon,
  leftIconAction,
  rightIconAction,
  titleStyle,
  titleContainerStyle,
}) => {
  const currentTheme = useTheme() as ITheme;
  const icons = createThemedIcons(currentTheme);

  return (
    <>
      <SafeAreaInset type="top" />

      <View style={styles.container}>
        <View style={styles.iconContainer}>
          {leftIcon ? (
            <TouchableOpacity onPress={leftIconAction}>
              <icons.ArrowLeftSvg />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={[styles.titleContainer, titleContainerStyle]}>
          {typeof screenTitle === 'string' ? (
            <Text style={[styles.title, titleStyle]} numberOfLines={1}>
              {screenTitle}
            </Text>
          ) : (
            screenTitle
          )}
        </View>

        <View style={styles.iconContainer}>
          {rightIcon ? (
            <TouchableOpacity onPress={rightIconAction}>
              <icons.XSvg />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
    </>
  );
};

export default memo(TopNavigation);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    paddingHorizontal: 8,
  },
  iconContainer: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 20,
    color: GlobalStyles.base.black,
    textAlign: 'center',
    marginLeft: 10,
  },
});
