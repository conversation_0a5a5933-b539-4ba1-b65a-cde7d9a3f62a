import React, {memo} from 'react';
import {
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import ArrowLeft from '@/assets/icons/arrow left.svg';
import X from '@/assets/icons/x.svg';
import SafeAreaInset from '@/components/SafeAreaInset';
import GlobalStyles from '@/constants/GlobalStyles';

const ArrowLeftSvg = () => <ArrowLeft width={30} height={30} style={{marginLeft: 5}} />;
const XSvg = () => <X width={30} height={30} />;

type TopNavigationProps = {
  screenTitle: any;
  leftIcon?: boolean;
  leftIconAction?: () => void;
  rightIcon?: boolean;
  rightIconAction?: () => void;
  titleContainerStyle?: StyleProp<ViewStyle>;
  titleStyle?: StyleProp<TextStyle>;
};

const TopNavigation: React.FC<TopNavigationProps> = ({
  screenTitle,
  leftIcon,
  rightIcon,
  leftIconAction,
  rightIconAction,
  titleStyle,
  titleContainerStyle,
}) => {
  return (
    <>
      <SafeAreaInset type="top" />

      <View style={styles.container}>
        <View style={styles.iconContainer}>
          {leftIcon ? (
            <TouchableOpacity onPress={leftIconAction}>
              <ArrowLeftSvg />
            </TouchableOpacity>
          ) : null}
        </View>

        <View style={[styles.titleContainer, titleContainerStyle]}>
          {typeof screenTitle === 'string' ? (
            <Text style={[styles.title, titleStyle]} numberOfLines={1}>
              {screenTitle}
            </Text>
          ) : (
            screenTitle
          )}
        </View>

        <View style={styles.iconContainer}>
          {rightIcon ? (
            <TouchableOpacity onPress={rightIconAction}>
              <XSvg />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
    </>
  );
};

export default memo(TopNavigation);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    paddingHorizontal: 8,
  },
  iconContainer: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 20,
    color: GlobalStyles.base.black,
    textAlign: 'center',
    marginLeft: 10,
  },
});
