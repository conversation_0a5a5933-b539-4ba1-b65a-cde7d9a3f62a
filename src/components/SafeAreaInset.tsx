import React, {ReactElement} from 'react';
import {View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

import useKeyboard from '@/hooks/keyboard';

const SafeAreaInset = ({
  type,
  minPadding = 0,
  maxPadding,
}: {
  type: 'top' | 'bottom';
  minPadding?: number;
  maxPadding?: number;
}): ReactElement => {
  const insets = useSafeAreaInsets();
  const {keyboardShown} = useKeyboard();

  let padding = Math.max(insets[type], minPadding);
  if (maxPadding) {
    padding = Math.min(padding, maxPadding);
  }

  // When keyboard is shown, don't add bottom padding
  if (type === 'bottom' && keyboardShown) {
    padding = minPadding;
  }

  return <View style={{height: padding}} />;
};

export default SafeAreaInset;
