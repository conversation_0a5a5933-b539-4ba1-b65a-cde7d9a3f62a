import {BlurView as Blur, BlurViewProps} from '@react-native-community/blur';
import React, {memo, ReactElement, ReactNode} from 'react';
import {Platform, StyleProp, StyleSheet, View, ViewStyle} from 'react-native';

type Props = {
  isBlurred?: boolean;
  children?: ReactNode;
  blurIntensity?: number;
  rootStyle?: StyleProp<ViewStyle>;
  androidOverlayStyle?: StyleProp<ViewStyle>;
  props?: BlurViewProps;
};

const BlurView = ({
  isBlurred = false,
  children,
  rootStyle,
  blurIntensity = 14,
  androidOverlayStyle,
  ...props
}: Props): ReactElement => {
  if (isBlurred) {
    return Platform.OS === 'ios' ? (
      <Blur
        {...props}
        style={[styles.ios, rootStyle]}
        blurType="light"
        blurAmount={blurIntensity}
      >
        {children}
      </Blur>
    ) : (
      <View style={[styles.androidContainer, rootStyle]}>
        <View style={[styles.androidOverlay, androidOverlayStyle]} />

        <View {...props} style={[styles.androidContent, styles.hiddenContent]}>
          {children}
        </View>
      </View>
    );
  }

  return (
    <View {...props} style={[rootStyle]}>
      {children}
    </View>
  );
};

export default memo(BlurView);

const styles = StyleSheet.create({
  ios: {
    borderRadius: 18,
  },
  androidContainer: {
    borderRadius: 18,
    overflow: 'hidden',
    position: 'relative',
  },
  androidOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#F5F5F5',
    opacity: 0.98,
  },
  androidContent: {
    position: 'relative',
    zIndex: 1,
    backgroundColor: 'transparent',
  },
  hiddenContent: {
    opacity: 0,
  },
});
