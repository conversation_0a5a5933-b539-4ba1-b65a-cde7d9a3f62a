import {BottomTabParamList} from '@/navigation/types';
import React, {useCallback} from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from 'styled-components/native';

import {
  HomeIcon,
  BoltIcon,
  ArrowsRightLeftIcon,
  EllipsisHorizontalIcon,
  CalculatorIcon,
  NewspaperIcon,
} from 'react-native-heroicons/outline';
import {
  HomeIcon as HomeIconSolid,
  BoltIcon as BoltIconSolid,
  ArrowsRightLeftIcon as ArrowsRightLeftIconSolid,
  EllipsisHorizontalIcon as EllipsisHorizontalIconSolid,
  CalculatorIcon as CalculatorIconSolid,
  NewspaperIcon as NewspaperIconSolid,
} from 'react-native-heroicons/solid';
import {AssetifyWhite} from '@/assets/index';
import Assetify from '@/assets/nav/homeFilled.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {ITheme} from '@/styles/themes';
import {vibrate} from '@/utils';
import BottomTabIcon from './BottomTabIcon';
import IconButton from './IconButton';

const ICON_SIZE = 28;

const HOME_ICON_SIZE = 31;
const HOME_FILLED_ICON_SIZE = 33;
const LN_ICON_SIZE = 31;
const MORE_ICON_SIZE = 33;

// prettier-ignore
const createIcons = (theme: ITheme) => ({
  Home: (filled: boolean) =>
    filled ? (
      <HomeIconSolid
        width={HOME_FILLED_ICON_SIZE}
        height={HOME_FILLED_ICON_SIZE}
        color={theme.colors.primary}
      />
    ) : (
      <HomeIcon
        width={HOME_ICON_SIZE}
        height={HOME_ICON_SIZE}
        color={theme.colors.text}
      />
    ),
  Lightning: (filled: boolean) =>
    filled ? (
      <BoltIconSolid
        width={LN_ICON_SIZE}
        height={LN_ICON_SIZE}
        color={theme.colors.primary}
      />
    ) : (
      <BoltIcon
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.text}
      />
    ),
  Exchange: (filled: boolean) =>
    filled ? (
      <ArrowsRightLeftIconSolid
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.primary}
      />
    ) : (
      <ArrowsRightLeftIcon
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.text}
      />
    ),
  More: (filled: boolean) =>
    filled ? (
      <EllipsisHorizontalIconSolid
        width={MORE_ICON_SIZE}
        height={MORE_ICON_SIZE}
        color={theme.colors.primary}
      />
    ) : (
      <EllipsisHorizontalIcon
        width={MORE_ICON_SIZE}
        height={MORE_ICON_SIZE}
        color={theme.colors.text}
      />
    ),
  Calculator: (filled: boolean) =>
    filled ? (
      <CalculatorIconSolid
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.primary}
      />
    ) : (
      <CalculatorIcon
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.text}
      />
    ),
  News: (filled: boolean) =>
    filled ? (
      <NewspaperIconSolid
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.primary}
      />
    ) : (
      <NewspaperIcon
        width={ICON_SIZE}
        height={ICON_SIZE}
        color={theme.colors.text}
      />
    ),
});

type Props = {
  state: any;
  descriptors: any;
  navigation: any;
  isLoggedIn: boolean;
};

const BottomTabBar = ({state, navigation, isLoggedIn}: Props) => {
  const {height} = useSafeAreaFrame();
  const insets = useSafeAreaInsets();
  const currentTheme = useTheme() as ITheme;

  const selectedTab = state.routes[state.index].name;
  const icons = createIcons(currentTheme);

  const handleTabPress = useCallback(
    (tabName: keyof BottomTabParamList) => {
      // logAnalyticsEventForTab(tabName);

      if (selectedTab === tabName) {
        const currentRoute = state.routes.find((route) => route.name === tabName);
        if (currentRoute?.state?.routes?.length > 1) {
          // Pop to the first screen in the stack
          navigation.popToTop();
        }
      }

      vibrate();
      navigation.navigate(tabName);
    },
    [selectedTab, state.routes, navigation],
  );

  const handleCBPress = () => {
    vibrate();
    navigation.navigate('Loan');
  };

  const renderTab = (
    tabName: keyof BottomTabParamList,
    Icon: React.ReactElement,
    style?: StyleProp<ViewStyle>,
  ) => (
    <BottomTabIcon
      IconComponent={Icon}
      onPress={() => handleTabPress(tabName)}
      style={style}
    />
  );

  const tabBarStyle = {
    height: isLoggedIn
      ? (height + insets.bottom + 30) / 10
      : (height + insets.bottom) / 10,
    // Add padding for devices with home indicators
    paddingBottom: insets.bottom > 0 ? 22 : 0,
    borderTopColor: 'transparent',
  };

  const styles = createStyles(currentTheme);

  return (
    <>
      {!isLoggedIn ? (
        <View style={[styles.container, tabBarStyle]}>
          {renderTab('Onboarding', icons.Home(selectedTab === 'Onboarding'))}

          {renderTab('Calculator', icons.Calculator(selectedTab === 'Calculator'))}

          {renderTab('News', icons.News(selectedTab === 'News'))}
        </View>
      ) : (
        <View style={[styles.container, tabBarStyle]}>
          <View style={styles.tabsContainer}>
            {renderTab('Wallet', icons.Home(selectedTab === 'Wallet'))}

            {renderTab('Lightning', icons.Lightning(selectedTab === 'Lightning'))}

            <View style={styles.centralButtonContainer}>
              <IconButton onPress={handleCBPress} style={styles.centralButton}>
                {currentTheme.id === 'dark' ? (
                  <AssetifyWhite
                    width={30}
                    height={30}
                    style={{marginLeft: 2, marginBottom: 3}}
                  />
                ) : (
                  <Assetify
                    width={30}
                    height={30}
                    style={{marginLeft: 2, marginBottom: 3}}
                  />
                )}
              </IconButton>
            </View>

            {renderTab('Exchange', icons.Exchange(selectedTab === 'Exchange'))}

            {renderTab('More', icons.More(selectedTab === 'More'))}
          </View>
        </View>
      )}
    </>
  );
};

export default BottomTabBar;

const createStyles = (currentTheme: ITheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      alignItems: 'center',
      backgroundColor: currentTheme.colors.background,
      borderTopColor: currentTheme.colors.onSurface,
      borderTopWidth: 1,
      // Android
      elevation: 4,
      // iOS
      shadowColor: '#633c61',
      shadowOffset: {width: 0, height: 4},
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    tabsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      alignItems: 'center',
      width: '100%',
      paddingHorizontal: 8,
      paddingVertical: 8,
      height: 70,
      marginRight: -8,
    },
    centralButtonContainer: {
      width: '20%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    centralButton: {
      width: 54,
      height: 54,
      backgroundColor: currentTheme.colors.background,
      borderColor: currentTheme.colors.secondary,
      borderWidth: 1,
      borderRadius: 27,
      justifyContent: 'center',
      alignItems: 'center',
      // Android
      elevation: 4,
      // iOS
      shadowColor: GlobalStyles.primary.primary700,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.4,
      shadowRadius: 2,
    },
  });
