import {useIsFocused} from '@react-navigation/native';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  ActivityIndicator,
  Animated,
  Easing,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
} from 'react-native';

import Checkmark from '@/assets/icons/checkmark.svg';
import COLORS from '@/constants/GlobalStyles';
import {vibrate} from '@/utils';

const {primary, success, base} = COLORS;
const HOLD_DURATION = 1500;
const BUBBLE_EXPAND_DURATION = 300;
const PULSE_DURATION = 1000;

type HoldToConfirmButtonProps = {
  disabled?: boolean;
  onPress: () => Promise<boolean>;
};

const HoldToConfirmButton: React.FC<HoldToConfirmButtonProps> = ({disabled, onPress}) => {
  const [holding, setHolding] = useState(false);
  const [executingOnPress, setExecutingOnPress] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const expandingBubbleAnim = useRef(new Animated.Value(0)).current;
  const expandingBubbleOpacityAnim = useRef(new Animated.Value(1)).current;
  const fillAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnimActive = useRef(false);
  const fillAnimRef = useRef<Animated.CompositeAnimation | null>(null);
  const bubbleAnimRef = useRef<Animated.CompositeAnimation | null>(null);
  const pulseAnimRef = useRef<Animated.CompositeAnimation | null>(null);

  const {t} = useTranslation();
  const viewFocused = useIsFocused();

  useEffect(() => {
    return () => {
      // Cleanup animations on unmount
      fillAnimRef.current?.stop();
      bubbleAnimRef.current?.stop();
      pulseAnimRef.current?.stop();
    };
  }, []);

  useEffect(() => {
    if (!viewFocused) {
      resetState();
    }
  }, [viewFocused]);

  useEffect(() => {
    if (holding) {
      expandBubble();
      pulse();
    } else {
      condenseBubble();
    }
  }, [holding]);

  const resetState = useCallback(() => {
    setHolding(false);
    setExecutingOnPress(false);
    setIsSuccess(false);
    stopFilling();
    stopPulsing();
    fillAnim.setValue(0);
    expandingBubbleAnim.setValue(0);
    expandingBubbleOpacityAnim.setValue(1);
    scaleAnim.setValue(1);
  }, []);

  const expandBubble = () => {
    bubbleAnimRef.current = Animated.sequence([
      Animated.timing(expandingBubbleAnim, {
        toValue: 1,
        duration: BUBBLE_EXPAND_DURATION,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(expandingBubbleOpacityAnim, {
        toValue: 0,
        duration: BUBBLE_EXPAND_DURATION,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    ]);
    bubbleAnimRef.current.start();
  };

  const condenseBubble = () => {
    bubbleAnimRef.current = Animated.parallel([
      Animated.timing(expandingBubbleAnim, {
        toValue: 0,
        duration: BUBBLE_EXPAND_DURATION,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(expandingBubbleOpacityAnim, {
        toValue: 1,
        duration: 0,
        useNativeDriver: true,
      }),
    ]);
    bubbleAnimRef.current.start();
  };

  const startFilling = () => {
    setHolding(true);
    vibrate();
    fillAnimRef.current = Animated.timing(fillAnim, {
      toValue: 1,
      duration: HOLD_DURATION,
      easing: Easing.linear,
      useNativeDriver: false,
    });
    fillAnimRef.current.start(async ({finished}) => {
      if (finished) {
        setExecutingOnPress(true);
        try {
          const result = await onPress();
          setIsSuccess(result);
          if (!result) {
            resetState();
          } else {
            Animated.sequence([
              Animated.timing(scaleAnim, {
                toValue: 1.05,
                duration: 100,
                easing: Easing.bounce,
                useNativeDriver: true,
              }),
              Animated.timing(scaleAnim, {
                toValue: 1,
                duration: 100,
                easing: Easing.bounce,
                useNativeDriver: true,
              }),
            ]).start();
          }
        } catch (e) {
          resetState();
        } finally {
          setExecutingOnPress(false);
        }
      }
    });
  };

  const stopFilling = () => {
    setHolding(false);
    fillAnimRef.current?.stop();
    fillAnim.setValue(0);
    stopPulsing();
  };

  const pulse = () => {
    pulseAnimActive.current = true;
    pulseAnimRef.current = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: PULSE_DURATION,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: PULSE_DURATION,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ]),
    );
    pulseAnimRef.current.start();
  };

  const stopPulsing = () => {
    pulseAnimActive.current = false;
    pulseAnimRef.current?.stop();
    pulseAnim.setValue(1);
  };

  const getButtonBackgroundColor = () => {
    if (isSuccess || executingOnPress) return success.success900;
    if (holding || pulseAnimActive.current) return primary.primary200;
    return primary.primary400;
  };

  const getTextColor = () => {
    if (holding || pulseAnimActive.current) return base.black;
    return base.white;
  };

  return (
    <TouchableWithoutFeedback
      onPressIn={disabled ? undefined : startFilling}
      onPressOut={disabled ? undefined : stopFilling}
    >
      <Animated.View
        style={[
          styles.buttonMain,
          {
            transform: [{scale: Animated.multiply(pulseAnim, scaleAnim)}],
            backgroundColor: getButtonBackgroundColor(),
          },
          disabled && styles.disabled,
        ]}
      >
        {executingOnPress ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={base.white} />
          </View>
        ) : (
          <>
            <Animated.View
              style={[
                styles.fill,
                {
                  height: fillAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                  backgroundColor: fillAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [success.success500, success.success900],
                  }),
                },
              ]}
            />
            <Animated.View
              style={[
                styles.expandingBubble,
                {
                  transform: [
                    {
                      scale: expandingBubbleAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [1, 2],
                      }),
                    },
                  ],
                  opacity: expandingBubbleOpacityAnim,
                },
              ]}
            />
            <View style={styles.textRow}>
              <View style={styles.iconContainer}>
                <Checkmark />
              </View>
              <Text style={[styles.text, {color: getTextColor()}]}>
                {t('holdToConfirm')}
              </Text>
            </View>
          </>
        )}
      </Animated.View>
    </TouchableWithoutFeedback>
  );
};

export default HoldToConfirmButton;

const styles = StyleSheet.create({
  buttonMain: {
    height: 70,
    width: 180,
    borderRadius: 8,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabled: {
    opacity: 0.5,
  },
  fill: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
  },
  expandingBubble: {
    position: 'absolute',
    width: 55,
    height: 55,
    borderRadius: 27.5,
    backgroundColor: 'rgba(255, 255, 255, 0.14)',
  },
  textRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginRight: 8,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
