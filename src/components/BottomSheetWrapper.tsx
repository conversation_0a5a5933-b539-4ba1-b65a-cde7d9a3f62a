import GlobalStyles from '@/constants/GlobalStyles';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import React, {
  ReactElement,
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {Platform, StyleSheet} from 'react-native';
import {useReducedMotion} from 'react-native-reanimated';

type BottomSheetWrapperProps = {
  isOpen: boolean;
  snapPoints: number[];
  children: ReactElement;
  backdrop?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  testID?: string;
};

/**
 * Wraps the `@gorhom/bottom-sheet` library
 * to more easily take advantage of it throughout the app
 *
 * Implementation:
 * 1. const snapPoints = useSnapPoints(percentage);
 * 2. <BottomSheetWrapper isOpen={isOpen} snapPoints={snapPoints}>
 */
const BottomSheetWrapper = forwardRef(
  (
    {
      isOpen,
      snapPoints,
      children,
      backdrop = true,
      onOpen,
      onClose,
      testID,
    }: BottomSheetWrapperProps,
    ref,
  ): ReactElement => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const reducedMotion = useMemo(() => useReducedMotion(), []);

    const [mounted, setMounted] = useState(false);

    // https://github.com/gorhom/react-native-bottom-sheet/issues/770#issuecomment-1072113936
    // Do not open the BottomSheet when swiping horizontally, so that a Swiper can be used within it
    const activeOffsetX = useMemo((): [number, number] => [-999, 999], []);
    const activeOffsetY = useMemo((): [number, number] => [-10, 10], []);

    useEffect(() => {
      if (isOpen) {
        bottomSheetRef.current?.snapToIndex(0);
      } else {
        bottomSheetRef.current?.close();
      }
      setTimeout(() => setMounted(true), 500);
    }, [isOpen]);

    useImperativeHandle(ref, () => ({
      snapToIndex(index: number = 0): void {
        bottomSheetRef.current?.snapToIndex(index);
      },
      expand(): void {
        bottomSheetRef.current?.snapToIndex(1);
      },
      close(): void {
        bottomSheetRef.current?.close();
      },
    }));

    const handleSheetChanges = useCallback(
      (index: number) => {
        if (index === -1) {
          onClose?.();
        } else {
          onOpen?.();
        }
      },
      [onClose, onOpen],
    );

    const renderBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => {
        if (!backdrop) {
          return null;
        }
        return (
          <BottomSheetBackdrop
            {...props}
            disappearsOnIndex={-1}
            appearsOnIndex={0}
            accessibilityLabel="Close"
          />
        );
      },
      [backdrop],
    );

    const style = useMemo(
      () => [styles.container, !mounted && {minHeight: snapPoints[0]! - 30}],
      [snapPoints, mounted],
    );

    // Determine initial `snapPoint`
    const index = useMemo((): number => (isOpen ? 0 : -1), [isOpen]);

    const platformProps = Platform.select({
      android: {
        enableOverDrag: false,
        enableContentPanningGesture: false,
        handleHeight: 24,
      },
      ios: {
        activeOffsetX,
        activeOffsetY,
      },
    });

    return (
      <BottomSheet
        ref={bottomSheetRef}
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={styles.handleIndicator}
        handleStyle={styles.handle}
        index={index}
        snapPoints={snapPoints}
        animateOnMount={!reducedMotion}
        enablePanDownToClose={true}
        keyboardBlurBehavior="restore"
        onChange={handleSheetChanges}
        {...platformProps}
      >
        <BottomSheetView style={style} testID={testID}>
          {children}
        </BottomSheetView>
      </BottomSheet>
    );
  },
);

export default memo(BottomSheetWrapper);

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    height: '100%',
    position: 'relative',
  },
  handleIndicator: {
    backgroundColor: GlobalStyles.gray.gray600,
  },
  handle: {
    alignSelf: 'center',
    height: 32,
    width: 32,
  },
});
