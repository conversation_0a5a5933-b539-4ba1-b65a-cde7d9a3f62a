import {useNavigation} from '@react-navigation/native';
import React, {useMemo} from 'react';
import {
  ImageSourcePropType,
  type ImageStyle,
  type StyleProp,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  type ViewProps,
} from 'react-native';
import type {AnimatedProps} from 'react-native-reanimated';
import Animated from 'react-native-reanimated';
import BlurView from './BlurView';

interface Props extends AnimatedProps<ViewProps> {
  style?: StyleProp<ImageStyle>;
  index?: number;
  rounded?: boolean;
  source?: ImageSourcePropType;
  title?: string;
  subtitle?: string;
}

export const PURPLE_IMAGES = [
  require('@/assets/images/purple-1.png'),
  require('@/assets/images/purple-2.png'),
];

export const SlideItem: React.FC<Props> = (props) => {
  const {
    style,
    index = 0,
    rounded = false,
    title = 'Need a Loan?',
    subtitle = 'Get a crypto-backed loan in 10 minutes',
    ...animatedViewProps
  } = props;

  const navigation = useNavigation<any>();

  const source = useMemo(
    () => props.source || PURPLE_IMAGES[index % PURPLE_IMAGES.length],
    [index, props.source],
  );

  const handlePress = () => {
    navigation.navigate('Subscribe');
  };

  return (
    <TouchableOpacity activeOpacity={0.9} onPress={handlePress} style={{flex: 1}}>
      <Animated.View style={{flex: 1}} {...animatedViewProps}>
        <Animated.Image
          style={[style, styles.container, rounded && {borderRadius: 15}]}
          source={source}
          resizeMode="cover"
        />
        <View style={styles.overlay}>
          <View style={styles.textContainerWrapper}>
            <View style={styles.textContainer}>
              <BlurView
                isBlurred
                rootStyle={[
                  StyleSheet.absoluteFillObject,
                  {backgroundColor: 'rgba(80, 0, 120, 0.2)'},
                ]}
                androidOverlayStyle={{
                  backgroundColor: 'transparent',
                }}
                blurIntensity={2}
              />
              <View style={styles.textContent}>
                <Text style={styles.title}>{title}</Text>
                <Text style={styles.subtitle}>{subtitle}</Text>
              </View>
            </View>
          </View>
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    padding: 20,
  },
  textContainerWrapper: {
    alignSelf: 'flex-start',
    marginTop: 'auto',
    marginBottom: 40,
  },
  textContainer: {
    padding: 16,
    borderRadius: 10,
    overflow: 'hidden',
    position: 'relative',
  },
  textContent: {
    position: 'relative',
    zIndex: 1,
  },
  title: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    color: 'white',
    fontSize: 18,
  },
});
