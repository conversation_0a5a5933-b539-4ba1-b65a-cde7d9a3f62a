import React, {forwardRef, useMemo, useState} from 'react';
import {
  TextInput as RNTextInput,
  StyleSheet,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {EyeIcon, EyeSlashIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {TextInput} from './TextInput';

type Props = {
  label: string;
  error?: string;
  styles?: {
    container?: ViewStyle;
    inputContainer?: ViewStyle;
    label?: TextStyle;
    input?: ViewStyle;
  };
  [key: string]: any;
};

export const PasswordInput = forwardRef<RNTextInput, Props>(
  ({label, error, styles: customStyles, ...inputProps}, ref) => {
    const [passwordVisible, setPasswordVisible] = useState(false);

    const togglePasswordVisibility = () => {
      setPasswordVisible(!passwordVisible);
    };

    const mergedStyles = useMemo(
      () => ({
        container: customStyles?.container,
        inputContainer: {
          ...customStyles?.inputContainer,
          color: GlobalStyles.gray.gray800,
          paddingRight: 8,
        },
        label: customStyles?.label,
        input: {
          ...customStyles?.input,
          paddingRight: 36,
        },
      }),
      [customStyles],
    );

    return (
      <View style={styles.container}>
        <TextInput
          ref={ref}
          label={label}
          error={error}
          secureTextEntry={!passwordVisible}
          styles={mergedStyles}
          placeholderTextColor={GlobalStyles.gray.gray600}
          textContentType="password"
          {...inputProps}
        />
        <TouchableOpacity
          style={styles.eyeIconContainer}
          onPress={togglePasswordVisibility}
          activeOpacity={0.7}
        >
          {passwordVisible ? (
            <EyeIcon size={26} color={GlobalStyles.gray.gray900} />
          ) : (
            <EyeSlashIcon size={26} color={GlobalStyles.gray.gray700} />
          )}
        </TouchableOpacity>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
  },
  eyeIconContainer: {
    position: 'absolute',
    right: 18,
    top: 20,
    height: 38,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
});
