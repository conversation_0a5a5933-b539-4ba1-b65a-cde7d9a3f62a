import {AuthenticationInstance} from '@/services/BackendServices';

type ForgotPasswordRequest = {
  email: string;
};

export const resetPassword = async ({email}: ForgotPasswordRequest) => {
  try {
    const {data} = await AuthenticationInstance.post('/user/reset-password', {
      email: email.trim().toLowerCase(),
    });
    return data;
  } catch (error: any) {
    throw new Error(error);
  }
};

export const fetchQrCode = async (accessToken): Promise<any> => {
  try {
    const response = await fetch('http://192.168.10.157/auth/user/otp/generate', {
      method: 'POST',
      headers: {
        accept: 'image/png',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('image/')) {
      const responseText = await response.text();
      throw new Error(
        `Expected image response, got: ${contentType}. Response: ${responseText}`,
      );
    }

    const blob = await response.blob();
    return new Promise<any>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve({data: reader.result as string});
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    console.error('QR Code fetch error:', error);
    throw error;
  }
};
