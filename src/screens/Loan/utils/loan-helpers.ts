export const validateEmailUsingRegex = (email: string): boolean => {
  const validEmailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return validEmailRegex.test(email.trim());
};

export const validateEmailWithMessage = (
  email: string,
): {isValid: boolean; message?: string} => {
  const trimmedEmail = email.trim();

  if (!trimmedEmail) {
    return {isValid: false, message: 'Email address is required'};
  }

  if (!validateEmailUsingRegex(trimmedEmail)) {
    return {isValid: false, message: 'Please enter a valid email address'};
  }

  return {isValid: true};
};
