import {useQuery} from '@tanstack/react-query';

import {getUserLoans} from './loan-queries';

export const useUserLoans = () => {
  const query = useQuery({
    queryKey: ['user-loans'],
    queryFn: getUserLoans,
  });
  return query;
};

/* -------------------------------------------------------------------------- */
/*                                 NOT IN USE                                 */
/* -------------------------------------------------------------------------- */

// import {createLoanWebHub} from '../_web-flow_/LoanWebHub';

// export const useWebLoanHub = () => {
//   const navigation = useNavigation<StackNavigationProp<LoanStackParamList>>();
//   const dispatch = useAppDispatch();

//   return useMemo(() => createLoanWebHub({navigation, dispatch}), [navigation, dispatch]);
// };
