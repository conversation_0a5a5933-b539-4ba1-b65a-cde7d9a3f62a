import {BottomSheetListData} from '@/components/BottomSheetList';

/* -------------------------------------------------------------------------- */
/*                                    APPLY                                   */
/* -------------------------------------------------------------------------- */

export type LoanDetailsFormProps = {
  onOpenBottomSheet: (data: BottomSheetListData & {title?: string}) => void;
  setLoanData: (data: any) => void;
  loanData: {
    borrowAmount: string;
    borrowCurrency: {
      fullName: string;
      label: string;
      type: string;
    };
    collateralAmount: string;
    collateralCurrency: {
      fullName: string;
      label: string;
      type: string;
    };
    loanToValue: string;
    interestPayment: string;
    term: string;
    errors: {
      borrowAmount?: string;
      collateralAmount?: string;
    };
  };
  getTokenPrice: (tokenSymbol: string) => number;
};

export enum ILoanState {
  PENDING_BANK_TERMS_APPROVAL = 'PENDING_BANK_TERMS_APPROVAL',
  PENDING_WALLET_CREATION = 'PENDING_WALLET_CREATION',

  PENDING_COLLATERAL_DEPOSIT_TRANSACTION = 'PENDING_COLLATERAL_DEPOSIT_TRANSACTION',
  PENDING_COLLATERAL_DEPOSIT_CONFIRMATION = 'PENDING_COLLATERAL_DEPOSIT_CONFIRMATION',

  ACTIVE = 'ACTIVE',

  PENDING_COLLATERAL_WITHDRAWAL_APPROVAL = 'PENDING_COLLATERAL_WITHDRAWAL_APPROVAL',
  PENDING_COLLATERAL_WITHDRAWAL_TRANSACTION = 'PENDING_COLLATERAL_WITHDRAWAL_TRANSACTION',
  PENDING_COLLATERAL_WITHDRAWAL_CONFIRMATION = 'PENDING_COLLATERAL_WITHDRAWAL_CONFIRMATION',

  COMPLETED = 'COMPLETED',
  TERMINATED = 'TERMINATED',

  REJECTED = 'REJECTED',
}

export type Loan = {
  borrowerUserId: string;
  createdAt: string;
  state: ILoanState;
  terms: {
    collateral: {
      amount: number;
      currency: string;
      btcAddress: string;
      ethAddress: string;
    };
    depositAddress: string;
    fiat: {
      amount: number;
      currency: string;
    };
    interest: 'DAILY' | 'MONTHLY' | 'YEARLY';
    ltv: number;
    proposedBy: string;
    term: number;
    _id: string;
  };
  updatedAt: string;
  _id: string;
};
