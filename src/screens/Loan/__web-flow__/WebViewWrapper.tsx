// import {useNavigation} from '@react-navigation/native';
// import {StackNavigationProp} from '@react-navigation/stack';
// import React, {useEffect, useRef, useState} from 'react';
// import {ActivityIndicator, StyleSheet, View} from 'react-native';
// import {WebView} from 'react-native-webview';

// import GlobalStyles from '@/constants/GlobalStyles';
// import {useAppSelector} from '@/hooks/redux';
// import {LoanStackParamList} from '@/navigation/types';
// import {BASE_URL} from '@/screens/Loan/helpers/loan-constants';
// import {useWebLoanHub} from '../helpers/loan-hooks';
// import {createTokenInjectionScript} from '../helpers/loan-utils';

// interface WebViewWrapperProps {
//   route: string;
//   requireAuth?: boolean;
// }

// const WebViewWrapper: React.FC<WebViewWrapperProps> = ({route, requireAuth = true}) => {
//   const webViewRef = useRef<WebView>(null);
//   const navigation = useNavigation<StackNavigationProp<LoanStackParamList>>();
//   const {accessToken, refreshToken} = useAppSelector((state) => state.loan);
//   const {handleWebViewMessage, handleWebViewError, handleWebViewHttpError} =
//     useWebLoanHub();

//   const [isLoading, setIsLoading] = useState(true);

//   useEffect(() => {
//     if (requireAuth && (!accessToken || !refreshToken)) {
//       navigation.replace('WebLoanLogin');
//     }
//   }, [accessToken, refreshToken, navigation, requireAuth]);

//   if (requireAuth && (!accessToken || !refreshToken)) {
//     return (
//       <View style={styles.loadingContainer}>
//         <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
//       </View>
//     );
//   }

//   return (
//     <View style={styles.container}>
//       {isLoading && (
//         <View style={styles.loadingContainer}>
//           <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
//         </View>
//       )}
//       <WebView
//         ref={webViewRef}
//         source={{
//           uri: `${BASE_URL}${route}`,
//           headers: accessToken
//             ? {
//                 Authorization: `Bearer ${accessToken}`,
//                 'Content-Type': 'application/json',
//                 Accept: 'application/json',
//               }
//             : undefined,
//         }}
//         style={[styles.webView, isLoading && styles.hidden]}
//         javaScriptEnabled={true}
//         domStorageEnabled={true}
//         thirdPartyCookiesEnabled={true}
//         cacheEnabled={false}
//         // incognito={true}
//         onShouldStartLoadWithRequest={(request) => {
//           const url = new URL(request.url);
//           const baseUrl = new URL(`${BASE_URL}`);
//           return url.origin === baseUrl.origin;
//         }}
//         injectedJavaScript={createTokenInjectionScript(accessToken, refreshToken)}
//         onMessage={handleWebViewMessage}
//         onError={handleWebViewError}
//         onHttpError={handleWebViewHttpError}
//         onLoadStart={() => setIsLoading(true)}
//         onLoadEnd={() => setIsLoading(false)}
//       />
//     </View>
//   );
// };

// export default WebViewWrapper;

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: GlobalStyles.base.white,
//   },
//   loadingContainer: {
//     ...StyleSheet.absoluteFillObject,
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: GlobalStyles.base.white,
//   },
//   webView: {
//     flex: 1,
//   },
//   hidden: {
//     opacity: 0,
//   },
// });
