import {setAccessToken, setRefreshToken} from '@/storage/actions/loanActions';
import {RootState} from '@/storage/store';
import {WEB_DASHBOARD_URL} from '@env';
import {Action, ThunkDispatch} from '@reduxjs/toolkit';

export const BASE_URL = `${WEB_DASHBOARD_URL}`;

export const WEB_ROUTES = {
  REGISTER: '/register',
  LOGIN: '/login',
  CONFIRM_LOAN: '/loan-confirm',
  DASHBOARD: '/loan-dashboard',
} as const;

/**
 * Encodes a JSON object to base64url format.
 *
 * This implementation:
 * 1. Converts object to JSON string
 * 2. Encodes to base64
 * 3. Makes it URL-safe by replacing '+' with '-', '/' with '_', and removing padding '='
 */
export const encodeObjectToBase64Url = (obj: Record<string, any>): string => {
  const jsonString = JSON.stringify(obj);
  return Buffer.from(jsonString)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
};

export const storeTokens = async (
  dispatch: ThunkDispatch<RootState, undefined, Action>,
  accessToken: string,
  refreshToken: string,
): Promise<void> => {
  await Promise.all([
    dispatch(setAccessToken(accessToken)),
    dispatch(setRefreshToken(refreshToken)),
  ]);
};

/**
 * TEMP: Currently the following helpers are not in use.
 */

export const setupWebViewBridge = `
  window.setupWebViewBridge = function() {
    if (window.WebViewBridge) return;
    
    window.WebViewBridge = {
      send: function(message) {
        if (window.ReactNativeWebView && typeof window.ReactNativeWebView.postMessage === 'function') {
          window.ReactNativeWebView.postMessage(JSON.stringify(message));
        }
      },
      onMessage: function(callback) {
        window.webViewBridgeCallback = callback;
      }
    };
    
    // Notify that bridge is ready
    window.dispatchEvent(new CustomEvent('webViewBridgeReady'));
  };
`;

export const createTokenInjectionScript = (accessToken: string, refreshToken: string) => `
  (function() {
    // Immediately set tokens before any React code runs
    sessionStorage.setItem('accessToken', '${accessToken}');
    sessionStorage.setItem('refreshToken', '${refreshToken}');

    // Mock the auth service to always return success
    window.authenticationService = {
      verifyToken: function() {
        return Promise.resolve({ data: { success: true } });
      },
      refreshAccessToken: function() {
        return Promise.resolve({ data: { accessToken: '${accessToken}' } });
      }
    };

    // Setup WebView bridge
    window.WebViewBridge = {
      send: function(message) {
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(JSON.stringify(message));
        }
      }
    };

    // Add auth headers to all requests
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      options.headers = options.headers || {};
      options.headers['Authorization'] = 'Bearer ${accessToken}';
      
      return originalFetch(url, options).then(response => {
        if (response.status === 401) {
          window.WebViewBridge.send({ type: 'ERROR', payload: { code: 'AUTH_ERROR' }});
        }
        return response;
      });
    };
  })();
`;
