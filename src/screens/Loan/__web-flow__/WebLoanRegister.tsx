// import React, {memo} from 'react';
// import {StyleSheet, View} from 'react-native';

// import {WEB_ROUTES} from '../helpers/loan-constants';
// import {encodeObjectToBase64Url} from '../helpers/loan-utils';
// import WebViewWrapper from './WebViewWrapper';

// const WebLoanRegister: React.FC = ({route}: any) => {
//   const encodedData = encodeObjectToBase64Url(route.params);

//   return (
//     <View style={styles.container}>
//       <View style={styles.webViewContainer}>
//         <WebViewWrapper
//           route={`${WEB_ROUTES.REGISTER}?loan=${encodedData}`}
//           requireAuth={false}
//         />
//       </View>
//     </View>
//   );
// };

// export default memo(WebLoanRegister);

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: 'white',
//   },
//   webViewContainer: {
//     flex: 1,
//     marginBottom: 20,
//   },
// });
