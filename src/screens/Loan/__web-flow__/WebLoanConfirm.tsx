// import {memo, useEffect} from 'react';
// import {Linking, StyleSheet, Text, View} from 'react-native';

// import {AssetifyWithText} from '@/assets';
// import GlobalStyles from '@/constants/GlobalStyles';
// import {useAppDispatch, useAppSelector} from '@/hooks/redux';
// import {setAccessToken, setIsRegistered} from '@/storage/actions/loanActions';
// import {BASE_URL, WEB_ROUTES} from '../helpers/loan-constants';

// const WebLoanConfirm = ({navigation}: any) => {
//   const dispatch = useAppDispatch();
//   const {accessToken, refreshToken} = useAppSelector((state) => state.loan);

//   if (!accessToken || !refreshToken) {
//     return;
//   }

//   console.log('[WebLoanConfirm] accessToken:', accessToken);

//   const handleRedirect = async () => {
//     dispatch(setIsRegistered(true));
//     dispatch(setAccessToken(accessToken));

//     const state = encodeURIComponent(
//       JSON.stringify({
//         accessToken,
//         refreshToken,
//       }),
//     );
//     const confirmUrl = `${BASE_URL}${WEB_ROUTES.CONFIRM_LOAN}?state=${state}`;
//     console.log('[WebLoanConfirm] Confirm URL:', confirmUrl);
//     await Linking.openURL(confirmUrl);
//     navigation.replace('LoanCompleteWebFlow');
//   };

//   useEffect(() => {
//     handleRedirect();
//   }, [accessToken, refreshToken, navigation]);

//   return (
//     <View style={styles.container}>
//       <View style={styles.content}>
//         <AssetifyWithText width={130} height={130} />
//         <Text style={styles.text}>Redirecting you to confirm your loan...</Text>
//       </View>
//     </View>
//   );
// };

// export default memo(WebLoanConfirm);

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: GlobalStyles.base.white,
//   },
//   content: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     gap: 24,
//   },
//   text: {
//     fontFamily: GlobalStyles.fonts.sfPro,
//     color: GlobalStyles.gray.gray900,
//     fontSize: 17,
//     textAlign: 'center',
//     paddingHorizontal: 42,
//   },
// });
