// import React from 'react';
// import {StyleSheet, Text, View} from 'react-native';
// import {useSafeAreaInsets} from 'react-native-safe-area-context';

// import {AssetifyWithText} from '@/assets';
// import GlobalStyles from '@/constants/GlobalStyles';

// const LoanCompleteWebFlow = () => {
//   const insets = useSafeAreaInsets();

//   return (
//     <View
//       style={[
//         styles.container,
//         {
//           paddingTop: insets.top,
//           paddingBottom: insets.bottom,
//         },
//       ]}
//     >
//       <View style={styles.content}>
//         <AssetifyWithText width={130} height={130} />

//         <Text style={styles.message}>
//           Please complete the forms opened in your browser
//         </Text>

//         <Text style={styles.submessage}>
//           You will be redirected to the app once you're done.
//         </Text>
//       </View>
//     </View>
//   );
// };

// export default LoanCompleteWebFlow;

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: GlobalStyles.base.white,
//   },
//   content: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     paddingHorizontal: 42,
//   },
//   message: {
//     marginTop: 22,
//     color: GlobalStyles.gray.gray900,
//     fontSize: 18,
//     fontWeight: '600',
//     textAlign: 'center',
//     letterSpacing: 0.5,
//   },
//   submessage: {
//     marginTop: 12,
//     color: GlobalStyles.gray.gray700,
//     fontSize: 17,
//     textAlign: 'center',
//   },
// });
