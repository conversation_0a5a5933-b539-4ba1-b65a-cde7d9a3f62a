// import React, {memo} from 'react';
// import {StyleSheet, View} from 'react-native';

// import {WEB_ROUTES} from '../helpers/loan-constants';
// import WebViewWrapper from './WebViewWrapper';

// const WebLoanLogin: React.FC = () => {
//   return (
//     <View style={styles.container}>
//       <View style={styles.webViewContainer}>
//         <WebViewWrapper route={WEB_ROUTES.LOGIN} requireAuth={false} />
//       </View>
//     </View>
//   );
// };

// export default memo(WebLoanLogin);

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
//   webViewContainer: {
//     flex: 1,
//   },
// });
