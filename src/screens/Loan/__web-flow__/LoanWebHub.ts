// import {StackNavigationProp} from '@react-navigation/stack';
// import {Dispatch} from '@reduxjs/toolkit';
// import {Linking} from 'react-native';

// import {LoanStackParamList} from '@/navigation/types';
// import {
//   setAccessToken,
//   setIsRegistered,
//   setRefreshToken,
//   setTokenTimestamp,
// } from '@/storage/actions/loanActions';
// import {store} from '@/storage/store';
// import {BASE_URL, WEB_ROUTES} from '../helpers/loan-constants';
// import {storeTokens} from '../helpers/loan-utils';

// export interface LoanWebHubConfig {
//   navigation: StackNavigationProp<LoanStackParamList>;
//   dispatch: Dispatch;
// }

// export interface WebViewMessage {
//   type: WebViewMessageType;
//   payload: Record<string, unknown>;
// }

// export interface AuthSuccessPayload {
//   accessToken: string;
//   refreshToken: string;
//   hasActiveLoan: boolean;
// }

// export interface TokenUpdatePayload {
//   key: string;
//   value: string;
// }

// export type WebViewMessageType =
//   | 'AUTH_SUCCESS'
//   | 'IS_REGISTERED'
//   | 'TOKEN_UPDATED'
//   | 'ERROR';

// export const createLoanWebHub = ({navigation, dispatch}: LoanWebHubConfig) => {
//   const handleWebViewMessage = async (event: {nativeEvent: {data: string}}) => {
//     try {
//       const message = JSON.parse(event.nativeEvent.data) as WebViewMessage;
//       console.log('[LoanWebHub] Received message:', message);

//       switch (message.type) {
//         case 'AUTH_SUCCESS': {
//           const payload = message.payload as unknown as AuthSuccessPayload;
//           const {accessToken, refreshToken} = payload;

//           const {hasLoan} = store.getState().loan;

//           await storeTokens(dispatch, accessToken, refreshToken);
//           dispatch(setIsRegistered(true));

//           if (!store.getState().loan.tokenTimestamp) {
//             const now = Date.now();
//             dispatch(setTokenTimestamp(now));
//           }

//           if (hasLoan) {
//             navigation.replace('LoanList');
//           } else {
//             const state = encodeURIComponent(
//               JSON.stringify({
//                 accessToken,
//                 refreshToken,
//               }),
//             );
//             const confirmUrl = `${BASE_URL}${WEB_ROUTES.CONFIRM_LOAN}?state=${state}`;
//             console.log('[LoanWebHub] Confirm URL:', confirmUrl);
//             await Linking.openURL(confirmUrl);

//             navigation.replace('LoanCompleteWebFlow');
//           }
//           break;
//         }

//         case 'IS_REGISTERED': {
//           dispatch(setIsRegistered(true));
//           break;
//         }

//         case 'TOKEN_UPDATED': {
//           const {key, value} = message.payload as unknown as TokenUpdatePayload;

//           if (key === 'accessToken') {
//             console.log('[LoanWebHub] Updating access token');
//             dispatch(setAccessToken(value));
//           } else if (key === 'refreshToken') {
//             console.log('[LoanWebHub] Updating refresh token');
//             dispatch(setRefreshToken(value));
//           }
//           break;
//         }

//         case 'ERROR':
//           if (message.payload?.code === 'AUTH_ERROR') {
//             console.log('[LoanWebHub] Auth error detected, redirecting to login');
//             navigation.replace('WebLoanLogin');
//           }
//           break;

//         default:
//           console.warn('[LoanWebHub] Unhandled message type:', message.type);
//       }
//     } catch (error) {
//       console.error('[LoanWebHub] Failed to handle WebView message:', error);
//     }
//   };

//   const handleWebViewError = (syntheticEvent: any): void => {
//     const {nativeEvent} = syntheticEvent;
//     console.warn('[LoanWebHub] WebView error:', nativeEvent);

//     if (
//       nativeEvent.description?.includes('ERR_FAILED') ||
//       nativeEvent.description?.includes('ERR_CONNECTION_REFUSED')
//     ) {
//       console.log('[LoanWebHub] Critical WebView error, redirecting to login');
//       navigation.replace('WebLoanLogin');
//     }
//   };

//   const handleWebViewHttpError = (syntheticEvent: any): void => {
//     const {nativeEvent} = syntheticEvent;
//     console.warn('[LoanWebHub] WebView HTTP error:', nativeEvent);

//     // Handle HTTP errors (e.g., 401 Unauthorized)
//     if (nativeEvent.statusCode === 401 || nativeEvent.statusCode === 403) {
//       console.log('[LoanWebHub] Auth-related HTTP error, redirecting to login');
//       navigation.replace('WebLoanLogin');
//     }
//   };

//   return {
//     handleWebViewMessage,
//     handleWebViewError,
//     handleWebViewHttpError,
//   };
// };
