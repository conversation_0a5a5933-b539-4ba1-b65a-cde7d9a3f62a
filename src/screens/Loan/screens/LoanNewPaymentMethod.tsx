import React, {memo, useCallback, useState} from 'react';
import {Image, StyleSheet, Text, TextInput, View} from 'react-native';

import MastercardIcon from '@/assets/logo/mastercard.svg';
import VisaIcon from '@/assets/logo/visa.svg';
import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import MButton from '@/components/MButton';
import SafeAreaInset from '@/components/SafeAreaInset';
import SelectCountryOption from '@/components/SelectCountryOption';
import SelectOption from '@/components/SelectOption';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {CURRENCY_OPTIONS, DEFAULT_CARD_DATA} from './apply-to-a-loan/utils';

type CardData = {
  label: string;
  cardNumber: string;
  cardHolder: string;
  country: {
    code: string;
    name: string;
  };
  currency: {
    label: string;
    value: string;
  };
  loanPurpose: string;
};

const WorldMapBackground = memo(() => (
  <View style={styles.worldMapContainer}>
    <Image
      source={require('@/assets/images/world-map-dots.png')}
      style={styles.worldMap}
      resizeMode="cover"
    />
  </View>
));

const LoanNewPaymentMethod = ({navigation}: {navigation: any}) => {
  const {open: openSheet} = useBottomSheet('list');

  const [cardData, setCardData] = useState<CardData>(DEFAULT_CARD_DATA);
  const [cardType, setCardType] = useState<'visa' | 'mastercard' | null>(null);

  const handleCardNumberChange = (value: string) => {
    const formatted = value
      .replace(/\s/g, '')
      .replace(/(\d{4})/g, '$1 ')
      .trim();
    setCardData((prev) => ({...prev, cardNumber: formatted}));
    detectCardType(formatted);
  };

  const detectCardType = (number: string) => {
    const cleanNumber = number.replace(/\s/g, '');
    if (cleanNumber.startsWith('4')) {
      setCardType('visa');
    } else if (/^5[1-5]/.test(cleanNumber)) {
      setCardType('mastercard');
    } else {
      setCardType(null);
    }
  };

  const handleCountryPress = useCallback(() => {
    openSheet(
      {
        type: 'simple',
        data: [],
        onSelect: (country: any) => {
          setCardData((prev) => ({
            ...prev,
            country: {
              code: country.value,
              name: country.label,
            },
          }));
        },
      },
      60,
      true,
    );
  }, [openSheet]);

  const handleCurrencyPress = useCallback(() => {
    openSheet(
      {
        type: 'currency',
        data: CURRENCY_OPTIONS.fiat,
        onSelect: (currency: any) => {
          setCardData((prev) => ({
            ...prev,
            currency: {
              label: currency.label,
              value: currency.value,
            },
          }));
        },
      },
      60,
      true,
    );
  }, [openSheet]);

  const handleSendCollateralPress = () => {
    navigation.reset({
      index: 0,
      routes: [{name: 'LoanCollateral'}],
    });
  };

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView style={styles.keyboardAvoidingView}>
        <View style={styles.content}>
          <View style={styles.card}>
            <WorldMapBackground />

            <View style={styles.cardContent}>
              <View style={styles.cardHeader}>
                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Card Label</Text>
                  <TextInput
                    style={styles.labelInput}
                    placeholder="My Personal Card"
                    value={cardData.label}
                    onChangeText={(text) =>
                      setCardData((prev) => ({...prev, label: text}))
                    }
                    placeholderTextColor={GlobalStyles.gray.gray700}
                  />
                </View>

                {cardType && (
                  <View style={styles.cardLogoContainer}>
                    {cardType === 'mastercard' && (
                      <MastercardIcon width={40} height={25} />
                    )}
                    {cardType === 'visa' && <VisaIcon width={40} height={30} />}
                  </View>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Card Number</Text>
                <TextInput
                  style={styles.cardNumberInput}
                  placeholder="Enter your card number"
                  value={cardData.cardNumber}
                  onChangeText={handleCardNumberChange}
                  keyboardType="numeric"
                  maxLength={19}
                  placeholderTextColor={GlobalStyles.gray.gray700}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Cardholder Name</Text>
                <TextInput
                  style={styles.cardHolderInput}
                  placeholder="Enter cardholder name"
                  value={cardData.cardHolder}
                  onChangeText={(text) =>
                    setCardData((prev) => ({...prev, cardHolder: text.toUpperCase()}))
                  }
                  placeholderTextColor={GlobalStyles.gray.gray700}
                  autoCapitalize="characters"
                />
              </View>
            </View>
          </View>

          <View style={styles.selectionSection}>
            <View style={styles.selectionRow}>
              <View style={styles.selectionField}>
                <SelectCountryOption
                  countryCode={cardData.country.code}
                  countryName={cardData.country.name}
                  onPress={handleCountryPress}
                />
              </View>

              <View style={styles.selectionField}>
                <SelectOption
                  label="Receiving Currency"
                  option={cardData.currency.label}
                  onPress={handleCurrencyPress}
                />
              </View>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>

      <View style={styles.footer}>
        <MButton text="Send Collateral" onPress={handleSendCollateralPress} />
      </View>

      <SafeAreaInset type="bottom" />
    </View>
  );
};

export default memo(LoanNewPaymentMethod);

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    padding: 16,
  },
  card: {
    width: '100%',
    aspectRatio: 1.586,
    borderRadius: 20,
    backgroundColor: GlobalStyles.base.white,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 1,
  },
  worldMapContainer: {
    position: 'absolute',
    width: '70%',
    height: '100%',
    right: '-2%',
    top: '25%',
    opacity: 0.6,
  },
  worldMap: {
    width: '100%',
    height: '60%',
    transform: [{scale: 1.2}, {translateX: -20}],
  },
  cardContent: {
    padding: 22,
    flex: 1,
    position: 'relative',
    zIndex: 1,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  cardLogoContainer: {
    backgroundColor: 'white',
    padding: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    width: 60,
    height: 45,
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelInput: {
    fontSize: 16,
    fontWeight: '600',
    height: 30,
  },
  inputContainer: {},
  inputLabel: {
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 13,
    color: GlobalStyles.gray.gray900,
    marginBottom: 4,
    fontWeight: '500',
  },
  cardNumberInput: {
    fontSize: 16,
    letterSpacing: 2,
    color: GlobalStyles.base.black,
    fontWeight: '500',
    marginBottom: 16,
    height: 30,
  },
  cardHolderInput: {
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontWeight: '500',
    marginBottom: 16,
    textTransform: 'uppercase',
    height: 30,
  },
  selectionSection: {
    marginTop: 16,
  },
  selectionRow: {
    flexDirection: 'column',
  },
  selectionField: {
    flex: 1,
    marginBottom: 86,
  },
  footer: {
    width: '90%',
    alignSelf: 'center',
    paddingBottom: 12,
  },
});
