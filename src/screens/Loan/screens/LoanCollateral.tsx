import {useQuery} from '@tanstack/react-query';
import React, {memo} from 'react';
import {Image, Share, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import CopyIcon from '@/assets/icons/copy.svg';
import ShareIcon from '@/assets/icons/profit.svg';
import AssetifyLogo from '@/assets/logo/Logo.svg';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAppSelector} from '@/hooks/redux';
import {fetcher} from '@/screens/CurrencySpecificScreen/ReceiveAsset/receiveAssetUtils';
import {setHasLoan} from '@/storage/actions/loanActions';
import {handleCopy} from '@/utils/index';
import {QR_CODE_BACKEND_SERVICE} from '@env';

const QR_CODE_URL = `${QR_CODE_BACKEND_SERVICE}/qr`;

const LoanCollateral = ({navigation}: any) => {
  const dispatch = useAppDispatch();

  const mpcComputedAddress = useAppSelector((state) => state.loan.mpcComputedAddress);

  const {
    data: qrCode,
    isLoading: isQrCodeLoading,
    error: qrCodeError,
    refetch: refetchQrCode,
  } = useQuery({
    queryKey: ['qrCode', mpcComputedAddress ? mpcComputedAddress : null],
    queryFn: () =>
      fetcher(
        typeof mpcComputedAddress === 'string'
          ? [QR_CODE_URL, JSON.stringify({data: mpcComputedAddress})]
          : [QR_CODE_URL, JSON.stringify({data: mpcComputedAddress})],
      ),
    enabled: !!mpcComputedAddress,
  });

  const handleShareAddress = async () => {
    try {
      await Share.share({
        message: mpcComputedAddress,
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleButtonPress = () => {
    dispatch(setHasLoan(true));

    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'BottomTabs',
          params: {screen: 'Loan', params: {screen: 'LoanList'}},
        },
      ],
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <AssetifyLogo width={40} height={40} />
      </View>

      <View style={styles.content}>
        <View style={styles.qrBox}>
          <TouchableOpacity
            onPress={() => handleCopy(mpcComputedAddress)}
            style={styles.qrButton}
            activeOpacity={0.7}
          >
            <Image style={styles.qrCode} source={{uri: qrCode?.data}} />
          </TouchableOpacity>
        </View>

        <View style={styles.addressWrapper}>
          <View style={styles.addressContainer}>
            <Text style={styles.addressText}>{mpcComputedAddress}</Text>
          </View>

          <View style={styles.actionsContainer}>
            <TouchableOpacity
              onPress={() => handleCopy(mpcComputedAddress)}
              style={styles.actionButton}
              activeOpacity={0.7}
            >
              <CopyIcon width={24} height={24} color={GlobalStyles.gray.gray600} />
              <Text style={styles.actionText}>Copy</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleShareAddress}
              style={styles.actionButton}
              activeOpacity={0.7}
            >
              <ShareIcon width={24} height={24} color={GlobalStyles.gray.gray600} />
              <Text style={styles.actionText}>Share</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.transferSection}>
          <Text style={styles.transferTitle}>Waiting for your transfer</Text>
          <Text style={styles.transferDescription}>
            Scan or copy the address above to send a transfer using your Assetify Wallet
            or any external wallet.
          </Text>
        </View>
      </View>

      <View style={styles.footer}>
        <MButton onPress={handleButtonPress} text="Continue" />
      </View>
    </SafeAreaView>
  );
};

export default memo(LoanCollateral);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  header: {
    paddingVertical: 14,
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  content: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  qrBox: {
    width: '100%',
    aspectRatio: 1.5,
    backgroundColor: '#353839',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
  },
  qrButton: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 12,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  qrCode: {
    width: 160,
    height: 160,
    resizeMode: 'contain',
    borderRadius: 8,
  },
  addressWrapper: {
    marginHorizontal: 16,
    marginTop: 14,
  },
  addressContainer: {
    paddingVertical: 10,
    borderRadius: 12,
    backgroundColor: GlobalStyles.gray.gray50,
    alignItems: 'center',
  },
  addressText: {
    fontSize: 15,
    color: GlobalStyles.base.black,
    fontFamily: 'monospace',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
    gap: 32,
  },
  actionButton: {
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  transferSection: {
    paddingHorizontal: 24,
    marginTop: 24,
  },
  transferTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 8,
  },
  transferDescription: {
    fontSize: 16,
    lineHeight: 22,
    color: GlobalStyles.gray.gray800,
    marginBottom: 32,
  },
  footer: {
    width: '90%',
    alignSelf: 'center',
    paddingBottom: 18,
  },
});
