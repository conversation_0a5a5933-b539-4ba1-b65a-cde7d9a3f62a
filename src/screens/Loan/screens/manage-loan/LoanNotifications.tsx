import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';

import theme from '@/styles/themes';

const LoanNotifications: React.FC = () => {
  return (
    <View style={styles.root}>
      <View style={styles.content}></View>
    </View>
  );
};

export default memo(LoanNotifications);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.layout.gap.screen,
    marginTop: 52,
  },
});
