import {RouteProp, useRoute} from '@react-navigation/native';
import React, {useCallback, useMemo} from 'react';
import {ActivityIndicator, Alert, ScrollView, StyleSheet, Text, View} from 'react-native';
import Pie<PERSON>hart from 'react-native-pie-chart';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {LoanStackParamList} from '@/navigation/types';
import {formatNumber} from '@/utils/index';
import {useUserLoans} from '../../utils/loan-hooks';
import {Loan} from '../../utils/loan-types';

type LoanDashboardRouteProp = RouteProp<LoanStackParamList, 'LoanDashboard'>;

const LoanDashboard = () => {
  const route = useRoute<LoanDashboardRouteProp>();
  const {loanId} = route.params;

  const [timeRange, setTimeRange] = '1W';

  const {data: loans, isLoading} = useUserLoans();

  const chartData = useMemo(() => {
    const riskPercentage = 57;

    const getRiskColor = (percentage: number) => {
      if (percentage <= 60) {
        return '#10B981';
      } else {
        const intensity = (percentage - 60) / 20;

        if (intensity <= 0.33) {
          return '#EF4444';
        } else if (intensity <= 0.66) {
          return '#DC2626';
        } else {
          return '#B91C1C';
        }
      }
    };

    const riskColor = getRiskColor(riskPercentage);

    const maxRisk = 100;
    const adjustedRiskPercentage = Math.min(riskPercentage, maxRisk);
    const remainingPercentage = maxRisk - adjustedRiskPercentage;

    const rawData = [
      {
        value: adjustedRiskPercentage,
        color: riskColor,
        label: 'Current Risk',
        displayValue: `${riskPercentage}%`,
      },
      {
        value: remainingPercentage,
        color: GlobalStyles.gray.gray600,
        label: 'Safe Zone',
        displayValue: `${remainingPercentage}%`,
      },
    ];

    return {
      series: rawData.map((item) => ({value: item.value, color: item.color})),
      labels: rawData.map((item) => item.label),
      values: rawData.map((item) => item.displayValue),
      colors: rawData.map((item) => item.color),
      riskPercentage,
      riskColor,
    };
  }, []);

  const mockEthData = useMemo((): any[] => {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    const basePrice = 3500;
    const volatility = 200;

    const dataPoints: number =
      timeRange === '24H'
        ? 24
        : timeRange === '1W'
        ? 7 * 24
        : timeRange === '1M'
        ? 30 * 8
        : timeRange === '1Y'
        ? 365
        : 500;

    const timeInterval =
      timeRange === '24H'
        ? oneDayMs / 24
        : timeRange === '1W'
        ? oneDayMs
        : timeRange === '1M'
        ? oneDayMs
        : timeRange === '1Y'
        ? oneDayMs * 7
        : oneDayMs * 30;

    return Array.from({length: dataPoints}).map((_, i) => {
      const timestamp = now - (dataPoints - i - 1) * timeInterval;
      // Create some randomness but with a trend
      const randomFactor = Math.sin(i / (dataPoints / 10)) * 0.5 + Math.random() * 0.5;
      const price = basePrice + randomFactor * volatility;
      return {timestamp, price};
    });
  }, [timeRange]);

  const loan = useMemo(() => {
    if (!loans) return null;
    return loans.find((l: Loan) => l._id === loanId);
  }, [loans, loanId]);

  const calculateCollateralValue = (loan: Loan) => {
    if (!loan) return 0;

    if (loan.terms.collateral.currency === 'ETH') {
      return loan.terms.collateral.amount;
    }

    if (loan.terms.collateral.currency === 'BTC') {
      return loan.terms.collateral.amount * 60000;
    }

    return 0;
  };

  const calculateCurrentLTV = (loan: Loan) => {
    if (!loan) return 0;

    const loanAmount = loan.terms.fiat.amount;
    const collateralValue = calculateCollateralValue(loan);

    if (collateralValue === 0) return 0;

    return (loanAmount / collateralValue) * 100;
  };

  const handleAddCollateral = useCallback(() => {
    Alert.alert('Add Collateral', 'This feature is not implemented yet.');
  }, []);

  const handleRepayLoan = useCallback(() => {
    Alert.alert('Repay Loan', 'This feature is not implemented yet.');
  }, []);

  const handleRequestSupport = useCallback(() => {
    Alert.alert('Request Support', 'This feature is not implemented yet.');
  }, []);

  if (isLoading || !loan) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
        <Text style={styles.loadingText}>Loading loan details...</Text>
      </View>
    );
  }

  const collateralValue = calculateCollateralValue(loan);
  const currentLTV = calculateCurrentLTV(loan);
  const isLTVHigh = currentLTV > 80;

  const nextPaymentDate = new Date();
  nextPaymentDate.setDate(nextPaymentDate.getDate() + 30);
  const formattedNextPaymentDate = nextPaymentDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Your LTV</Text>
        <View style={styles.chartWrapper}>
          <PieChart
            widthAndHeight={200}
            series={chartData.series}
            cover={{radius: 0.6, color: '#FFF'}}
          />

          <View style={styles.centerValueContainer}>
            <Text style={[styles.centerValue, {color: chartData.riskColor}]}>
              {chartData.values[0]}
            </Text>
            <Text style={styles.riskStatus}>
              {chartData.riskPercentage <= 60 ? 'SAFE' : 'HIGH RISK'}
            </Text>
          </View>
        </View>

        {/* <View style={styles.chartLabels}>
          {chartData.labels.map((label, index) => (
            <View key={index} style={styles.labelItem}>
              <View
                style={[styles.labelColor, {backgroundColor: chartData.colors[index]}]}
              />
              <Text style={styles.labelText}>{label}</Text>
              <Text style={styles.labelValue}>{chartData.values[index]}</Text>
            </View>
          ))}
        </View> */}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Loan Summary</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Borrowed Amount</Text>
          <Text style={styles.detailValue}>
            {formatNumber(loan.terms.fiat.amount, {decimals: 2})}{' '}
            {loan.terms.fiat.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Interest Rate</Text>
          <Text style={styles.detailValue}>{loan.terms.interest}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Collateral</Text>
          <Text style={styles.detailValue}>
            {formatNumber(loan.terms.collateral.amount)} {loan.terms.collateral.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Loan Term</Text>
          <Text style={styles.detailValue}>{loan.terms.term} months</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Total Repayment Amount</Text>
          <Text style={styles.detailValue}>
            {loan.terms.fiat.amount.toLocaleString()} {loan.terms.fiat.currency}
          </Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Loan Activity</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Next Payment Due</Text>
          <Text style={styles.detailValue}>{formattedNextPaymentDate}</Text>
        </View>

        <View style={styles.emptyActivity}>
          <Text style={styles.emptyText}>No payment history available</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Collateral Monitoring</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Current Collateral Value</Text>
          <Text style={styles.detailValue}>
            {formatNumber(collateralValue, {decimals: 2})} {loan.terms.fiat.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Current LTV</Text>
          <Text style={[styles.detailValue, isLTVHigh && styles.warningText]}>
            {currentLTV.toFixed(2)}%
          </Text>
        </View>

        {isLTVHigh && (
          <View style={styles.warningContainer}>
            <Text style={styles.warningText}>
              LTV is too high; please consider depositing additional collateral to avoid
              liquidation.
            </Text>
          </View>
        )}
      </View>

      <View style={styles.actionContainer}>
        <MButton
          text="Add Collateral"
          onPress={handleAddCollateral}
          style={styles.actionButton}
          testID="add-collateral-button"
        />

        <MButton
          text="Repay Loan"
          onPress={handleRepayLoan}
          style={styles.actionButton}
          testID="repay-loan-button"
        />

        <MButton
          text="Request Support"
          onPress={handleRequestSupport}
          style={styles.actionButton}
          testID="request-support-button"
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
  },
  chartContainer: {
    marginVertical: 42,
    paddingHorizontal: 16,
  },
  chartTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
    marginBottom: 24,
  },
  chartWrapper: {
    alignItems: 'center',
    position: 'relative',
  },
  centerValueContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  centerValue: {
    fontSize: 22,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
  },
  riskStatus: {
    fontSize: 10,
    fontWeight: '600',
    color: GlobalStyles.gray.gray800,
    textAlign: 'center',
    marginTop: 4,
    letterSpacing: 1,
  },
  chartLabels: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  labelItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  labelColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  labelText: {
    flex: 1,
    fontSize: 14,
    color: GlobalStyles.gray.gray700,
    fontWeight: '500',
  },
  labelValue: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  centerLabel: {
    fontSize: 12,
    color: GlobalStyles.gray.gray600,
    textAlign: 'center',
    marginTop: 4,
  },
  chartLegend: {
    marginTop: 24,
    width: '100%',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  legendText: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  legendLabel: {
    fontSize: 14,
    color: GlobalStyles.gray.gray700,
  },
  legendValue: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusActive: {
    backgroundColor: GlobalStyles.success.success500,
  },
  statusOverdue: {
    backgroundColor: '#F59E0B',
  },
  statusCompleted: {
    backgroundColor: GlobalStyles.gray.gray500,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  section: {
    padding: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  warningText: {
    color: '#F59E0B',
  },
  warningContainer: {
    backgroundColor: '#FEF3C7',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  emptyActivity: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  emptyText: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  actionContainer: {
    width: '90%',
    alignSelf: 'center',
    marginBottom: 32,
  },
  actionButton: {
    marginBottom: 12,
    height: 48,
  },
});

export default LoanDashboard;
