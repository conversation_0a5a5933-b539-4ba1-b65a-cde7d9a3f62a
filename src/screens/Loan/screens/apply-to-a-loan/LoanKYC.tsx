import {useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useMemo, useState} from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';

import IdentityCardIcon from '@/assets/icons/identity-card.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {useKyc} from '../../components/kyc/KycContext';
import KycStepIndicator, {Step} from '../../components/KycStepIndicator';
// Form components
import AssetsInfoForm from '../../components/kyc/forms/AssetsInfoForm';
import IdentityVerificationForm from '../../components/kyc/forms/IdentityVerificationForm';
import PersonalInfoForm from '../../components/kyc/forms/PersonalInfoForm';
import ResidentialInfoForm from '../../components/kyc/forms/ResidentialInfoForm';

const LoanKYC: React.FC = () => {
  const {submitKycData} = useKyc();
  const navigation = useNavigation();

  const [currentStep, setCurrentStep] = useState(0);

  const steps = useMemo<Step[]>(
    () => [
      {
        id: 1,
        title: 'Personal information',
        component: PersonalInfoForm,
      },
      {
        id: 2,
        title: 'Residential information',
        component: ResidentialInfoForm,
      },
      {
        id: 3,
        title: 'Your assets',
        component: AssetsInfoForm,
      },
      {
        id: 4,
        title: 'ID verification',
        component: IdentityVerificationForm,
      },
    ],
    [],
  );

  const handleNext = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmit();
    }
  }, [currentStep, steps.length]);

  const handleBack = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  }, [currentStep, navigation]);

  const handleSubmit = useCallback(async () => {
    const success = await submitKycData();
    if (success) {
      navigation.navigate('KYCComplete');
    }
  }, [submitKycData, navigation]);

  const CurrentStepComponent = useMemo(
    () => steps[currentStep]?.component,
    [steps, currentStep],
  );

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <IdentityCardIcon
            width={32}
            height={32}
            color={GlobalStyles.primary.primary500}
          />

          <Text style={styles.headerTitle}>Loan KYC Verification</Text>
        </View>

        {/* Step indicators */}
        {steps.map((step) => (
          <KycStepIndicator
            key={step.id}
            step={step}
            isActive={step.id === currentStep + 1}
            isCompleted={step.id < currentStep + 1}
          />
        ))}

        {/* Current Form Section */}
        <View style={styles.formContainer}>
          {CurrentStepComponent && (
            <CurrentStepComponent onNext={handleNext} onBack={handleBack} />
          )}
        </View>
      </ScrollView>
    </View>
  );
};

export default memo(LoanKYC);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    marginLeft: 8,
    fontSize: 24,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },

  formContainer: {
    padding: 16,
    backgroundColor: GlobalStyles.base.white,
    marginTop: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 32,
  },
});
