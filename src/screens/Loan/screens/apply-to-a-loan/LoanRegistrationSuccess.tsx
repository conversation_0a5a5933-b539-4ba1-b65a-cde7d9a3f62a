import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo} from 'react';
import {StyleSheet, Text, View, SafeAreaView} from 'react-native';
import {EnvelopeIcon} from 'react-native-heroicons/outline';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {LoanStackParamList} from '@/navigation/types.tsx';
import theme from '@/styles/themes';

type LoanRegistrationSuccessNavigationProp = StackNavigationProp<
  LoanStackParamList,
  'LoanRegistrationSuccess'
>;

const LoanRegistrationSuccess: React.FC = () => {
  const navigation = useNavigation<LoanRegistrationSuccessNavigationProp>();

  const handleGoToLogin = () => {
    navigation.replace('LoanLogin');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.content}>
          <EnvelopeIcon width={80} height={80} color={GlobalStyles.gray.gray900} />
          {/* <AssetifyLogo width={42} height={42} /> */}
          <Text style={styles.title}>Check Your Email</Text>
          <Text style={styles.subtitle}>
            We've sent a confirmation link to your email address. Please check your inbox
            (and spam folder) to complete your registration.
          </Text>
        </View>

        <View style={styles.footer}>
          <MButton text="Go to Login" onPress={handleGoToLogin} />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default memo(LoanRegistrationSuccess);

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: theme.layout.ph.screen,
    paddingVertical: theme.layout.pv.screen,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.xl,
    paddingHorizontal: theme.layout.ph.screen,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
    marginTop: theme.spacing.md,
  },
  subtitle: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
    textAlign: 'center',
    lineHeight: 24,
  },
  footer: {
    paddingBottom: theme.spacing.lg,
  },
});
