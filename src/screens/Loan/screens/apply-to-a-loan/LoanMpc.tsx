import React, {memo, useEffect} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import Assetify<PERSON>ogo from '@/assets/logo/Logo.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAppSelector, useAuth} from '@/hooks/redux';
import {KeyGenerationService} from '@/mpc/actions/key-generation';
import {LoanMPCSharesService} from '@/services/LoanMPCSharesService';
import {addLoan} from '@/storage/actions/loanActions';
import {SESSION_SERVICE_URL} from '@env';
import {DKGSession} from 'specs/common-payloads';

const LoanMpc = ({navigation, route}: any) => {
  const {loanID} = route.params;

  console.log('LOAN ID >>>>>>>>>', loanID);

  const dispatch = useAppDispatch();
  const {user, userAddresses} = useAuth();
  const loan = useAppSelector((state) => state.loan.loanData);

  useEffect(() => {
    const runMPCWithDelay = async () => {
      const session = {
        session: {
          parameters: {threshold: 2, share_count: 3},
          party_index: 1,
          session_id: Array(32).fill(0),
        },
      } as DKGSession;

      const networkParams = {
        socketURL: `${SESSION_SERVICE_URL}`,
        namespace: 'mpc-connections',
        sessionId: loanID,
      };
      try {
        const keyGenerationService = new KeyGenerationService();

        const keyGenerationSession = await keyGenerationService.startDkgSession(
          session,
          networkParams,
        );

        console.log('KEY GENERATION SESSION >>>>>>>>>', keyGenerationSession);

        // // Start both the DKG operation and the delay concurrently
        // const playerPromise = await runDKGParty(
        //   session,
        //   {
        //     socketURL: `${'ws://localhost:9000'}/`,
        //     namespace: 'mpc-connections',
        //   },
        //   loanID,
        // );

        dispatch(addLoan(loanID, keyGenerationService.party!));
        const delayPromise = new Promise((resolve) => setTimeout(resolve, 5000));

        const encryptedKeyShare = await keyGenerationService.encryptKeyShare(
          user.wallet[0].mnemonic,
          JSON.stringify(keyGenerationService.party),
        );

        console.log('ENCRYPTED KEY SHARE >>>>>>>>>', encryptedKeyShare);

        console.log('test 0', encryptedKeyShare);
        const keyshares = new LoanMPCSharesService();
        await keyshares.createLoanMPCShare(
          {...encryptedKeyShare, loanID: loanID},
          userAddresses[0].address,
        );

        console.log('test 1', keyshares);

        // Wait for both promises to resolve
        await Promise.all([keyGenerationSession, delayPromise]);

        console.log('test 2', keyshares);

        // console.log(playerPromise)

        // const playerDSG = await runDSGSign(
        //   playerPromise.party!,
        //   signData,
        //   loanID
        // );

        // console.log(playerDSG)

        console.log(
          'KEY GENERATION SESSION >>>>>>>>>',
          !!keyGenerationSession.eth_address,
        );
        console.log('ENCRYPTED KEY SHARE >>>>>>>>>', encryptedKeyShare);
        console.log('KEYSHARE >>>>>>>>>', keyshares);

        if (keyGenerationSession.eth_address) {
          // Navigate to LoanCollateral, passing the ethAddress
          navigation.replace('LoanPaymentMethods', {
            ethAddress: keyGenerationSession.eth_address,
            btcAddress: keyGenerationSession.btc_address,
            chain: loan.collateralCurrency,
          });
        } else {
          console.error('Eth address not found in player.party');
          // Optionally, handle the absence of eth_address
        }
      } catch (err) {
        console.error('DKG error:', err);
        // Optionally, navigate to an error screen or show a toast
      }
    };

    runMPCWithDelay();
  }, [loanID, navigation]);

  return (
    <View style={styles.container}>
      <AssetifyLogo width={90} height={90} style={styles.logo} />

      <Text style={styles.title}>Creating your MPC Wallet...</Text>

      <Text style={styles.description}>
        We're crafting a secure and personalized wallet experience just for you.
      </Text>
    </View>
  );
};

export default memo(LoanMpc);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
    paddingBottom: 22,
  },
  logo: {
    marginBottom: 22,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    textAlign: 'center',
    paddingHorizontal: 60,
    marginBottom: 14,
  },
  description: {
    color: GlobalStyles.gray.gray800,
    fontSize: 18,
    textAlign: 'center',
    paddingHorizontal: 70,
    lineHeight: 26,
  },
});
