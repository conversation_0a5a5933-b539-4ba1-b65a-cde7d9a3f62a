import React, {useEffect, useState} from 'react';
import {ActivityIndicator, StyleSheet, Text, View} from 'react-native';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAppSelector, useAuth, useLoan} from '@/hooks/redux';
import {resetToHome} from '@/navigation/utils/navigation';
import Success from '@/screens/Success';
import {createLoanInstance} from '@/services/BackendServices';
import {
  setAccessToken,
  setHasLoan,
  setLoanData,
  setRefreshToken,
} from '@/storage/actions/loanActions';
import {RootState} from '@/storage/store';

const LoanApproval = ({route}: any) => {
  const dispatch = useAppDispatch();
  const {userAddresses} = useAuth();
  const {loanData: updatedLoan} = useAppSelector((state: RootState) => state.loan);
  const {accessToken, refreshToken} = useLoan();
  // const {updatedLoanData, accessToken, refreshToken} = route.params;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const submitLoanApproval = async () => {
    try {
      console.log(
        'data to be sent',
        JSON.stringify({
          collateral: {
            amount: updatedLoan.collateralAmount,
            currency: updatedLoan.collateralCurrency,
            btcAddress: userAddresses[0].address,
            ethAddress: userAddresses[1].address,
          },
          fiat: {
            amount: updatedLoan.borrowAmount,
            currency: updatedLoan.borrowCurrency,
          },
          ltv: updatedLoan.loanToValue,
          interest: updatedLoan.interestPayment.toUpperCase(),
          term: updatedLoan.term,
        }),
      );

      dispatch(setLoanData(updatedLoan));
      dispatch(setAccessToken(accessToken));
      dispatch(setRefreshToken(refreshToken));

      console.log('Current token ', accessToken);
      const loan = createLoanInstance(accessToken);

      const response = await loan.post('/loan', {
        collateral: {
          amount: updatedLoan.collateralAmount,
          currency: updatedLoan.collateralCurrency,
          btcAddress: userAddresses[0].address,
          ethAddress: userAddresses[1].address,
        },
        fiat: {
          amount: updatedLoan.borrowAmount,
          currency: updatedLoan.borrowCurrency,
        },
        ltv: updatedLoan.loanToValue,
        interest: updatedLoan.interestPayment.toUpperCase(),
        term: updatedLoan.term,
      });

      dispatch(
        setLoanData({
          ...updatedLoan,
          id: response.data.loanId,
        }),
      );

      if (!response.data) {
        throw new Error('Failed to submit loan approval');
      }

      setIsLoading(false);
      setIsSuccess(true);
    } catch (err) {
      console.log('err', err);
      setIsLoading(false);
      setError(err instanceof Error ? err.message : 'Something went wrong');
    }
  };

  const retrySubmission = () => {
    setError(null);
    setIsLoading(true);
    submitLoanApproval();
  };

  const handleBackToHome = () => {
    dispatch(setHasLoan(true));
    resetToHome();
  };

  useEffect(() => {
    submitLoanApproval();
  }, []);

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
        <Text style={styles.loadingText}>Submitting your application...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>{error}</Text>
        <View style={styles.errorButton}>
          <MButton text="Try Again" onPress={retrySubmission} />
        </View>
      </View>
    );
  }

  if (isSuccess) {
    return (
      <Success
        title="Waiting for Approval"
        description="Typically, this takes 24-48 hours! We'll be in touch with you soon!"
        onFinish={handleBackToHome}
        buttonText="Back to Home"
        isSuccess={isSuccess}
      />
    );
  }

  return null;
};

export default LoanApproval;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 24,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  errorText: {
    fontSize: 16,
    color: GlobalStyles.error.error500,
    textAlign: 'center',
    marginBottom: 24,
  },
  errorButton: {
    width: '100%',
  },
});
