import React, {memo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

export type Step = {
  id: number;
  title: string;
  component: React.FC<{onNext: () => void; onBack?: () => void}>;
};

type Props = {
  step: Step;
  isActive: boolean;
  isCompleted: boolean;
};

const KycStepIndicator: React.FC<Props> = React.memo(({step, isActive, isCompleted}) => {
  return (
    <View
      style={[
        styles.stepContainer,
        isActive && styles.activeStepContainer,
        isCompleted && styles.completedStepContainer,
      ]}
    >
      <View
        style={[
          styles.stepNumber,
          isActive && styles.activeStepNumber,
          isCompleted && styles.completedStepNumber,
        ]}
      >
        <Text
          style={[
            styles.stepNumberText,
            isActive && styles.activeStepNumberText,
            isCompleted && styles.completedStepNumberText,
          ]}
        >
          {step.id}
        </Text>
      </View>
      <Text
        style={[
          styles.stepTitle,
          isActive && styles.activeStepTitle,
          isCompleted && styles.completedStepTitle,
        ]}
      >
        {step.title}
      </Text>
    </View>
  );
});

export default memo(KycStepIndicator);

const styles = StyleSheet.create({
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: GlobalStyles.base.white,
    marginBottom: 2,
  },
  activeStepContainer: {
    backgroundColor: GlobalStyles.base.white,
  },
  completedStepContainer: {
    backgroundColor: GlobalStyles.base.white,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: GlobalStyles.gray.gray700,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activeStepNumber: {
    backgroundColor: GlobalStyles.primary.primary500,
  },
  completedStepNumber: {
    backgroundColor: GlobalStyles.success.success500,
  },
  stepNumberText: {
    color: GlobalStyles.base.white,
    fontSize: 16,
    fontWeight: '600',
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  activeStepNumberText: {
    color: GlobalStyles.base.white,
  },
  completedStepNumberText: {
    color: GlobalStyles.base.white,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: GlobalStyles.gray.gray800,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  activeStepTitle: {
    color: GlobalStyles.base.black,
    fontWeight: '600',
  },
  completedStepTitle: {
    color: GlobalStyles.base.black,
    fontWeight: '500',
  },
});
