import GlobalStyles from '@/constants/GlobalStyles';
import {ILoanState} from '../utils/loan-types';

export const getLoanStatusText = (state: ILoanState): string => {
  switch (state) {
    case ILoanState.PENDING_BANK_TERMS_APPROVAL:
      return 'Pending Loan Approval';
    case ILoanState.PENDING_WALLET_CREATION:
      return 'Awaiting MPC Wallet Creation';
    case ILoanState.PENDING_COLLATERAL_DEPOSIT_TRANSACTION:
      return 'Awaiting Deposit';
    case ILoanState.PENDING_COLLATERAL_DEPOSIT_CONFIRMATION:
      return 'Confirming Deposit';
    case ILoanState.PENDING_COLLATERAL_WITHDRAWAL_APPROVAL:
      return 'Awaiting Withdrawal Approval';
    case ILoanState.PENDING_COLLATERAL_WITHDRAWAL_TRANSACTION:
      return 'Processing Withdrawal';
    case ILoanState.PENDING_COLLATERAL_WITHDRAWAL_CONFIRMATION:
      return 'Confirming Withdrawal';
    case ILoanState.ACTIVE:
      return 'Active';
    case ILoanState.COMPLETED:
      return 'Completed';
    case ILoanState.TERMINATED:
      return 'Terminated';
    case ILoanState.REJECTED:
      return 'Rejected';
    default:
      return 'Pending';
  }
};

export const getStatusColor = (state: ILoanState) => {
  switch (state) {
    case ILoanState.ACTIVE:
      return {
        badge: GlobalStyles.success.success800,
        text: GlobalStyles.success.success700,
      };
    case ILoanState.COMPLETED:
      return {
        badge: GlobalStyles.success.success800,
        text: GlobalStyles.success.success700,
      };
    case ILoanState.TERMINATED:
      return {
        badge: GlobalStyles.error.error700,
        text: GlobalStyles.gray.gray800,
      };
    case ILoanState.REJECTED:
      return {
        badge: GlobalStyles.error.error300,
        text: GlobalStyles.error.error400,
      };
    default:
      return {
        badge: GlobalStyles.orange.orange600,
        text: GlobalStyles.orange.orange700,
      };
  }
};

export const isPending = (state: ILoanState): boolean => {
  return (
    state === ILoanState.PENDING_BANK_TERMS_APPROVAL ||
    state === ILoanState.PENDING_WALLET_CREATION ||
    state === ILoanState.PENDING_COLLATERAL_DEPOSIT_TRANSACTION ||
    state === ILoanState.PENDING_COLLATERAL_DEPOSIT_CONFIRMATION ||
    state === ILoanState.PENDING_COLLATERAL_WITHDRAWAL_APPROVAL ||
    state === ILoanState.PENDING_COLLATERAL_WITHDRAWAL_TRANSACTION ||
    state === ILoanState.PENDING_COLLATERAL_WITHDRAWAL_CONFIRMATION
  );
};

export const isActive = (state: ILoanState): boolean => {
  return state === ILoanState.ACTIVE;
};

export const isInactive = (state: ILoanState): boolean => {
  return (
    state === ILoanState.COMPLETED ||
    state === ILoanState.TERMINATED ||
    state === ILoanState.REJECTED
  );
};
