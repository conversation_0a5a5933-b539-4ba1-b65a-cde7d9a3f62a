import React from 'react';
import {StyleSheet, Text, View, Image, ViewStyle} from 'react-native';
import GlobalStyles from '@/constants/GlobalStyles';

interface DocumentPreviewProps {
  title: string;
  imageUri: string;
  containerStyle?: ViewStyle;
}

/**
 * A reusable document preview component for identity verification
 */
const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  title,
  imageUri,
  containerStyle,
}) => {
  return (
    <View style={[styles.previewContainer, containerStyle]}>
      <Text style={styles.previewTitle}>{title}</Text>
      <Image
        source={{uri: imageUri}}
        style={styles.previewImage}
        resizeMode="cover"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  previewContainer: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: GlobalStyles.gray.gray100,
    borderRadius: 8,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 8,
    fontFamily: GlobalStyles.fonts.sfPro,
    textTransform: 'capitalize',
  },
  previewImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
    backgroundColor: GlobalStyles.gray.gray200,
  },
});

export default DocumentPreview;
