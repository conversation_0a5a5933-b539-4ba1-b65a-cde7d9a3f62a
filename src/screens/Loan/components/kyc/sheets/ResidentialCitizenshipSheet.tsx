import React, {memo} from 'react';

import BottomSheetList, {
  BaseOption,
  BottomSheetListData,
} from '@/components/BottomSheetList';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useAppSelector} from '@/hooks/redux';
import {selectSheet} from '@/storage/slices/ui';

const KYC_RESI_CITIZENSHIP_SHEET_SNAP_POINTS = [300];

const ResidentialCitizenshipSheet: React.FC = () => {
  const {isOpen, data, close: closeSheet} = useBottomSheet('kycResiCitizenship');
  const sheetState = useAppSelector((state) => selectSheet(state, 'kycResiCitizenship'));

  const sheetData = data as BottomSheetListData | null;
  const options = (sheetData?.data as BaseOption[]) || [];
  const onSelectCallback = sheetData?.onSelect as
    | ((item: BaseOption) => void)
    | undefined;

  if (!isOpen || !sheetData || !onSelectCallback) {
    return null;
  }

  return (
    <BottomSheetList
      isOpen={isOpen}
      snapPoints={sheetState.snapPoints || KYC_RESI_CITIZENSHIP_SHEET_SNAP_POINTS}
      data={options}
      onSelect={(option) => {
        onSelectCallback(option as BaseOption);
        closeSheet();
      }}
      onClose={closeSheet}
      enableSearch={sheetState.enableSearch || false}
    />
  );
};

export default memo(ResidentialCitizenshipSheet);
