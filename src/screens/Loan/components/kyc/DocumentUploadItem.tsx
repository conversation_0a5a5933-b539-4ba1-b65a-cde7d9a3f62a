import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View, ViewStyle} from 'react-native';
import {CheckIcon} from 'react-native-heroicons/solid';
import GlobalStyles from '@/constants/GlobalStyles';

interface DocumentUploadItemProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  isCompleted: boolean;
  onPress: () => void;
  containerStyle?: ViewStyle;
}

/**
 * A reusable document upload item component for identity verification
 */
const DocumentUploadItem: React.FC<DocumentUploadItemProps> = ({
  title,
  description,
  icon,
  isCompleted,
  onPress,
  containerStyle,
}) => {
  return (
    <TouchableOpacity
      style={[styles.uploadSection, isCompleted && styles.uploadSectionCompleted, containerStyle]}
      onPress={onPress}
    >
      <View style={styles.uploadIconContainer}>{icon}</View>
      <View style={styles.uploadTextContainer}>
        <Text style={styles.uploadTitle}>{title}</Text>
        <Text style={styles.uploadDescription}>{description}</Text>
      </View>
      <View style={styles.uploadStatusContainer}>
        {isCompleted ? (
          <View style={styles.uploadCompleteIndicator}>
            <CheckIcon width={16} height={16} color={GlobalStyles.base.white} />
          </View>
        ) : (
          <Text style={styles.uploadStatusText}>Required</Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  uploadSection: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: GlobalStyles.gray.gray100,
    borderRadius: 8,
    marginBottom: 16,
  },
  uploadSectionCompleted: {
    borderWidth: 1,
    borderColor: GlobalStyles.success.success500,
  },
  uploadIconContainer: {
    marginRight: 12,
  },
  uploadTextContainer: {
    flex: 1,
  },
  uploadTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 4,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  uploadDescription: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  uploadStatusContainer: {
    marginLeft: 12,
  },
  uploadStatusText: {
    fontSize: 14,
    color: GlobalStyles.primary.primary500,
    fontWeight: '600',
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  uploadCompleteIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: GlobalStyles.success.success500,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default DocumentUploadItem;
