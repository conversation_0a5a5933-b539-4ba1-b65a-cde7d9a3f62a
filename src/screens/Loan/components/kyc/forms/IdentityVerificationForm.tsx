import React, {useState, useEffect, useMemo} from 'react';
import {StyleSheet, Text, View, Platform, PermissionsAndroid, Alert} from 'react-native';

import {launchCamera, launchImageLibrary, CameraOptions} from 'react-native-image-picker';

import {IdentificationIcon, HomeIcon, CameraIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {useKyc} from '@/screens/Loan/components/kyc/KycContext';
import MButton from '@/components/MButton';
import DocumentUploadItem from '@/screens/Loan/components/kyc/DocumentUploadItem';
import DocumentPreview from '@/screens/Loan/components/kyc/DocumentPreview';
import {ID_VERIFICATION_DEFAULT_VALUES} from './utils';

interface DocumentType {
  id: string;
  label: string;
  icon: React.ReactNode;
}

interface IdentityVerificationFormProps {
  onNext: () => void;
  onBack?: () => void;
}

const IdentityVerificationForm: React.FC<IdentityVerificationFormProps> = ({
  onNext,

  onBack,
}) => {
  const {kycState, updateIdVerification, submitKycData, isSubmitting} = useKyc();

  // Document types
  const documentTypes: DocumentType[] = [
    {
      id: 'driver_license',
      label: 'Driver License',
      icon: (
        <IdentificationIcon width={32} height={32} color={GlobalStyles.gray.gray800} />
      ),
    },
    {
      id: 'national_id',
      label: 'National ID',
      icon: (
        <IdentificationIcon width={32} height={32} color={GlobalStyles.gray.gray800} />
      ),
    },
    {
      id: 'passport',
      label: 'Passport',
      icon: (
        <IdentificationIcon width={32} height={32} color={GlobalStyles.gray.gray800} />
      ),
    },
    {
      id: 'residence_permit',
      label: 'Residency Permit',
      icon: (
        <IdentificationIcon width={32} height={32} color={GlobalStyles.gray.gray800} />
      ),
    },
  ];

  // Address document types
  const addressTypes: DocumentType[] = [
    {
      id: 'utility_bill',
      label: 'Utility Bill',
      icon: <HomeIcon width={32} height={32} color={GlobalStyles.gray.gray800} />,
    },
    {
      id: 'tax_document',
      label: 'Tax Document',
      icon: <HomeIcon width={32} height={32} color={GlobalStyles.gray.gray800} />,
    },
  ];

  // Initialize state from context if available
  const storedIdVerification = kycState.idVerification || ID_VERIFICATION_DEFAULT_VALUES;

  const [selectedDocumentType, setSelectedDocumentType] = useState<string>(
    storedIdVerification.idType || '',
  );

  // Track if documents have been uploaded
  const [selfieUploaded, setSelfieUploaded] = useState(
    !!storedIdVerification.selfieImage,
  );
  const [documentUploaded, setDocumentUploaded] = useState(
    !!storedIdVerification.idFrontImage,
  );
  const [addressDocumentUploaded, setAddressDocumentUploaded] = useState(
    !!storedIdVerification.proofOfAddressImage,
  );

  // State for front/back capture
  const [isFrontSideCapture, setIsFrontSideCapture] = useState(true);

  // Request permissions when component mounts
  useEffect(() => {
    const requestPermissions = async () => {
      if (Platform.OS === 'android') {
        try {
          if (PermissionsAndroid.PERMISSIONS.CAMERA) {
            await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.CAMERA, {
              title: 'Camera Permission',
              message: 'We need access to your camera for taking photos',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            });
          }

          const storagePermission =
            Platform.Version >= 33
              ? PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
              : PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE;

          if (storagePermission) {
            await PermissionsAndroid.request(storagePermission, {
              title: 'Photo Library Permission',
              message: 'We need access to your photos to upload documents',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            });
          }
        } catch (err) {
          console.warn(err);
        }
      }
    };

    requestPermissions();
  }, []);

  const handleTakePhoto = (documentSection: 'id' | 'address' | 'selfie') => {
    const options: CameraOptions = {
      mediaType: 'photo',
      quality: 0.8,
      saveToPhotos: false,
      includeBase64: false,
      cameraType: documentSection === 'selfie' ? 'front' : 'back',
    };

    launchCamera(options, (response) => {
      if (response.didCancel || response.errorCode) {
        return;
      }

      if (response.assets && response.assets.length > 0) {
        const photoAsset = response.assets[0];
        if (photoAsset && photoAsset.uri) {
          if (documentSection === 'selfie') {
            updateIdVerification({selfieImage: photoAsset.uri});
            setSelfieUploaded(true);
          } else if (documentSection === 'id') {
            if (isFrontSideCapture) {
              updateIdVerification({
                idFrontImage: photoAsset.uri,
                idType: selectedDocumentType,
              });

              // If we need to capture both sides, prompt for back side
              if (selectedDocumentType !== 'passport') {
                setIsFrontSideCapture(false);
                Alert.alert(
                  'Front Side Captured',
                  'Now please capture the back side of your document.',
                  [{text: 'OK'}],
                );
                return;
              }
            } else {
              updateIdVerification({idBackImage: photoAsset.uri});
              setIsFrontSideCapture(true);
            }
            setDocumentUploaded(true);
          } else if (documentSection === 'address') {
            updateIdVerification({
              proofOfAddressImage: photoAsset.uri,
            });
            setAddressDocumentUploaded(true);
          }
        }
      }
    });
  };

  const handlePickFromGallery = (documentSection: 'id' | 'address' | 'selfie') => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        selectionLimit: 1,
        quality: 0.8,
      },
      (response) => {
        if (response.didCancel || response.errorCode) {
          return;
        }

        if (response.assets && response.assets.length > 0) {
          const photoAsset = response.assets[0];
          if (photoAsset && photoAsset.uri) {
            if (documentSection === 'selfie') {
              updateIdVerification({selfieImage: photoAsset.uri});
              setSelfieUploaded(true);
            } else if (documentSection === 'id') {
              if (isFrontSideCapture) {
                updateIdVerification({
                  idFrontImage: photoAsset.uri,
                  idType: selectedDocumentType,
                });

                if (selectedDocumentType !== 'passport') {
                  setIsFrontSideCapture(false);
                  Alert.alert(
                    'Front Side Selected',
                    'Now please select the back side of your document.',
                    [{text: 'OK'}],
                  );
                  return;
                }
              } else {
                updateIdVerification({idBackImage: photoAsset.uri});
                setIsFrontSideCapture(true);
              }
              setDocumentUploaded(true);
            } else if (documentSection === 'address') {
              updateIdVerification({
                proofOfAddressImage: photoAsset.uri,
              });
              setAddressDocumentUploaded(true);
            }
          }
        }
      },
    );
  };

  const selectDocumentType = (documentSection: 'id' | 'address') => {
    Alert.alert(
      documentSection === 'id'
        ? 'Select ID Document Type'
        : 'Select Address Document Type',
      'Choose the type of document you want to upload',
      [
        ...(documentSection === 'id' ? documentTypes : addressTypes).map((type) => ({
          text: type.label,
          onPress: () => {
            if (documentSection === 'id') {
              setSelectedDocumentType(type.id);
              showUploadOptions('id');
            } else {
              showUploadOptions('address');
            }
          },
        })),
        {text: 'Cancel', style: 'cancel'},
      ],
    );
  };

  const showUploadOptions = (documentSection: 'id' | 'address' | 'selfie') => {
    Alert.alert('Upload Document', 'Choose how you want to upload your document', [
      {
        text: 'Take Photo',
        onPress: () => handleTakePhoto(documentSection),
      },
      {
        text: 'Choose from Gallery',
        onPress: () => handlePickFromGallery(documentSection),
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ]);
  };

  const handleSubmit = async () => {
    const success = await submitKycData();
    if (success) {
      onNext();
    }
  };

  // Memoize completion status to prevent unnecessary recalculations
  const isComplete = useMemo(
    () => selfieUploaded && documentUploaded && addressDocumentUploaded,
    [selfieUploaded, documentUploaded, addressDocumentUploaded],
  );

  return (
    <>
      <Text style={styles.formDescription}>
        Please provide the required documents to verify your identity. All documents must
        be valid and clearly visible.
      </Text>

      {/* Document upload section */}
      <DocumentUploadItem
        title="Upload identification document"
        description="Choose a valid government-issued ID"
        icon={
          <IdentificationIcon width={24} height={24} color={GlobalStyles.gray.gray800} />
        }
        isCompleted={documentUploaded}
        onPress={() => selectDocumentType('id')}
      />

      {/* Display document preview if uploaded */}
      {documentUploaded && kycState.idVerification?.idFrontImage && (
        <>
          <DocumentPreview
            title={`${selectedDocumentType?.replace('_', ' ')} (Front)`}
            imageUri={kycState.idVerification.idFrontImage}
          />

          {kycState.idVerification.idBackImage && (
            <DocumentPreview
              title={`${selectedDocumentType?.replace('_', ' ')} (Back)`}
              imageUri={kycState.idVerification.idBackImage}
            />
          )}
        </>
      )}

      {/* Address document upload section */}
      <DocumentUploadItem
        title="Upload proof of address"
        description="Utility bill, bank statement or lease agreement"
        icon={<HomeIcon width={24} height={24} color={GlobalStyles.gray.gray800} />}
        isCompleted={addressDocumentUploaded}
        onPress={() => selectDocumentType('address')}
      />

      {/* Display address document preview if uploaded */}
      {addressDocumentUploaded && kycState.idVerification?.proofOfAddressImage && (
        <DocumentPreview
          title="Proof of Address"
          imageUri={kycState.idVerification.proofOfAddressImage}
        />
      )}

      {/* Selfie upload section */}
      <DocumentUploadItem
        title="Upload selfie photo"
        description="Take a clear photo of your face in good lighting"
        icon={<CameraIcon width={24} height={24} color={GlobalStyles.gray.gray800} />}
        isCompleted={selfieUploaded}
        onPress={() => showUploadOptions('selfie')}
      />

      {/* Display selfie preview if uploaded */}
      {selfieUploaded && kycState.idVerification?.selfieImage && (
        <DocumentPreview title="Selfie" imageUri={kycState.idVerification.selfieImage} />
      )}

      <View style={styles.buttonGroup}>
        <View style={{flex: 1}}>
          {onBack && <MButton text="Back" onPress={onBack} variant="secondary" />}
        </View>

        <View style={{flex: 1}}>
          <MButton
            text={isSubmitting ? 'Submitting...' : 'Submit'}
            onPress={handleSubmit}
            disabled={!isComplete || isSubmitting}
          />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  formDescription: {
    fontSize: 16,
    lineHeight: 24,
    color: GlobalStyles.gray.gray800,
    marginBottom: 24,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
});

export default IdentityVerificationForm;
