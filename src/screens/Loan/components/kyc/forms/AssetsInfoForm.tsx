import React, {useMemo, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {
  useKyc,
  FUNDS_SOURCES,
  INCOME_RANGES,
  ACCOUNT_PURPOSES,
} from '@/screens/Loan/components/kyc/KycContext';
import MButton from '@/components/MButton';
import RadioButton from '@/components/RadioButton';
import Checkbox from '@/components/Checkbox';
import {YOUR_ASSETS_DEFAULT_VALUES} from './utils';

interface AssetsInfoFormProps {
  onNext: () => void;
  onBack?: () => void;
}

const AssetsInfoForm: React.FC<AssetsInfoFormProps> = ({onNext, onBack}) => {
  const {kycState, updateYourAssets, goToNextStep} = useKyc();

  // Initialize from stored data if available
  const storedAssetsInfo = kycState.yourAssets || YOUR_ASSETS_DEFAULT_VALUES;

  // Form state
  const [selectedFundSource, setSelectedFundSource] = useState<string | null>(
    storedAssetsInfo.fundsSource || null,
  );

  const [selectedIncomeRange, setSelectedIncomeRange] = useState<string | null>(
    storedAssetsInfo.annualIncome || null,
  );
  4;

  const [selectedPurposes, setSelectedPurposes] = useState<string[]>(
    storedAssetsInfo.accountPurposes || [],
  );

  const [legalConfirmation, setLegalConfirmation] = useState(
    storedAssetsInfo.legalConfirmation || false,
  );

  const [platformDisclaimer, setPlatformDisclaimer] = useState(
    storedAssetsInfo.platformDisclaimer || false,
  );

  const handleFundSourceSelect = (source: string) => {
    setSelectedFundSource(source);
  };

  const handleIncomeRangeSelect = (range: string) => {
    setSelectedIncomeRange(range);
  };

  const handlePurposeToggle = (purpose: string) => {
    if (selectedPurposes.includes(purpose)) {
      setSelectedPurposes(selectedPurposes.filter((item) => item !== purpose));
    } else {
      setSelectedPurposes([...selectedPurposes, purpose]);
    }
  };

  const handleContinue = () => {
    // Save assets information to context
    updateYourAssets({
      fundsSource: selectedFundSource || '',
      annualIncome: selectedIncomeRange || '',
      accountPurposes: selectedPurposes,
      legalConfirmation,
      platformDisclaimer,
    });

    // Update step in KYC context
    goToNextStep();

    // Move to next step
    onNext();
  };

  // Memoize form validation to prevent unnecessary recalculations
  const isFormValid = useMemo(() => {
    return !!(
      selectedFundSource &&
      selectedIncomeRange &&
      selectedPurposes.length > 0 &&
      legalConfirmation &&
      platformDisclaimer
    );
  }, [
    selectedFundSource,
    selectedIncomeRange,
    selectedPurposes,
    legalConfirmation,
    platformDisclaimer,
  ]);

  return (
    <>
      <Text style={styles.sectionTitle}>What is the main source of your funds?</Text>

      {FUNDS_SOURCES.map((source) => (
        <RadioButton
          key={source}
          label={source}
          selected={selectedFundSource === source}
          onPress={() => handleFundSourceSelect(source)}
        />
      ))}

      <Text style={styles.noteText}>
        You might be required to provide documentation to prove it.
      </Text>

      <Text style={styles.sectionTitle}>
        What is your annual household income before taxes?
      </Text>

      {INCOME_RANGES.map((range) => (
        <RadioButton
          key={range}
          label={range}
          selected={selectedIncomeRange === range}
          onPress={() => handleIncomeRangeSelect(range)}
        />
      ))}

      <Text style={styles.sectionTitle}>
        What do you plan to achieve with your Assetify account?
      </Text>

      {ACCOUNT_PURPOSES.map((purpose) => (
        <Checkbox
          key={purpose}
          label={purpose}
          checked={selectedPurposes.includes(purpose)}
          onPress={() => handlePurposeToggle(purpose)}
        />
      ))}

      <View style={{marginTop: 32}}>
        <Checkbox
          label="I confirm that all my assets were acquired legally and will be used for legal purposes."
          checked={legalConfirmation}
          onPress={() => setLegalConfirmation(!legalConfirmation)}
        />

        <Checkbox
          label="I acknowledge that Assetify is a self-directed platform. Assetify does not provide any advice or recommendations regarding the purchase or sale of any digital assets, and I am solely responsible for my investment decisions and transactions, for any profits or losses and any tax consequences that may occur."
          checked={platformDisclaimer}
          onPress={() => setPlatformDisclaimer(!platformDisclaimer)}
        />
      </View>

      <View style={styles.buttonGroup}>
        <View style={{flex: 1}}>
          {onBack && <MButton text="Back" onPress={onBack} variant="secondary" />}
        </View>

        <View style={{flex: 1}}>
          <MButton text="Continue" onPress={handleContinue} disabled={!isFormValid} />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  noteText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    marginBottom: 24,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
});

export default AssetsInfoForm;
