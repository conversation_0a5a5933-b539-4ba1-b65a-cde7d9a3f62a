export type KycFormState = {
  personalInformation: PersonalInformationForm | null;
  residentialInformation: ResidentialInformationForm | null;
  yourAssets: YourAssetsForm | null;
  idVerification: IdVerificationForm | null;
  currentStep: number;
  isComplete: boolean;
};

export type PersonalInformationForm = {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  phoneNumber: string;
  occupation: string;
  occupationIndustry: string;
  occupationSector: string;
  citizenship: string[];
  isPep: boolean;
  confirmNoThirdParty: boolean;
};

export type ResidentialInformationForm = {
  streetAddress: string;
  city: string;
  country: string;
  stateProvince: string;
  postalCode: string;
  citizenshipStatus: string;
};

export type YourAssetsForm = {
  fundsSource: string;
  annualIncome: string;
  accountPurposes: string[];
  legalConfirmation: boolean;
  platformDisclaimer: boolean;
};

export type IdVerificationForm = {
  idType: string;
  idFrontImage: string | null;
  idBackImage: string | null;
  selfieImage: string | null;
  proofOfAddressImage: string | null;
};

export const PERSONAL_INFORMATION_DEFAULT_VALUES: PersonalInformationForm = {
  firstName: '',
  lastName: '',
  dateOfBirth: '',
  phoneNumber: '',
  occupation: '',
  occupationIndustry: '',
  occupationSector: '',
  citizenship: [],
  isPep: false,
  confirmNoThirdParty: false,
};

export const RESIDENTIAL_INFORMATION_DEFAULT_VALUES: ResidentialInformationForm = {
  streetAddress: '',
  city: '',
  country: '',
  stateProvince: '',
  postalCode: '',
  citizenshipStatus: '',
};

export const YOUR_ASSETS_DEFAULT_VALUES: YourAssetsForm = {
  fundsSource: '',
  annualIncome: '',
  accountPurposes: [],
  legalConfirmation: false,
  platformDisclaimer: false,
};

export const ID_VERIFICATION_DEFAULT_VALUES: IdVerificationForm = {
  idType: '',
  idFrontImage: null,
  idBackImage: null,
  selfieImage: null,
  proofOfAddressImage: null,
};
