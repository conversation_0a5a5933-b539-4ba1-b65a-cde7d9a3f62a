import React, {useState, useCallback, useMemo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import {TextInput} from '@/components/TextInput';
import SelectOption from '@/components/SelectOption';
import {BaseOption} from '@/components/BottomSheetList';
import GlobalStyles from '@/constants/GlobalStyles';
import {useKyc} from '@/screens/Loan/components/kyc/KycContext';
import MButton from '@/components/MButton';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {RESIDENTIAL_INFORMATION_DEFAULT_VALUES} from './utils';
import theme from '@/styles/themes';

const countryOptions: BaseOption[] = [
  {label: 'United States', value: 'us'},
  {label: 'United Kingdom', value: 'uk'},
  {label: 'Canada', value: 'ca'},
  {label: 'Australia', value: 'au'},
  {label: 'Germany', value: 'de'},
];

const citizenshipStatusOptions: BaseOption[] = [
  {label: 'Citizen', value: 'citizen'},
  {label: 'Permanent Resident', value: 'permanent_resident'},
  {label: 'Temporary Resident', value: 'temporary_resident'},
  {label: 'Non-Resident', value: 'non_resident'},
];

interface ResidentialInfoFormProps {
  onNext: () => void;
  onBack?: () => void;
}

const KYC_RESI_DEFAULT_SNAP_POINTS = [70];

const ResidentialInfoForm: React.FC<ResidentialInfoFormProps> = ({onNext, onBack}) => {
  const {kycState, updateResidentialInformation, goToNextStep} = useKyc();

  const {open: openResiCountrySheet} = useBottomSheet('list');
  const {open: openResiCitizenshipSheet} = useBottomSheet('list');

  const storedResidentialInfo =
    kycState.residentialInformation || RESIDENTIAL_INFORMATION_DEFAULT_VALUES;

  const [streetAddress, setStreetAddress] = useState(
    storedResidentialInfo.streetAddress || '',
  );
  const [city, setCity] = useState(storedResidentialInfo.city || '');
  const [stateProvince, setStateProvince] = useState(
    storedResidentialInfo.stateProvince || '',
  );
  const [postalCode, setPostalCode] = useState(storedResidentialInfo.postalCode || '');

  const [selectedCountry, setSelectedCountry] = useState<BaseOption | null>(
    storedResidentialInfo.country
      ? countryOptions.find((option) => option.value === storedResidentialInfo.country) ||
          null
      : null,
  );

  const [selectedCitizenshipStatus, setSelectedCitizenshipStatus] =
    useState<BaseOption | null>(
      storedResidentialInfo.citizenshipStatus
        ? citizenshipStatusOptions.find(
            (option) => option.value === storedResidentialInfo.citizenshipStatus,
          ) || null
        : null,
    );

  const handleContinue = () => {
    updateResidentialInformation({
      streetAddress,
      city,
      stateProvince,
      postalCode,
      country: selectedCountry?.value || '',
      citizenshipStatus: selectedCitizenshipStatus?.value || '',
    });
    goToNextStep();
    onNext();
  };

  // Memoize form validation to prevent unnecessary recalculations
  const isFormValid = useMemo(() => {
    return !!(
      streetAddress.trim() &&
      city.trim() &&
      stateProvince.trim() &&
      postalCode.trim() &&
      selectedCountry &&
      selectedCitizenshipStatus
    );
  }, [
    streetAddress,
    city,
    stateProvince,
    postalCode,
    selectedCountry,
    selectedCitizenshipStatus,
  ]);

  const handleOpenResiCountry = useCallback(() => {
    openResiCountrySheet(
      {
        type: 'simple' as const,
        data: countryOptions,
        onSelect: (item) => {
          setSelectedCountry(item as BaseOption);
        },
      },
      KYC_RESI_DEFAULT_SNAP_POINTS,
      false,
    );
  }, [openResiCountrySheet]);

  const handleOpenResiCitizenship = useCallback(() => {
    openResiCitizenshipSheet(
      {
        type: 'simple' as const,
        data: citizenshipStatusOptions,
        onSelect: (item) => {
          setSelectedCitizenshipStatus(item as BaseOption);
        },
      },
      KYC_RESI_DEFAULT_SNAP_POINTS,
      false,
    );
  }, [openResiCitizenshipSheet]);

  return (
    <>
      <Text style={styles.formDescription}>
        Please provide your residential information. This information will be used to
        verify your identity and ensure compliance with applicable regulations.
      </Text>

      <TextInput
        label="Full Street Address"
        placeholder="Enter your street address"
        value={streetAddress}
        onChangeText={setStreetAddress}
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="City"
        placeholder="Enter your city"
        value={city}
        onChangeText={setCity}
      />

      <View style={styles.inputSpacing} />

      <SelectOption
        label="Country"
        option={selectedCountry?.label || 'Select a country'}
        onPress={handleOpenResiCountry}
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="State/Province"
        placeholder="Enter your state or province"
        value={stateProvince}
        onChangeText={setStateProvince}
      />

      <View style={styles.inputSpacing} />

      <TextInput
        label="Postal Code"
        placeholder="Enter your postal code"
        value={postalCode}
        onChangeText={setPostalCode}
      />

      <View style={styles.inputSpacing} />

      <SelectOption
        label="Citizenship Status"
        option={selectedCitizenshipStatus?.label || 'Select your status'}
        onPress={handleOpenResiCitizenship}
      />

      <View style={styles.buttonGroup}>
        <View style={{flex: 1}}>
          {onBack && <MButton text="Back" onPress={onBack} variant="secondary" />}
        </View>

        <View style={{flex: 1}}>
          <MButton text="Continue" onPress={handleContinue} disabled={!isFormValid} />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  formDescription: {
    fontSize: 16,
    lineHeight: 24,
    color: GlobalStyles.gray.gray800,
    marginBottom: 24,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  inputSpacing: {
    height: 16,
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xxl,
  },
  backButton: {
    flex: 1,
    marginRight: 8,
  },
  continueButton: {
    flex: 1,
    marginLeft: 8,
  },
  disabledButton: {
    backgroundColor: GlobalStyles.gray.gray600,
  },
});

export default ResidentialInfoForm;
