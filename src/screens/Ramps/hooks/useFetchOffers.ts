import {useQuery} from '@tanstack/react-query';
import type {ActionType, RampOffer} from '../_types';
import {
  createBuyOrder,
  createSellOrder,
  fetchBuyOffers,
  fetchSellOffers,
} from '../helpers/rampsChangellyApi';

type OfferParams = {
  currencyFrom: string;
  currencyTo: string;
  amountFrom: string;
  country: string;
  state: string | null;
};

type OrderParams = {
  externalOrderId: string;
  externalUserId: string;
  providerCode: string;
  currencyFrom: string;
  currencyTo: string;
  amountFrom: string;
  country: string;
  walletAddress?: string;
  state?: string;
};

export const useFetchOffers = (type: ActionType, params: OfferParams) => {
  const {
    data: offers,
    isLoading,
    error,
  } = useQuery<RampOffer[]>({
    queryKey: ['offers', type, params],
    queryFn: async () => {
      const fetchFn = type === 'buy' ? fetchBuyOffers : fetchSellOffers;
      const cleanParams = Object.fromEntries(
        Object.entries(params)
          .filter(([_, value]) => value != null)
          .map(([key, value]) => [key, value as string]),
      );
      return fetchFn(cleanParams);
    },
    enabled: Boolean(params.currencyFrom && params.currencyTo && params.amountFrom),
    staleTime: 30 * 1000, // 30 seconds
    retry: 2,
  });

  const createOrder = async (type: ActionType, orderParams: OrderParams) => {
    const orderFn = type === 'buy' ? createBuyOrder : createSellOrder;
    const cleanParams = Object.fromEntries(
      Object.entries(orderParams)
        .filter(([_, value]) => value != null)
        .map(([key, value]) => [key, value as string]),
    );
    return orderFn(cleanParams);
  };

  return {
    offers,
    isLoading,
    error,
    createOrder,
  };
};
