import {useQuery} from '@tanstack/react-query';
import type {Country, ActionType} from '../_types';
import {
  fetchDataAndStore,
  isCountryCacheValid,
  readCachedData,
} from '../helpers/countries';
import {CACHE_DURATIONS} from '../helpers/cacheUtils';

export const useCountries = (type: ActionType) => {
  return useQuery<Country[]>({
    queryKey: ['countries', type],
    queryFn: async () => {
      const cachedData = await readCachedData(type);
      const isCacheValid = await isCountryCacheValid(type);

      if (!cachedData || !isCacheValid) {
        return fetchDataAndStore(type);
      }

      return cachedData;
    },
    staleTime: CACHE_DURATIONS.COUNTRIES,
  });
};
