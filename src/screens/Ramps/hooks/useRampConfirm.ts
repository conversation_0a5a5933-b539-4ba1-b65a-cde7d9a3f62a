import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {Linking} from 'react-native';

import {useAppSelector} from '@/hooks/redux';
import {showWarningToast} from '@/utils/toast';
import {CONFIRM_INITIAL_STATE} from '../helpers/rampConstants';
import {useFetchOffers} from './useFetchOffers';

export type RampConfirmState = {
  chosenOffer: {
    providerCode: string;
    fee: number;
    amountExpectedTo: string;
    amountFrom: string;
  } | null;
  switched: boolean;
  loading: boolean;
};

export const useRampConfirm = (rampData: any) => {
  const isFocused = useIsFocused();
  const btcAddr = useAppSelector(
    // @ts-ignore - ensures correct production state
    (state) => state.auth.userAddress ?? state.auth.userAddresses,
  );
  const {currencyFrom, currencyTo, amount, location, action, addressToUse, state} =
    rampData;

  const [confirmState, setConfirmState] =
    useState<RampConfirmState>(CONFIRM_INITIAL_STATE);

  const {offers, isLoading, error, createOrder} = useFetchOffers(action, {
    currencyFrom,
    currencyTo,
    amountFrom: amount,
    country: location,
    state: location === 'US' && state ? state : null,
  });

  useEffect(() => {
    if (!isFocused) {
      setConfirmState(CONFIRM_INITIAL_STATE);
    }
  }, [isFocused]);

  const handleOfferPress = (offer: RampConfirmState['chosenOffer'] | null) => {
    setConfirmState((prev) => ({...prev, chosenOffer: offer}));
  };

  const handleSwitchChange = () => {
    setConfirmState((prev) => ({...prev, switched: !prev.switched}));
  };

  const handleProceed = async () => {
    if (!confirmState.chosenOffer || !btcAddr?.[0]) return;

    setConfirmState((prev) => ({...prev, loading: true}));
    try {
      const data = await createOrder(action, {
        externalOrderId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        externalUserId: btcAddr[0].address,
        providerCode: confirmState.chosenOffer!.providerCode,
        currencyFrom,
        currencyTo,
        amountFrom: confirmState.chosenOffer!.amountFrom,
        country: location,
        walletAddress: action === 'buy' ? addressToUse : undefined,
        state: location === 'US' && state ? state : undefined,
      });

      await Linking.openURL(data.redirectUrl);
    } catch (err) {
      console.warn('Failed to create order:', err);
      showWarningToast('Something went wrong. Please try again');
    } finally {
      setConfirmState((prev) => ({...prev, loading: false}));
    }
  };

  const canProceed = confirmState.chosenOffer && confirmState.switched;

  return {
    state: confirmState,
    offers: offers || [],
    isLoading,
    error,
    canProceed,
    handlers: {
      handleOfferPress,
      handleSwitchChange,
      handleProceed,
    },
  };
};
