import {useQuery} from '@tanstack/react-query';
import type {ActionType} from '../_types';
import {CACHE_DURATIONS} from '../helpers/cacheUtils';
import {
  fetchDataAndStore,
  isCurrencyCacheValid,
  readCachedData,
} from '../helpers/currencies';

export const useCurrencies = (type: ActionType) => {
  return useQuery({
    queryKey: ['currencies', type],
    queryFn: async () => {
      const cachedData = await readCachedData(type);
      const isCacheValid = await isCurrencyCacheValid(type);

      if (!cachedData || !isCacheValid) {
        return fetchDataAndStore(type);
      }

      return cachedData;
    },
    staleTime: CACHE_DURATIONS.CURRENCIES,
  });
};
