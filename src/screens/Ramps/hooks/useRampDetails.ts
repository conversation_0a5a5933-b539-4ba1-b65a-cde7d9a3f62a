import {useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';

import type {BaseOption, BottomSheetListData} from '@/components/BottomSheetList';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {capitalizeAll} from '@/utils';
import type {ActionType, Country} from '../_types';
import {
  CURRENCY_OPTIONS,
  DETAILS_INITIAL_STATE,
  MIN_AMOUNT_FOR_BUY,
} from '../helpers/rampConstants';
import {useCountries} from './useCountries';

export type RampDetailsState = {
  amount: string;
  currencyFiat: string;
  location: Country | null;
  states: Array<{code: string; name: string}> | null;
  stateInput: string;
  validation: {
    amount: boolean | null;
    state: boolean | null;
  };
};

export const useRampDetails = (
  action: ActionType,
  currencySymbol: string,
  addressToUse: string,
) => {
  const {t} = useTranslation();
  const {data: countries} = useCountries(action);
  const {open: openSheet} = useBottomSheet('list');

  const [state, setState] = useState<RampDetailsState>(DETAILS_INITIAL_STATE);

  const handleAmountChange = (value: string) => {
    setState((prev) => ({
      ...prev,
      amount: value,
    }));
  };

  const handleAmountBlur = () => {
    const amountAsNumber = parseFloat(state.amount.replace(/,/g, ''));
    const isValid =
      !isNaN(amountAsNumber) &&
      amountAsNumber > 0 &&
      (action === 'buy' ? amountAsNumber >= MIN_AMOUNT_FOR_BUY : true);

    setState((prev) => ({
      ...prev,
      validation: {
        ...prev.validation,
        amount: isValid,
      },
    }));
  };

  const handleCurrencyPress = () => {
    if (action === 'sell') return;

    const sheetData: BottomSheetListData = {
      type: 'currency',
      data: CURRENCY_OPTIONS,
      onSelect: (item: BaseOption) => {
        setState((prev) => ({
          ...prev,
          currencyFiat: item.label,
        }));
      },
    };

    openSheet(sheetData, 40, false);
  };

  const handleCountryPress = useCallback(() => {
    if (!countries) return;

    const countryOptions = countries.map((country) => ({
      fullName: country.name,
      label: country.code,
      type: 'fiat' as const,
      value: country.code,
    }));

    const sheetData: BottomSheetListData = {
      type: 'currency',
      data: countryOptions,
      onSelect: (item) => {
        const countryCode = item.value;
        const country = countries.find((c) => c.code === countryCode);
        if (country) {
          setState((prev) => ({
            ...prev,
            location: {
              code: country.code,
              name: country.name,
              states: country.states || [],
            },
            states: country.code === 'US' ? country.states || null : null,
            stateInput: '',
            validation: {
              ...prev.validation,
              state: null,
            },
          }));
        }
      },
    };

    openSheet(sheetData, [60, 90], true);
  }, [countries]);

  const handleStateInputChange = (text: string) => {
    setState((prev) => ({
      ...prev,
      stateInput: text,
      validation: {
        ...prev.validation,
        state: null,
      },
    }));
  };

  const handleStateInputBlur = () => {
    if (!state.states) return;

    const stateMatch = state.states.find(
      (s) => s.name.toLowerCase() === state.stateInput.toLowerCase(),
    );

    setState((prev) => ({
      ...prev,
      validation: {
        ...prev.validation,
        state: !!stateMatch,
      },
    }));
  };

  const handleProceed = useCallback(() => {
    const currencyFrom =
      action === 'buy' ? state.currencyFiat : capitalizeAll(currencySymbol);
    const currencyTo =
      action === 'buy' ? capitalizeAll(currencySymbol) : state.currencyFiat;

    const stateCode =
      state.location?.code === 'US'
        ? state.states?.find(
            (s) => s.name.toLowerCase() === state.stateInput.toLowerCase(),
          )?.code
        : null;

    navigateViaBottomTabs('Wallet', 'RampConfirm', {
      currencyFrom,
      currencyTo,
      amount: state.amount,
      location: state.location?.code || '',
      action,
      addressToUse,
      state: stateCode,
    });
  }, [action, state.amount, state.location, state.stateInput, state.states]);

  const canProceed =
    state.amount &&
    state.validation.amount &&
    state.location?.code &&
    (!state.states || state.validation.state);

  return {
    state,
    handlers: {
      handleAmountChange,
      handleAmountBlur,
      handleCurrencyPress,
      handleCountryPress,
      handleStateInputChange,
      handleStateInputBlur,
      handleProceed,
    },
    canProceed,
  };
};
