import React, {useMemo} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, View} from 'react-native';

import LoadingHandler from '@/components/LoadingHandler';
import MButton from '@/components/MButton';
import Switch from '@/components/Switch';
import GlobalStyles from '@/constants/GlobalStyles';
import type {ActionType} from './_types';
import RampOffersList from './components/RampOffersList';
import {useRampConfirm} from './hooks/useRampConfirm';

type RouteParams = {
  currencyFrom: string;
  currencyTo: string;
  amount: string;
  location: string;
  action: ActionType;
  addressToUse: string;
  state: string | null;
};

const RampConfirm: React.FC<any> = ({route}) => {
  const {t} = useTranslation();
  const {currencyFrom, currencyTo, action} = route.params as RouteParams;

  const {
    state: confirmState,
    offers,
    isLoading,
    error,
    canProceed,
    handlers,
  } = useRampConfirm(route.params);

  const validOffers = useMemo(
    () => offers.filter((offer) => !offer.errorMessage),
    [offers],
  );

  console.log('validOffers', validOffers);

  if (isLoading) return <LoadingHandler />;

  if (error)
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.error}>Something went wrong, try again.</Text>
      </View>
    );

  return (
    <>
      <View style={styles.root}>
        <RampOffersList
          offers={offers}
          onOfferPress={handlers.handleOfferPress}
          activeTab={action}
          currencyFrom={currencyFrom}
          currencyTo={currencyTo}
        />
      </View>

      {validOffers.length > 0 && (
        <View style={styles.bottomContainer}>
          <View style={styles.consent}>
            <Switch
              label="I hereby acknowledge and accept the KYC and AML policies and procedures."
              isEnabled={confirmState.switched}
              handleSwitch={handlers.handleSwitchChange}
              link="https://changelly.com/aml-kyc"
            />
          </View>

          <MButton
            text="Complete"
            onPress={handlers.handleProceed}
            disabled={!canProceed}
            isLoading={confirmState.loading}
          />
        </View>
      )}
    </>
  );
};

export default RampConfirm;

const styles = StyleSheet.create({
  root: {
    flex: 1,
    paddingHorizontal: 13,
  },
  consent: {
    marginBottom: 10,
    paddingLeft: 6,
    paddingRight: 18,
  },
  bottomContainer: {
    paddingHorizontal: 16,
    paddingBottom: 10,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  error: {
    fontSize: 16,
    color: GlobalStyles.base.black,
    textAlign: 'center',
    marginTop: 20,
  },
});
