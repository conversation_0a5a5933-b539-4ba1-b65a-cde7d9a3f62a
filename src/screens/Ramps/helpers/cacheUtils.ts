import RNFS from 'react-native-fs';
import type {ActionType} from '../_types';

export const CACHE_DURATIONS = {
  COUNTRIES: 14 * 24 * 60 * 60 * 1000, // 14 days
  CURRENCIES: 7 * 24 * 60 * 60 * 1000, // 7 days
  PROVIDERS: 5 * 60 * 1000, // 5 minutes
} as const;

export const getCacheFilePath = (prefix: string, type: ActionType) =>
  RNFS.DocumentDirectoryPath + `/${prefix}.${type}.json`;

export const readCache = async <T>(filePath: string): Promise<T | null> => {
  try {
    const cachedData = await RNFS.readFile(filePath, 'utf8');
    return JSON.parse(cachedData) as T;
  } catch (e) {
    return null;
  }
};

export const writeCache = async <T>(filePath: string, data: T): Promise<void> => {
  try {
    await RNFS.writeFile(filePath, JSON.stringify(data), 'utf8');
  } catch (e) {
    console.error('Error writing cache:', e);
  }
};

export const isCacheValid = async (
  filePath: string,
  staleDuration: number,
): Promise<boolean> => {
  try {
    const fileInfo = await RNFS.stat(filePath);
    const cacheTimestamp = new Date(fileInfo.mtime).getTime();
    return Date.now() - cacheTimestamp <= staleDuration;
  } catch (e) {
    return false;
  }
};

export const createCacheManager = <T>(prefix: string, staleDuration: number) => {
  return {
    getCachePath: (type: ActionType) => getCacheFilePath(prefix, type),
    readFromCache: async (type: ActionType): Promise<T | null> => {
      const path = getCacheFilePath(prefix, type);
      return readCache<T>(path);
    },
    writeToCache: async (type: ActionType, data: T): Promise<void> => {
      const path = getCacheFilePath(prefix, type);
      return writeCache(path, data);
    },
    isValid: async (type: ActionType): Promise<boolean> => {
      const path = getCacheFilePath(prefix, type);
      return isCacheValid(path, staleDuration);
    },
  };
};
