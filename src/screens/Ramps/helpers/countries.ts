import type {Country, ActionType} from '../_types';
import {fetchBuyCountries, fetchSellCountries} from './rampsChangellyApi';
import {CACHE_DURATIONS, createCacheManager} from './cacheUtils';

const countryCache = createCacheManager<Country[]>(
  'countries',
  CACHE_DURATIONS.COUNTRIES,
);

const formatCountryData = (data: any[]): Country[] => {
  return data.map((item) => ({
    code: item.code,
    name: item.name,
    ...(item.code === 'US' && item.states
      ? {
          states: item.states.map((state: any) => ({
            code: state.code,
            name: state.name,
          })),
        }
      : {}),
  }));
};

export const fetchDataAndStore = async (type: ActionType): Promise<Country[]> => {
  try {
    const fetchFn = type === 'buy' ? fetchBuyCountries : fetchSellCountries;
    const data = await fetchFn();
    const formattedData = formatCountryData(data);
    await countryCache.writeToCache(type, formattedData);
    return formattedData;
  } catch (e) {
    console.error(`Error fetching ${type} countries data:`, e);
    throw e;
  }
};

export const readCachedData = async (type: ActionType): Promise<Country[] | null> => {
  return countryCache.readFromCache(type);
};

export const isCountryCacheValid = async (type: ActionType): Promise<boolean> => {
  return countryCache.isValid(type);
};
