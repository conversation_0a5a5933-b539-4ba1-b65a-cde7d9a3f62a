import {RampsInstance as api} from '@/services/BackendServices';
import {RampOffer} from '../_types';

export const getOrders = async () => {
  const {data} = await api.get('/orders');
  return data;
};

export const getProviders = async () => {
  const {data} = await api.get('/providers');
  return data;
};

/* ============================================================================================== */
/*                                             ON-RAMP                                            */
/* ============================================================================================== */

export const fetchBuyCurrencies = async () => {
  const {data} = await api.get('/currencies?supportedFlow=buy');
  return data;
};

export const fetchBuyCountries = async () => {
  const {data} = await api.get('/available-countries?supportedFlow=buy');
  return data;
};

export const fetchBuyOffers = async (
  params: Record<string, string>,
): Promise<RampOffer[]> => {
  const {data} = await api.get('/offers', {params});
  return data;
};

export const createBuyOrder = async (orderData: any): Promise<any> => {
  const {data} = await api.post('/orders', orderData);
  return data;
};

/* ============================================================================================== */
/*                                            OFF-RAMP                                            */
/* ============================================================================================== */

export const fetchSellCurrencies = async () => {
  const {data} = await api.get('/currencies?supportedFlow=sell');
  return data;
};

export const fetchSellCountries = async () => {
  const {data} = await api.get('/available-countries?supportedFlow=sell');
  return data;
};

export const fetchSellOffers = async (
  params: Record<string, string>,
): Promise<RampOffer[]> => {
  const {data} = await api.get('/sell/offers', {params});
  return data;
};

export const createSellOrder = async (orderData: any): Promise<any> => {
  const {data} = await api.post('/sell/orders', orderData);
  return data;
};
