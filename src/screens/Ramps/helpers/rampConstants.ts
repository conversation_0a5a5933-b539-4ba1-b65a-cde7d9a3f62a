import {CurrencyOption} from '@/components/BottomSheetList';
import {RampConfirmState} from '../hooks/useRampConfirm';
import {RampDetailsState} from '../hooks/useRampDetails';

export const TABS: [string, string] = ['buy', 'sell'];
export const MIN_AMOUNT_FOR_BUY = 50;

export const CURRENCY_OPTIONS: CurrencyOption[] = [
  {
    fullName: 'US Dollar',
    label: 'USD',
    type: 'fiat',
    value: 'US',
  },
  {
    fullName: 'Euro',
    label: 'EUR',
    type: 'fiat',
    value: 'EU',
  },
];

export const DETAILS_INITIAL_STATE: RampDetailsState = {
  amount: '',
  currencyFiat: 'USD',
  location: {
    code: 'BG',
    name: 'Bulgaria',
    states: [],
  },
  states: null,
  stateInput: '',
  validation: {
    amount: null,
    state: null,
  },
};

export const CONFIRM_INITIAL_STATE: RampConfirmState = {
  chosenOffer: null,
  switched: false,
  loading: false,
};
