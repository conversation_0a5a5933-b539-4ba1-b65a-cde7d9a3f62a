import type {ActionType} from '../_types';
import {fetchBuyCurrencies, fetchSellCurrencies} from './rampsChangellyApi';
import {CACHE_DURATIONS, createCacheManager} from './cacheUtils';

const currencyCache = createCacheManager<any[]>('currencies', CACHE_DURATIONS.CURRENCIES);

export const fetchDataAndStore = async (type: ActionType): Promise<any[]> => {
  try {
    const fetchFn = type === 'buy' ? fetchBuyCurrencies : fetchSellCurrencies;
    const data = await fetchFn();
    await currencyCache.writeToCache(type, data);
    return data;
  } catch (e) {
    console.error(`Error fetching ${type} currencies data:`, e);
    throw e;
  }
};

export const readCachedData = async (type: ActionType): Promise<any[] | null> => {
  return currencyCache.readFromCache(type);
};

export const isCurrencyCacheValid = async (type: ActionType): Promise<boolean> => {
  return currencyCache.isValid(type);
};
