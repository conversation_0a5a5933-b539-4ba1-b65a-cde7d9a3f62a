import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';

import DetectSwipe from '@/components/DetectSwipe';
import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import MButton from '@/components/MButton';
import Slider from '@/components/Slider';
import GlobalStyles from '@/constants/GlobalStyles';
import RampForm from '@/screens/Ramps/components/RampForm';
import {ActionType} from './_types';
import {TABS} from './helpers/rampConstants';
import {useRampDetails} from './hooks/useRampDetails';

const RampDetails: React.FC<any> = ({route}) => {
  const {t} = useTranslation();
  const {currency, currencySymbol, tokenPrice, addressToUse, blockchainSymbol} =
    route.params;

  const [action, setAction] = useState<ActionType>('buy');
  const {state, handlers, canProceed} = useRampDetails(
    action,
    currencySymbol,
    addressToUse,
  );

  const handleTabChange = (tabName: string) => {
    setAction(tabName as ActionType);
  };

  const handleSwipeLeft = () => {
    if (action === 'buy') {
      setAction('sell');
    }
  };

  const handleSwipeRight = () => {
    if (action === 'sell') {
      setAction('buy');
    }
  };

  return (
    <>
      <KeyboardAvoidingView style={styles.keyboardAvoidingView}>
        <View style={styles.screen}>
          <Slider tabs={TABS} activeTab={action} onTabClick={handleTabChange} />

          <View style={styles.content}>
            <DetectSwipe onSwipeLeft={handleSwipeLeft} onSwipeRight={handleSwipeRight}>
              <RampForm
                currency={currency}
                currencySymbol={currencySymbol}
                tokenPrice={tokenPrice}
                action={action}
                state={state}
                handlers={handlers}
                blockchainSymbol={blockchainSymbol}
              />
            </DetectSwipe>
          </View>
        </View>

        <View style={styles.footer}>
          <MButton
            text="Choose Offer"
            onPress={handlers.handleProceed}
            disabled={!canProceed}
          />
        </View>
      </KeyboardAvoidingView>
    </>
  );
};

export default RampDetails;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  topNavTitleContainer: {
    justifyContent: 'flex-start',
    marginLeft: -210,
  },
  topNavTitle: {
    fontSize: 19,
  },
  screen: {
    flex: 1,
  },
  content: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 12,
  },
  footer: {
    position: 'absolute',
    bottom: 16,
    width: '90%',
    alignSelf: 'center',
  },
});
