import React, {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {parsePrice} from '@/utils/parsing';
import type {ActionType, Provider, RampOffer} from '../_types';
import ProviderIcon from './RampProviderIcon';

type RampOfferCardProps = {
  offer: RampOffer;
  provider: Provider | undefined;
  onPress: (offer: RampOffer) => void;
  isFocused: boolean;
  activeTab: ActionType;
  currencyFrom: string;
  currencyTo: string;
};

const RampOfferCard: React.FC<RampOfferCardProps> = memo(
  ({offer, provider, onPress, isFocused, activeTab, currencyFrom, currencyTo}) => {
    const renderAmountInfo = () => {
      if (activeTab === 'buy') {
        return (
          <>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>You Pay</Text>

              <Text style={[styles.infoValue, isFocused && styles.focusedText]}>
                {offer.amountFrom} {currencyFrom}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>You Receive</Text>

              <Text style={[styles.infoValue, isFocused && styles.focusedText]}>
                {Number(parsePrice(offer.amountExpectedTo)).toFixed(8)} {currencyTo}
              </Text>
            </View>
          </>
        );
      }

      return (
        <>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>You Send</Text>

            <Text style={[styles.infoValue, isFocused && styles.focusedText]}>
              {offer.amountFrom} {currencyFrom}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>You Get</Text>

            <Text style={[styles.infoValue, isFocused && styles.focusedText]}>
              {Number(offer.amountExpectedTo)} {currencyTo}
            </Text>
          </View>
        </>
      );
    };

    return (
      <TouchableOpacity
        style={[styles.container, isFocused && styles.focusedContainer]}
        onPress={() => onPress(offer)}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <View style={styles.providerSection}>
            {provider?.iconUrl && (
              <ProviderIcon iconUrl={provider.iconUrl} providerName={provider.name} />
            )}

            <Text style={[styles.providerName, isFocused && styles.focusedText]}>
              {provider?.name || offer.providerCode}
            </Text>
          </View>
          <View style={styles.feeSection}>
            <Text style={[styles.feeLabel, isFocused && styles.focusedSubText]}>Fee</Text>

            <Text style={[styles.feeValue, isFocused && styles.focusedText]}>
              {Number(offer.fee).toFixed(2)}{' '}
              {activeTab === 'buy' ? currencyFrom : currencyTo}
            </Text>
          </View>
        </View>

        <View style={styles.divider} />

        <View style={styles.content}>{renderAmountInfo()}</View>
      </TouchableOpacity>
    );
  },
);

export default RampOfferCard;

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray300,
    backgroundColor: GlobalStyles.base.white,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  focusedContainer: {
    borderColor: GlobalStyles.gray.gray600,
    backgroundColor: GlobalStyles.primary.primary50,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  providerSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  providerName: {
    marginLeft: 10,
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  feeSection: {
    alignItems: 'flex-end',
  },
  feeLabel: {
    fontSize: 12,
    color: GlobalStyles.gray.gray800,
    marginBottom: 2,
  },
  feeValue: {
    fontSize: 12,
    fontWeight: '500',
    color: GlobalStyles.base.black,
  },
  divider: {
    height: 1,
    backgroundColor: GlobalStyles.gray.gray200,
    marginVertical: 12,
  },
  content: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 12,
    color: GlobalStyles.gray.gray800,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  focusedText: {
    color: GlobalStyles.primary.primary700,
  },
  focusedSubText: {
    color: GlobalStyles.primary.primary500,
  },
});
