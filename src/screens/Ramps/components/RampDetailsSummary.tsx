import React, {useMemo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import Logo from '@/components/Logo/AssetLogo';
import GlobalStyles from '@/constants/GlobalStyles';
import {capitalize, capitalizeAll, formatNumber} from '@/utils';
import {ActionType} from '../_types';

interface RampDetailsSummaryProps {
  action: ActionType;
  amount: string;
  currencyFiat: string;
  tokenPrice: string;
  cryptoName: string;
  cryptoSymbol: string;
  amountValidation: boolean;
  blockchainSymbol?: string;
}

const RampDetailsSummary: React.FC<RampDetailsSummaryProps> = ({
  action,
  amount,
  currencyFiat,
  tokenPrice,
  cryptoName,
  cryptoSymbol,
  amountValidation,
  blockchainSymbol,
}) => {
  const approximateValue = useMemo(() => {
    if (!amount) return '0';

    const numericAmount = Number(amount);
    const numericTokenPrice = Number(tokenPrice);

    if (action === 'buy') {
      const rate = currencyFiat === 'EUR' ? 0.92 : 1;
      const value = (numericAmount * rate) / numericTokenPrice;
      return formatNumber(value);
    }
    return formatNumber(numericAmount * numericTokenPrice, {decimals: 0});
  }, [amount, currencyFiat, tokenPrice, action]);

  const cryptoPrice = useMemo(() => {
    const numericTokenPrice = Number(tokenPrice);
    const rate = currencyFiat === 'EUR' ? 0.92 : 1;
    return formatNumber(numericTokenPrice * rate, {decimals: 4});
  }, [tokenPrice, currencyFiat]);

  const showLogo = `${cryptoSymbol}${
    blockchainSymbol ? capitalizeAll(blockchainSymbol) : ''
  }SVG`;

  return (
    <View style={styles.summaryContainer}>
      <View style={styles.logoContainer}>
        <Logo name={showLogo} />
        <Text style={styles.currencyName}>{capitalize(cryptoName)}</Text>
      </View>

      <View style={styles.priceContainer}>
        <Text style={styles.priceLabel}>Current Price</Text>
        <Text style={styles.priceValue}>
          {cryptoPrice} {currencyFiat}
        </Text>
      </View>

      {amountValidation && (
        <View style={styles.approximateContainer}>
          <Text style={styles.approximateValue}>
            Approximate value: {approximateValue}{' '}
            {action === 'buy' ? cryptoSymbol : currencyFiat}
          </Text>
        </View>
      )}
    </View>
  );
};

export default RampDetailsSummary;

const styles = StyleSheet.create({
  summaryContainer: {
    marginTop: 42,
    padding: 16,
    backgroundColor: GlobalStyles.gray.gray100,
    borderRadius: 8,
    elevation: 3,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  currencyName: {
    marginLeft: 12,
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  priceContainer: {
    marginBottom: 12,
  },
  priceLabel: {
    fontSize: 12,
    color: GlobalStyles.gray.gray900,
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  approximateContainer: {
    borderTopWidth: 1,
    borderTopColor: GlobalStyles.gray.gray300,
    paddingTop: 12,
  },
  approximateValue: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    fontStyle: 'italic',
  },
});
