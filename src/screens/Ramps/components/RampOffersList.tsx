import React, {useMemo, useState} from 'react';
import {FlatList, ListRenderItemInfo, StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import type {ActionType, RampOffer} from '../_types';
import {useProviders} from '../hooks/useProviders';
import RampOfferCard from './RampOfferCard';

type RampOffersListProps = {
  offers: RampOffer[];
  onOfferPress: (offer: RampOffer | null) => void;
  activeTab: ActionType;
  currencyFrom: string;
  currencyTo: string;
};

const RampOffersList: React.FC<RampOffersListProps> = ({
  offers,
  onOfferPress,
  activeTab,
  currencyFrom,
  currencyTo,
}) => {
  const [focusedOfferCode, setFocusedOfferCode] = useState<string | null>(null);
  const {data: providers} = useProviders();

  const validOffers = useMemo(
    () => offers.filter((offer) => !offer.errorMessage),
    [offers],
  );

  const handleOfferPress = (offer: RampOffer | null) => {
    const newFocusedOfferCode =
      offer?.providerCode === focusedOfferCode ? null : offer?.providerCode;
    setFocusedOfferCode(newFocusedOfferCode!);
    onOfferPress(newFocusedOfferCode ? offer : null);
  };

  const renderItem = ({item}: ListRenderItemInfo<RampOffer>) => {
    const provider = providers?.find((p) => p.code === item.providerCode);

    return (
      <RampOfferCard
        offer={item}
        provider={provider}
        onPress={handleOfferPress}
        isFocused={focusedOfferCode === item.providerCode}
        activeTab={activeTab}
        currencyFrom={currencyFrom}
        currencyTo={currencyTo}
      />
    );
  };

  if (validOffers.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No offers available at the moment</Text>
        <Text style={styles.emptySubtext}>
          Please try again later or adjust your amount
        </Text>
      </View>
    );
  }

  return (
    <FlatList
      data={validOffers}
      renderItem={renderItem}
      keyExtractor={(item) => item.providerCode}
      contentContainerStyle={styles.listContent}
      showsVerticalScrollIndicator={false}
    />
  );
};

export default RampOffersList;

const styles = StyleSheet.create({
  listContent: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 22,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 18,
    color: GlobalStyles.gray.gray800,
    textAlign: 'center',
  },
});
