import React, {useMemo} from 'react';
import {useTranslation} from 'react-i18next';
import {ScrollView, StyleSheet, Text, TextInput, View} from 'react-native';

import type {CurrencyOption} from '@/components/BottomSheetList';
import CurrencyInput from '@/components/CurrencyInput';
import SelectCountryOption from '@/components/SelectCountryOption';
import GlobalStyles from '@/constants/GlobalStyles';
import {capitalize} from '@/utils';
import {getFontSize, NameParsing} from '@/utils/parsing';
import type {ActionType} from '../_types';
import {MIN_AMOUNT_FOR_BUY} from '../helpers/rampConstants';
import {RampDetailsState} from '../hooks/useRampDetails';
import RampDetailsSummary from './RampDetailsSummary';

type RampFormProps = {
  currency: string;
  currencySymbol: string;
  tokenPrice: string;
  action: ActionType;
  state: RampDetailsState;
  handlers: {
    handleAmountChange: (value: string) => void;
    handleAmountBlur: () => void;
    handleCurrencyPress: () => void;
    handleCountryPress: () => void;
    handleStateInputChange: (text: string) => void;
    handleStateInputBlur: () => void;
  };
  blockchainSymbol?: string;
};

export const RampForm: React.FC<RampFormProps> = ({
  currency,
  currencySymbol,
  tokenPrice,
  action,
  state,
  handlers,
  blockchainSymbol,
}) => {
  const {t} = useTranslation();

  const selectedCurrency = useMemo((): CurrencyOption => {
    if (action === 'sell') {
      return {
        fullName: capitalize(currency),
        label: currencySymbol,
        type: 'crypto',
        value: currencySymbol,
      };
    }
    return {
      fullName: state.currencyFiat === 'EUR' ? 'Euro' : 'US Dollar',
      label: state.currencyFiat,
      type: 'fiat',
      value: state.currencyFiat === 'EUR' ? 'EU' : 'US',
    };
  }, [action, currency, currencySymbol, state.currencyFiat]);

  return (
    <ScrollView contentContainerStyle={styles.scrollContent}>
      <View style={styles.content}>
        <View style={styles.inputContainer}>
          <CurrencyInput
            label={t('exchange.payinInputLabel')}
            value={state.amount}
            selectedOption={selectedCurrency}
            onOptionPress={handlers.handleCurrencyPress}
            onChangeText={handlers.handleAmountChange}
            onBlur={handlers.handleAmountBlur}
            optionsDisabled={action === 'sell'}
            decimals={action === 'sell' ? 8 : 0}
          />

          {state.validation.amount === false && (
            <Text style={styles.errorText}>
              The amount should be greater than {MIN_AMOUNT_FOR_BUY}.
            </Text>
          )}
        </View>

        <View style={[styles.inputContainer, {marginTop: 10}]}>
          <SelectCountryOption
            countryCode={state.location?.code || 'BG'}
            countryName={state.location?.name || 'Bulgaria'}
            onPress={handlers.handleCountryPress}
          />

          {state.location?.code === 'US' && (
            <View style={styles.stateContainer}>
              <Text style={styles.inputLabel}>State</Text>

              <TextInput
                value={state.stateInput}
                placeholder="Enter state..."
                onChangeText={handlers.handleStateInputChange}
                onBlur={handlers.handleStateInputBlur}
                style={[
                  styles.textInput,
                  state.validation.state && styles.inputError,
                  state.validation.state && styles.inputValidated,
                ]}
                placeholderTextColor={GlobalStyles.gray.gray600}
              />

              {state.validation.state === false && (
                <Text style={styles.errorText}>Please enter a valid US state.</Text>
              )}
            </View>
          )}

          <RampDetailsSummary
            action={action}
            amount={state.amount}
            currencyFiat={state.currencyFiat}
            tokenPrice={tokenPrice}
            cryptoName={NameParsing(currency.toLowerCase())}
            cryptoSymbol={currencySymbol}
            amountValidation={state.validation.amount ?? false}
            blockchainSymbol={blockchainSymbol}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default RampForm;

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  content: {
    flex: 1,
  },
  inputContainer: {
    marginTop: 32,
    marginBottom: 12,
  },
  stateContainer: {
    marginTop: 22,
  },
  inputLabel: {
    marginBottom: 5,
    marginLeft: 2,
    fontSize: getFontSize(18),
    fontWeight: '600',
    color: GlobalStyles.primary.primary600,
  },
  textInput: {
    height: 60,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    borderRadius: 8,
    paddingHorizontal: 14,
    fontSize: getFontSize(16),
    fontWeight: '500',
    color: GlobalStyles.base.black,
  },
  inputError: {
    borderColor: GlobalStyles.error.error500,
  },
  inputValidated: {
    borderColor: GlobalStyles.success.success500,
  },
  errorText: {
    marginTop: 4,
    marginLeft: 2,
    fontSize: getFontSize(14),
    color: GlobalStyles.error.error500,
  },
});
