import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';
import {SvgUri} from 'react-native-svg';

import BanxaLogo from '@/assets/providers/banxa.svg';
import KadoLogo from '@/assets/providers/logo-kado.svg';
import GlobalStyles from '@/constants/GlobalStyles';

const RampProviderIcon: React.FC<{iconUrl: string; providerName: string}> = memo(
  ({iconUrl, providerName}) => {
    const renderIcon = () => {
      if (providerName.toLowerCase() === 'banxa') {
        return <BanxaLogo width={62} height={62} style={styles.icon} />;
      } else if (providerName.toLowerCase() === 'kado') {
        return <KadoLogo width={34} height={34} style={styles.icon} />;
      } else {
        return <SvgUri width="62" height="62" uri={iconUrl} style={styles.icon} />;
      }
    };

    return <View style={styles.iconContainer}>{renderIcon()}</View>;
  },
);

export default RampProviderIcon;

const styles = StyleSheet.create({
  iconContainer: {
    width: 72,
    height: 72,
    borderRadius: 40,
    backgroundColor: GlobalStyles.base.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 9,
    elevation: 4,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  icon: {
    width: '100%',
    height: '100%',
  },
});
