import React from 'react';
import {View} from 'react-native';
import AssetComponent from '../../../Assets/components/Asset/Asset';
import ProviderComponent from '../../../Providers/components/Provider/Provider';
import styles from './styles';
import {Asset, Provider} from '../../../topAssetsUtils';

interface Props {
  providerComponents: Provider[];
  assetComponents: Asset[];
}

const isProvider = (item: Provider | Asset): item is Provider => {
  return (item as Provider).name !== undefined;
};

const isAsset = (item: Provider | Asset): item is Asset => {
  return (item as Asset).symbol !== undefined;
};

const renderItem = ({item, index}: {item: Provider | Asset; index: number}) => {
  if (isProvider(item)) {
    return (
      <ProviderComponent
        name={item.name}
        AUM={item.AUM}
        key={index}
        STAKING_WALLETS={item.STAKING_WALLETS}
      />
    );
  } else if (isAsset(item)) {
    return (
      <AssetComponent
        tokenPrice={item.tokenPrice}
        symbol={item.symbol}
        key={index}
      />
    );
  }
};

const List = ({providerComponents, assetComponents}: Props) => {
  const combinedList = [...providerComponents, ...assetComponents];

  return (
    <View>
      {combinedList.length !== 0 && (
        <View style={styles.listContainer}>
          {combinedList.map((item, index) => {
            return renderItem({item, index});
          })}
        </View>
      )}
    </View>
  );
};

export default List;
