import React, {useEffect, useState} from 'react';
import {View, ScrollView} from 'react-native';
import styles from './styles';
import List from './components/List/List';
import {Asset, Provider} from '../topAssetsUtils';

interface AllListProps {
  providers: Provider[];
  assets: Asset[];
  search: string;
}

const All: React.FC<AllListProps> = ({providers, assets, search}) => {
  const [filteredProviders, setFilteredProviders] = useState(providers);
  const [filteredAssets, setFilteredAssets] = useState(assets);

  useEffect(() => {
    if (search === '') {
      setFilteredProviders(providers);
      setFilteredAssets(assets);
    } else {
      setFilteredProviders(
        providers.filter(item =>
          item.name.toLowerCase().includes(search.toLowerCase()),
        ),
      );
      setFilteredAssets(
        assets.filter(item =>
          item.symbol.toLowerCase().includes(search.toLowerCase()),
        ),
      );
    }
  }, [search]);

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <List
          providerComponents={filteredProviders}
          assetComponents={filteredAssets}
        />
      </ScrollView>
    </View>
  );
};

export default All;
