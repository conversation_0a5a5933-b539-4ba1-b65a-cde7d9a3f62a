import {View} from 'react-native';
import {styles} from './styles';
import FixedText from '../../../../../components/FixedText/FixedText';
import {providerLogo} from '../../../../../utils/components/providerLogo';
import {useTranslation} from 'react-i18next';

type ProviderComponentProps = {
  name: string;
  AUM: string;
  STAKING_WALLETS: string;
};

const ProviderComponent = ({name, AUM, STAKING_WALLETS}: ProviderComponentProps) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <View style={styles.tokenLogoContainer}>{providerLogo(name)}</View>
        <View style={styles.tokenInfoContainer}>
          <FixedText style={styles.tokenName}>{name}</FixedText>
          <FixedText style={styles.text}>
            {t('topAssets.topProviders_stakedValue')}
          </FixedText>
          <FixedText style={styles.text}>{t('topAssets.topProviders_users')}</FixedText>
        </View>
      </View>
      <View style={styles.rightContainer}>
        <FixedText style={styles.tokenAmount}>{AUM}</FixedText>
        <FixedText style={styles.calculatedPrice}>{STAKING_WALLETS}</FixedText>
      </View>
    </View>
  );
};

export default ProviderComponent;
