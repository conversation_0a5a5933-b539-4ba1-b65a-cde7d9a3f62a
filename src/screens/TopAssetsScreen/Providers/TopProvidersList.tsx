import React, {useEffect, useState} from 'react';
import {View, Text, ScrollView} from 'react-native';
import styles from './styles';
import ProviderComponent from './components/Provider/Provider';
import {useTranslation} from 'react-i18next';
import FixedText from '../../../components/FixedText/FixedText';

// Define the types for the props
type ProviderList = {
  name: string;
  logoUrl: any;
  AUM: string;
  AUM_CHANGE: string;
  STAKING_WALLETS: string;
};

interface TopProvidersListProps {
  providers: ProviderList[];
  search: string;
}

const TopProvidersList: React.FC<TopProvidersListProps> = ({providers, search}) => {
  const [filteredProvider, setFilteredProvider] = useState(providers);
  const {t} = useTranslation();
  useEffect(() => {
    if (search === '') {
      setFilteredProvider(providers);
    } else {
      setFilteredProvider(
        providers.filter((item) =>
          item.name.toLowerCase().includes(search.toLowerCase()),
        ),
      );
    }
  }, [search]);

  return (
    <ScrollView style={styles.container}>
      {filteredProvider !== undefined && filteredProvider.length !== 0 ? (
        filteredProvider.map((item, index) => {
          return (
            <ProviderComponent
              name={item.name}
              AUM={item.AUM}
              key={index}
              STAKING_WALLETS={item.STAKING_WALLETS}
            />
          );
        })
      ) : (
        <View>
          <FixedText>{t('topAssets.noProviders')}</FixedText>
        </View>
      )}
    </ScrollView>
  );
};

export default TopProvidersList;
