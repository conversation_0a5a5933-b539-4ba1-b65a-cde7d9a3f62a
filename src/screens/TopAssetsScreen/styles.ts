import { StyleSheet } from 'react-native';
import GlobalStyles from '../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  searchContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',

    alignSelf: 'center',
    backgroundColor: GlobalStyles.base.white,
    // add border
    borderRadius: 8,
    marginBottom: 20,
    paddingVertical: 6,
    width: '92%',
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    paddingLeft: 10
  },
  searchInput: {
    width: '80%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontWeight: '500',
    marginLeft: 10
  },
  searchIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    marginHorizontal: 10,
    tintColor: GlobalStyles.gray.gray900
  },
  navContainer: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    paddingBottom: 10
  },
  listContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    marginTop: -10,
    height: '100%'
  },
  safeArea: {backgroundColor: GlobalStyles.gray.gray300}
});
