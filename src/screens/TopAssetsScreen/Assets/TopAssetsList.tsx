import React, {useEffect, useState} from 'react';
import {View, Text, ScrollView} from 'react-native';
import styles from './styles';
import AssetComponent from './components/Asset/Asset';
import {useTranslation} from 'react-i18next';
import {Asset} from '../topAssetsUtils';
import FixedText from '../../../components/FixedText/FixedText';

interface TopAssetsListProps {
  assets: Asset[];
  search: string;
}

const TopAssetsList: React.FC<TopAssetsListProps> = ({assets, search}) => {
  const {t} = useTranslation();
  const [filteredAssets, setFilteredAssets] = useState(assets);

  useEffect(() => {
    if (search === '') {
      setFilteredAssets(assets);
    } else {
      setFilteredAssets(
        assets.filter((item) => item.symbol.toLowerCase().includes(search.toLowerCase())),
      );
    }
  }, [search]);

  return (
    <ScrollView style={styles.container}>
      {filteredAssets.length !== 0 ? (
        filteredAssets.map((item, index) => {
          return (
            <AssetComponent
              tokenPrice={item.tokenPrice}
              symbol={item.symbol}
              key={index}
            />
          );
        })
      ) : (
        <View>
          <FixedText>{t('topAssets.noAssets')}</FixedText>
        </View>
      )}
    </ScrollView>
  );
};

export default TopAssetsList;
