import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 4,
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    paddingHorizontal: 8,
    width: '100%',
    paddingVertical: 14,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  leftContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenLogoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: GlobalStyles.primary.primary100,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  tokenLogo: {
    width: 40,
    height: 40,
  },
  tokenInfoContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  tokenName: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  tokenAmount: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  rightContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  tokenPriceUp: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.success.success700,
    fontStyle: 'normal',
  },
  tokenPriceDown: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.error.error500,
    fontStyle: 'normal',
  },
  calculatedPrice: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray900,
    fontStyle: 'normal',
  },
});
