import {View} from 'react-native';
import {styles} from './styles';
import FixedText from '../../../../../components/FixedText/FixedText';
import Logo from '../../../../../components/Logo/AssetLogo';

type AssetComponentProps = {
  symbol: string;
  tokenPrice?: string;
};

const AssetComponent = ({symbol, tokenPrice}: AssetComponentProps) => {
  let tokenSymbol = `${symbol}Svg`.toUpperCase();

  if (symbol === 'ETC') {
    tokenSymbol = 'ETHClassicSVG';
  }

  const price = tokenPrice != undefined ? tokenPrice : '0.00';
  return (
    <View style={styles.container}>
      <View style={styles.leftContainer}>
        <View style={styles.tokenLogoContainer}>
          <Logo name={tokenSymbol} />
        </View>
        <View style={styles.tokenInfoContainer}>
          <FixedText style={styles.tokenName}>{symbol}</FixedText>

          <FixedText style={styles.tokenPriceUp}>${price}</FixedText>
        </View>
      </View>
    </View>
  );
};

export default AssetComponent;
