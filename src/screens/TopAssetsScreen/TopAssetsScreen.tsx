import {STAKING_CALCULATOR_BACKEND_SERVICE} from '@env';
import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {connect} from 'react-redux';

import MagnifyingGlass from '@/assets/icons/magnifying glass.svg';
import LoadingHandler from '@/components/LoadingHandler';
import NavButton from '@/components/NavButton/NavButton';
import TopNavigation from '@/components/TopNavigation';
import GlobalStyles from '@/constants/GlobalStyles';
import {PriceFetchingInstance} from '@/services/BackendServices';
import {AxiosResponse} from 'axios';
import {FlatList, SafeAreaView, TextInput, View} from 'react-native';
import All from './All/All';
import TopAssetsList from './Assets/TopAssetsList';
import TopProvidersList from './Providers/TopProvidersList';
import {styles} from './styles';
import {Asset, Provider} from './topAssetsUtils';

type TopAssetsScreenProps = {
  currency: string;
};

const MagnifyingGlassSvg = () => <MagnifyingGlass width={24} height={24} />;

const TopAssetsScreen = (props: TopAssetsScreenProps) => {
  const {t} = useTranslation();
  const viewFocused = useIsFocused();

  const navigation = ['topAssets', 'topProviders', 'favorites', 'all'];
  const assetsUrl = `${STAKING_CALCULATOR_BACKEND_SERVICE}/assets/`;
  const providersUrl = `${STAKING_CALCULATOR_BACKEND_SERVICE}/providers/`;

  const [assets, setAssets] = useState<Asset[]>([]);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedNav, setSelectedNav] = useState<string>(navigation[0]);
  const [searchText, setSearchText] = useState<string>('');

  const fetchAssets = async () => {
    try {
      const response = await fetch(assetsUrl);
      const data = await response.json();
      let updatedAssets: Asset[] = [];

      data.assets.map((asset: Asset) => {
        PriceFetchingInstance.get(
          `/price/${asset.symbol}?currency=${
            props.currency.toLowerCase() === 'eur' ? 'eur' : 'usd'
          }`,
        ).then((response: AxiosResponse) => {
          if (response.data.price) {
            updatedAssets.push({
              ...asset,
              tokenPrice: response.data.price.price,
            });
          }
        });
      });

      updatedAssets = updatedAssets.sort((a: Asset, b: Asset) => {
        if (parseFloat(a.tokenPrice!) > parseFloat(b.tokenPrice!)) {
          return -1;
        } else if (parseFloat(a.tokenPrice!) < parseFloat(b.tokenPrice!)) {
          return 1;
        } else {
          return 0;
        }
      });

      setAssets(updatedAssets);
      setLoading(false);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchProviders = async () => {
    try {
      const response = await fetch(providersUrl);
      const data = await response.json();

      let updatedProviders: Provider[] = [];

      updatedProviders = data.topProviders.sort((a: Provider, b: Provider) => {
        if (parseFloat(a.AUM) > parseFloat(b.AUM)) {
          return -1;
        } else if (parseFloat(a.AUM) < parseFloat(b.AUM)) {
          return 1;
        } else {
          return 0;
        }
      });

      setProviders(updatedProviders);

      setLoading(false);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (!viewFocused) {
      return;
    }
    setSearchText('');

    if (selectedNav === navigation[0]) {
      if (assets.length === 0) {
        setLoading(true);
        fetchAssets();
      }
    } else if (selectedNav === navigation[1]) {
      if (providers.length === 0) {
        setLoading(true);
        fetchProviders();
      }
    } else {
      if (assets.length === 0 || providers.length === 0) {
        setLoading(true);
        if (assets.length === 0) {
          fetchAssets();
        } else {
          fetchProviders();
        }
      }
    }
  }, [viewFocused, selectedNav]);

  return (
    <>
      <SafeAreaView style={styles.safeArea}>
        <TopNavigation screenTitle={t('topAssets.title')} />
        <View style={styles.searchContainer}>
          <MagnifyingGlassSvg />
          <TextInput
            style={styles.searchInput}
            placeholder={t('topAssets.search')}
            onChangeText={(text) => setSearchText(text)}
            value={searchText}
            returnKeyType="done"
            placeholderTextColor={GlobalStyles.gray.gray900}
          />
        </View>
        <FlatList
          data={navigation}
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          style={styles.navContainer}
          renderItem={({item}) => (
            <NavButton
              title={t(`topAssets.${item}`)}
              onPress={() => {
                setSelectedNav(item);
              }}
              selected={selectedNav === item}
            />
          )}
        />
      </SafeAreaView>
      <View style={GlobalStyles.THEME.mainContainer}>
        <View style={styles.listContainer}>
          {loading ? (
            <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
              <LoadingHandler />
            </View>
          ) : (
            <>
              {selectedNav === 'topAssets' && (
                <TopAssetsList assets={assets} search={searchText} />
              )}
              {selectedNav === 'topProviders' && (
                <TopProvidersList providers={providers} search={searchText} />
              )}
              {selectedNav === 'favorites' && (
                <All providers={providers} assets={assets} search={searchText} />
              )}
              {selectedNav === 'all' && (
                <All providers={providers} assets={assets} search={searchText} />
              )}
            </>
          )}
        </View>
      </View>
    </>
  );
};

const mapStateToProps = (state: any) => {
  return {
    currency: state.common.currency,
  };
};

export default connect(mapStateToProps)(TopAssetsScreen);
