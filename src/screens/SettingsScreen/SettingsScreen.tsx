import analytics from '@react-native-firebase/analytics';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import i18next from 'i18next';
import React, {useCallback, useEffect, useState} from 'react';
import {initReactI18next, useTranslation} from 'react-i18next';
import {Alert, ScrollView, StyleSheet, Switch, Text, View} from 'react-native';
import {
  ArrowRightOnRectangleIcon,
  CurrencyDollarIcon,
  DocumentTextIcon,
  KeyIcon,
  LanguageIcon,
  LockClosedIcon,
  TrashIcon,
  WrenchScrewdriverIcon,
} from 'react-native-heroicons/solid';
import * as Keychain from 'react-native-keychain';
import {connect, useDispatch} from 'react-redux';

import GlobalStyles from '@/constants/GlobalStyles';
import {languageResources} from '@/constants/i18next';
import {useAuth} from '@/hooks/redux';
import {disconnectBreezSDK} from '@/screens/LightningNetwork/helpers/breezApi';
import SettingsItem from '@/screens/SettingsScreen/ui/SettingsItem';
import SettingsSection from '@/screens/SettingsScreen/ui/SettingsSection';
import {logout, setIsAuthenticated} from '@/storage/actions/authActions';
import {changeCurrency, changeLanguage} from '@/storage/actions/sharedActions';
import {AuthAddresses} from '@/types/authTypes';
import SafeAreaInset from '@components/SafeAreaInset';
import theme from '@/styles/themes';
import {showWarningToast} from '@/utils/toast';
import data from '../../../package.json';
import {
  isWalletStoredInKeychain,
  KEYCHAIN_BACKUP_PROVIDER,
} from '../KeychainBackup/helpers-keychain';
import {stripServicePrefix} from '../Onboarding/helpers/import-wallet-helpers';

type SettingsScreenProps = {
  language: string;
  currency: string;
  userAddresses: AuthAddresses;
};

const SettingsScreen: React.FC = (props: any) => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const viewFocused = useIsFocused();
  const {language, currency, userAddresses} = props as SettingsScreenProps;
  const {user} = useAuth();

  const [languageSetting, setLanguageSetting] = useState<string>(language);
  const [currencySetting, setCurrencySetting] = useState<string>(currency);

  useEffect(() => {
    analytics().logEvent('Settings_languageButton_tap');
    i18next.use(initReactI18next).init({
      compatibilityJSON: 'v3',
      lng: languageSetting.toLowerCase(),
      fallbackLng: 'en',
      resources: languageResources,
    });

    dispatch(changeLanguage(languageSetting));
  }, [languageSetting]);

  useEffect(() => {
    analytics().logEvent('Settings_currencyButton_tap');
    dispatch(changeCurrency(currencySetting));
  }, [currencySetting]);

  useEffect(() => {
    analytics().logEvent('Settings_screen_open');
    setLanguageSetting(language);
    setCurrencySetting(currency);
  }, [viewFocused]);

  const handleLogout = async () => {
    try {
      // @ts-ignore
      await disconnectBreezSDK(userAddresses[0].address);
      dispatch(logout());
      analytics().logEvent('Settings_logoutButton_tap');
      props.navigation.navigate('BottomTabs', {screen: 'AuthWallet'});
    } catch (error) {
      console.error('[Settings / Breez SDK] Error during logout:', error);
      dispatch(logout());
      analytics().logEvent('Settings_logoutButton_tap');
      props.navigation.reset({
        index: 0,
        routes: [{name: 'BottomTabs', params: {screen: 'AuthWallet'}}],
      });
    }
  };

  const handlePrivateKey = () => {
    navigation.navigate('PrivateKeys' as never);
  };

  const handleSeedPhrase = () => {
    dispatch(setIsAuthenticated(false));
    navigation.navigate('SeedPhrase');
  };

  const handleKeychain = async () => {
    const {walletLabel, encryptedData, result} = await checkStoredCredentials();

    if (result) {
      // @ts-ignore
      navigation.navigate('Keychain', {
        screen: 'KeychainHome',
        params: {walletLabel, encryptedData},
      });
    } else {
      // @ts-ignore
      navigation.navigate('Keychain', {screen: 'KeychainWalletLabel'});
    }
  };

  const handleTermsAndServices = () => {
    analytics().logEvent('Settings_termsButton_tap');
    navigation.navigate('TermsOfService' as never);
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      t('settings.deleteAccountMessage'),
      '',
      [
        {
          text: t('settings.no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: t('settings.yes'),
          onPress: () => {
            handleLogout();
          },
        },
      ],
      {cancelable: false},
    );
  };

  const handleLogoutConfirmation = () => {
    Alert.alert(
      t('settings.logoutMessage'),
      '',
      [
        {
          text: t('settings.no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: t('settings.yes'),
          onPress: () => {
            handleLogout();
          },
        },
      ],
      {cancelable: false},
    );
  };

  const getAllKeychainPasswordServices = useCallback(async () => {
    const services = await Keychain.getAllGenericPasswordServices();
    return services.filter((service) => service.startsWith('assetify-password_'));
  }, []);

  const checkStoredCredentials = useCallback(async () => {
    try {
      const keychainPasswordServices = await getAllKeychainPasswordServices();
      console.log('keychainPasswordServices', keychainPasswordServices);

      if (keychainPasswordServices.length === 0) {
        return {walletLabel: null, encryptedData: null, result: false};
      }

      if (!user.wallet || !user.wallet.length || !user.wallet[0]) {
        console.log('No wallet available for verification');
        return {walletLabel: null, encryptedData: null, result: false};
      }

      const mnemonic = user.wallet[0].mnemonic;

      // Extract encrypted parts from service names
      // Format: "assetify-password_label__encryptedData"
      for (const service of keychainPasswordServices) {
        try {
          // Extract the actual encrypted data after the "__"
          const parts = service.split('__');
          if (parts.length >= 2) {
            const encryptedData = parts[1];
            const walletLabel = stripServicePrefix(parts[0]);
            // Try to directly verify this wallet's mnemonic against the encrypted part
            console.log('Checking wallet backup for service: ', service);
            const isMatching = await isWalletStoredInKeychain(encryptedData, mnemonic);
            if (isMatching) {
              console.log('Found matching wallet in keychain');
              return {walletLabel, encryptedData, result: true};
            }
          }
        } catch (err) {
          console.error('[SettingsScreen] Error processing keychain service:', err);
          showWarningToast(`Could not verify ${KEYCHAIN_BACKUP_PROVIDER} backup`);
        }
      }

      console.log('No matching wallet found in keychain backups');
      return {walletLabel: null, encryptedData: null, result: false};
    } catch (error) {
      console.error('[KeychainHome] Error checking credentials:', error);
      return {walletLabel: null, encryptedData: null, result: false};
    }
  }, [getAllKeychainPasswordServices]);

  const renderLanguageSwitch = () => (
    <View style={styles.switchView}>
      <Text style={styles.valueText} numberOfLines={1}>
        {' '}
        BG{' '}
      </Text>
      <Switch
        trackColor={{
          false: GlobalStyles.gray.gray600,
          true: GlobalStyles.success.success600,
        }}
        thumbColor={GlobalStyles.base.white}
        ios_backgroundColor={GlobalStyles.gray.gray600}
        onValueChange={() => {
          setLanguageSetting(languageSetting === 'en' ? 'bg' : 'en');
        }}
        value={languageSetting === 'en'}
      />
      <Text style={styles.valueText} numberOfLines={1}>
        {' '}
        EN{' '}
      </Text>
    </View>
  );

  const renderCurrencySwitch = () => (
    <View style={styles.switchView}>
      <Text style={styles.valueText} numberOfLines={1}>
        {' '}
        EUR{' '}
      </Text>
      <Switch
        trackColor={{
          false: GlobalStyles.gray.gray600,
          true: GlobalStyles.success.success600,
        }}
        thumbColor={GlobalStyles.base.white}
        ios_backgroundColor={GlobalStyles.gray.gray600}
        onValueChange={() => {
          setCurrencySetting(currencySetting !== 'EUR' ? 'EUR' : 'USD');
        }}
        value={currencySetting !== 'EUR'}
      />
      <Text style={styles.valueText} numberOfLines={1}>
        {' '}
        USD{' '}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <SettingsSection title={t('settings.preferences')}>
            <SettingsItem
              title={t('settings.currency')}
              rightElement={renderCurrencySwitch()}
              leftIcon={<CurrencyDollarIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
            <SettingsItem
              title={t('settings.language')}
              rightElement={renderLanguageSwitch()}
              leftIcon={<LanguageIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
          </SettingsSection>

          <SettingsSection title={t('settings.privacyAndSecurity')}>
            <SettingsItem
              title={t('settings.seedPhrase')}
              onPress={handleSeedPhrase}
              leftIcon={<DocumentTextIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />

            <SettingsItem
              title={t('settings.privateKey.title')}
              onPress={handlePrivateKey}
              leftIcon={<KeyIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />

            <SettingsItem
              title={t('keychain.keychainBackup', {
                opt: KEYCHAIN_BACKUP_PROVIDER,
              })}
              onPress={handleKeychain}
              leftIcon={<LockClosedIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
          </SettingsSection>

          <SettingsSection title={t('settings.about')} isLast>
            <SettingsItem
              title={t('settings.termsAndServices')}
              onPress={handleTermsAndServices}
              leftIcon={<DocumentTextIcon size={26} fill={GlobalStyles.gray.gray700} />}
            />
            <SettingsItem
              title={t('settings.logout')}
              onPress={handleLogoutConfirmation}
              leftIcon={
                <ArrowRightOnRectangleIcon size={26} fill={GlobalStyles.gray.gray700} />
              }
            />
            <SettingsItem
              title={t('settings.deleteAccount')}
              onPress={handleDeleteAccount}
              leftIcon={<TrashIcon size={26} fill={GlobalStyles.error.error600} />}
            />
            <SettingsItem
              title={t('settings.version')}
              rightElement={
                <Text style={styles.valueText} numberOfLines={1}>
                  {data.version}
                </Text>
              }
              leftIcon={
                <WrenchScrewdriverIcon size={26} fill={GlobalStyles.gray.gray700} />
              }
            />
          </SettingsSection>

          <SafeAreaInset type="bottom" />
        </View>
      </ScrollView>
    </View>
  );
};

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    flexDirection: 'column',
  },
  content: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  warningContainer: {
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'center',
    display: 'flex',
    width: '100%',
    marginTop: 40,
    paddingHorizontal: 10,
  },
  warningText: {
    fontSize: 18,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '400',
    lineHeight: 19,
    color: GlobalStyles.base.black,
    marginHorizontal: 2,
    marginTop: 30,
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-between',
    width: '90%',
    marginTop: 20,
    marginBottom: 20,
  },
  typeText: {
    flex: 1,
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.base.black,
  },
  valueText: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.gray.gray900,
    marginHorizontal: 2,
    alignSelf: 'center',
  },
  line: {
    width: '90%',
    height: 2,
    backgroundColor: GlobalStyles.gray.gray500,
  },
  valueView: {
    width: '43%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginVertical: -10,
    marginRight: 10,
  },
  switchView: {
    width: '43%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: -10,
    marginRight: 18,
  },
  deleteView: {
    width: '10%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginVertical: -30,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 40,
    alignSelf: 'center',
    width: '90%',
  },
});

const mapStateToProps = (state: any) => ({
  language: state.common.language,
  currency: state.common.currency,
  userAddresses: state.auth.userAddresses,
});

export default connect(mapStateToProps)(SettingsScreen);
