import Clipboard from '@react-native-clipboard/clipboard';
import analytics from '@react-native-firebase/analytics';
import {useIsFocused} from '@react-navigation/native';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {TextInput, View} from 'react-native';
import {connect} from 'react-redux';

import Banner from '@/components/Banner';
import CopyPasteButton from '@/components/CopyPasteBtn';
import MButton from '@/components/MButton';
import SafeAreaInset from '@/components/SafeAreaInset';
import {navigate} from '@/navigation/utils/navigation';
import {AuthUser} from '@/types/authTypes';
import {shuffleArray} from '@/utils';
import styles from './styles';

type SeedPhraseProps = {
  user: AuthUser;
  seedPhraseConfirmed: number;
  seedPhraseVerified: boolean;
};

const SeedPhraseScreen: React.FC = (props: any) => {
  const {user, seedPhraseConfirmed, seedPhraseVerified} = props as SeedPhraseProps;

  const {t} = useTranslation();
  const viewFocused = useIsFocused();

  const mnemonic = user.wallet[0]?.mnemonic || '';

  useEffect(() => {
    analytics().logEvent('SeedPhrase_screen_open');
  }, [viewFocused]);

  const seedWords = mnemonic.split(' ');
  const copyToClipboard = () => {
    analytics().logEvent('SeedPhrase_CopyButton_tap');
    const seedWordsText = seedWords.join(' ');
    Clipboard.setString(seedWordsText);
  };

  const onContinue = async () => {
    const shuffledSeedWords = shuffleArray(seedWords);
    navigate('VerifySeedPhrase', {
      shuffledPhrases: shuffledSeedWords,
      originalPhrases: user.wallet[0]?.mnemonic.split(' '),
      path: 'SeedPhrase',
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.bannerContainer}>
        <Banner type="warning" message={t('wallet.doNotShare', {opt: 'Seed Phrase'})} />
      </View>
      <View style={styles.containerContent}>
        <View style={styles.seedPhrase}>
          {seedWords.map((word: any, index: number) => (
            <TextInput
              key={index}
              style={styles.seedWord}
              value={`${word}`}
              editable={false}
            />
          ))}
        </View>
        <View>
          <View style={styles.copyBtn}>
            <CopyPasteButton clipboardAction={copyToClipboard} title="copy" />
          </View>
        </View>

        {!seedPhraseVerified && (
          <View style={{width: '90%', alignSelf: 'center', paddingBottom: 12}}>
            <MButton text="Confirm" onPress={onContinue} />
          </View>
        )}
      </View>

      <SafeAreaInset type="bottom" />
    </View>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.auth.user,
  seedPhraseConfirmed: state.common.seedPhraseConfirmed,
  seedPhraseVerified: state.common.seedPhraseVerified,
});

export default connect(mapStateToProps)(SeedPhraseScreen);
