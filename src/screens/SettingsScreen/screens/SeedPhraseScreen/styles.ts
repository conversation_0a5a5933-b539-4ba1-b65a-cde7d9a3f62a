import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';
import {getFontSize} from '../../../../utils/parsing';

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    paddingLeft: 20,
    paddingRight: 20,
    display: 'flex',
    flexDirection: 'column',
  },
  bannerContainer: {
    width: '100%',
    marginBottom: 10,
  },
  containerContent: {
    flex: 1,
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between', // To place items at the top and bottom
    alignItems: 'center',
    paddingTop: 20, // Adjust this value for spacing between top and bottom views
    paddingBottom: 20, // Adjust this value for spacing between top and bottom views
  },
  seedPhrase: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    width: '100%',
    display: 'flex',
    alignItems: 'flex-start',
    gap: getFontSize(16),
  },
  seedWord: {
    width: '30%', // 3 boxes per row
    paddingLeft: 8,
    paddingVertical: 8,
    textAlign: 'left',
    alignItems: 'center',
    justifyContent: 'center',
    borderStyle: 'solid',
    borderColor: GlobalStyles.primary.primary500,
    borderWidth: 1.2,
    borderRadius: 8,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 19,
    textTransform: 'capitalize',
    color: GlobalStyles.base.black,
  },
  copyBtn: {
    display: 'flex',
    alignItems: 'center',
    marginVertical: 40,
  },
  snackbar: {
    display: 'flex',
    backgroundColor: GlobalStyles.orange.orange50,
    width: '100%',
    borderColor: GlobalStyles.orange.orange500,
    borderStyle: 'solid',
    borderWidth: 1.5,
    borderRadius: 8,
    fontFamily: GlobalStyles.fonts.sfPro,
    flexDirection: 'row-reverse',
  },
});
export default styles;
