import analytics from '@react-native-firebase/analytics';
import {useIsFocused, useRoute} from '@react-navigation/native';
import {isEqual} from 'lodash';
import {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Dimensions, ScrollView, TouchableOpacity, View} from 'react-native';
import {useDispatch} from 'react-redux';

import Check from '@/assets/icons/check.svg';
import Checkmark from '@/assets/icons/checkmark.svg';
import FixedText from '@/components/FixedText/FixedText';
import LoadingHandler from '@/components/LoadingHandler';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {navigate} from '@/navigation/utils/navigation';
import {setSeedPhraseConfirmed} from '@/storage/actions/sharedActions';
import styles from './styles';

const ResponsiveCheckSvg = () => (
  <Check height={'150%'} width={'150%'} style={{alignSelf: 'center'}} />
);

const CheckmarkSvg = () => <Checkmark width={24} height={24} />;

type VerifySeedPhraseScreenParams = {
  shuffledPhrases: string[];
  originalPhrases: string[];
};

type IDictionary = {
  [key: string]: {
    x: number;
    y: number;
  };
};

const VerifySeedPhraseScreen = ({navigation}) => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const viewFocused = useIsFocused();
  const route = useRoute() as {params: VerifySeedPhraseScreenParams};

  const {shuffledPhrases, originalPhrases} = route.params;
  const [selectedBoxes, setSelectedBoxes] = useState<string[]>([]);
  const [shuffledBoxes, setShuffledBoxes] = useState<string[]>(shuffledPhrases);
  const [shuffledBoxesPosition, setShuffledBoxesPosition] = useState<IDictionary>({});

  const [disabled, setDisabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const shuffledBoxesPosition: IDictionary = {};

    const width = Dimensions.get('window').width * 0.9;
    const boxWidth = width * 0.29;

    const xPositions = [0, width - boxWidth, (width - boxWidth) / 2];

    shuffledPhrases.forEach((phrase, index) => {
      shuffledBoxesPosition[phrase] = {
        x: xPositions[index % 3],
        y: (index % 4) * 50 + 20,
      };
    });
    setShuffledBoxes(shuffledPhrases);
    setShuffledBoxesPosition(shuffledBoxesPosition);
    setSelectedBoxes([]);
    setDisabled(true);
    setLoading(false);
    setSuccess(false);
    analytics().logEvent('VerifySeedPhrase_screen_open');
  }, [viewFocused]);

  const handleShuffledBoxClick = (index: number) => {
    const clickedWord = shuffledBoxes[index];

    // Check if the clicked word is undefined or falsy
    if (!clickedWord) {
      return;
    }

    // Remove the clicked word from shuffledBoxes
    const updatedShuffledBoxes = shuffledBoxes.filter((_: any, i: number) => i !== index);
    setShuffledBoxes(updatedShuffledBoxes);

    // Add it to selectedBox
    const updatedShuffledSelectedBox = [...selectedBoxes, clickedWord];

    setSelectedBoxes(updatedShuffledSelectedBox);

    if (updatedShuffledSelectedBox.length === 12) {
      console.log('updatedShuffledSelectedBox', updatedShuffledSelectedBox);
      console.log('originalPhrases', originalPhrases);
      if (isEqual(updatedShuffledSelectedBox, originalPhrases)) {
        setDisabled(false);
      }
    }
  };

  const handleBoxClick = (index: number) => {
    setDisabled(true);
    const clickedWord = selectedBoxes[index];

    // Remove the clicked word from selectedBox
    const updatedSelectedBox = selectedBoxes.filter((_, i) => i !== index);
    setSelectedBoxes(updatedSelectedBox);

    // Add it back to shuffledBoxes
    const updatedShuffledBoxes = [...shuffledBoxes, clickedWord];

    setShuffledBoxes(updatedShuffledBoxes);
  };

  const handleContinue = async () => {
    analytics().logEvent('VerifySeedPhrase_continueButton_tap');
    setLoading(true);

    dispatch(setSeedPhraseConfirmed(0));
    setLoading(false);

    setSuccess(true);
  };

  if (loading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <LoadingHandler />
      </View>
    );
  }

  if (success) {
    return (
      <>
        <View style={GlobalStyles.THEME.mainContainer}>
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              width: '100%',
            }}
          >
            <View style={{alignItems: 'center', marginTop: -20, width: '100%'}}>
              <View
                style={{
                  width: 80,
                  height: 80,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <ResponsiveCheckSvg />
              </View>
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                  alignSelf: 'center',
                  width: '90%',
                }}
              >
                <View
                  style={{
                    width: '100%',
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'center',
                    marginTop: 120,
                    marginBottom: 20,
                  }}
                >
                  <FixedText
                    style={{
                      fontSize: 26,
                      color: GlobalStyles.base.black,
                      textAlign: 'center',
                    }}
                  >
                    {t('settings.seedPhraseConfirmed')}
                  </FixedText>
                </View>
              </View>
            </View>
          </View>
        </View>
        <View
          style={{
            position: 'absolute',
            bottom: 40,
            width: '90%',
            alignSelf: 'center',
          }}
        >
          <MButton
            text={'Continue'}
            onPress={() => {
              navigate('Settings');
            }}
            icon={<CheckmarkSvg />}
          />
        </View>
      </>
    );
  }

  return (
    <>
      <ScrollView style={{flex: 1, height: '100%'}}>
        <View style={styles.container}>
          <View style={styles.textContainer}>
            <FixedText
              style={{
                ...styles.textSecondary,
                fontFamily: GlobalStyles.fonts.sfPro,
              }}
            >
              {t('wallet.please_input_seed_phrase')}
            </FixedText>
          </View>

          <View style={styles.containerContentUser}>
            {selectedBoxes.map((word: string, index: number) => (
              <TouchableOpacity
                onPress={() => handleBoxClick(index)}
                key={index}
                style={styles.seedWord}
              >
                <View key={index}>
                  <FixedText style={styles.seedWordText}>{`${word}`}</FixedText>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.containerContent}>
            {shuffledBoxes.map((word: string, index: number) => (
              <TouchableOpacity
                onPress={() => handleShuffledBoxClick(index)}
                style={{
                  ...styles.seedWord,
                  ...{
                    position: 'absolute',
                    left: shuffledBoxesPosition[word]?.x,
                    top: shuffledBoxesPosition[word]?.y,
                  },
                }}
                key={index}
              >
                <View key={index}>
                  <FixedText
                    style={{
                      color: GlobalStyles.base.black,
                      textTransform: 'capitalize',
                      fontWeight: '500',
                    }}
                  >
                    {`${word}`}
                  </FixedText>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>
      <View
        style={{
          bottom: 40,
          backfaceVisibility: 'hidden',
          alignSelf: 'center',
          width: '90%',
        }}
      >
        <MButton text={t('continue')} onPress={handleContinue} disabled={disabled} />
      </View>
    </>
  );
};

export default VerifySeedPhraseScreen;
