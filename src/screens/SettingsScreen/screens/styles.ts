import {Dimensions, StyleSheet} from 'react-native';
import GlobalStyles from '@/constants/GlobalStyles';
import {getFontSize} from '@/utils/parsing';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
    paddingLeft: 20,
    paddingRight: 20,
    display: 'flex',
    flexDirection: 'column',
  },
  containerContent: {
    flex: 1,
    width: Dimensions.get('window').width * 0.9,
    height: 210,
    display: 'flex',
    justifyContent: 'space-between', // To place items at the top and bottom
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getFontSize(16),
  },
  containerContentUser: {
    flex: 1,
    width: Dimensions.get('window').width,
    height: 210,
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingTop: 10,
    paddingHorizontal: 20,
    paddingBottom: 30,
    gap: getFontSize(16),
    backgroundColor: GlobalStyles.gray.gray300,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderStyle: 'solid',
    borderTopColor: GlobalStyles.primary.primary300,
  },
  textContainer: {
    display: 'flex',
    paddingRight: 16,
    paddingLeft: 16,
    flexDirection: 'column',
    alignSelf: 'stretch',
    textAlign: 'center',
    marginVertical: 8,
  },
  textSecondary: {
    textAlign: 'center',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 14,
    color: GlobalStyles.gray.gray900,
    marginVertical: 8,
  },
  seedWord: {
    width: Dimensions.get('window').width * 0.9 * 0.29, // 3 boxes per row
    height: 35,
    padding: 8,
    flexWrap: 'wrap',
    textAlign: 'center',
    alignItems: 'flex-start',
    borderStyle: 'solid',
    borderWidth: 1.1,
    borderRadius: 8,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 19,
    textTransform: 'capitalize',
    color: GlobalStyles.primary.primary500,
  },
  seedWordText: {
    color: GlobalStyles.base.black,
    textTransform: 'capitalize',
    fontWeight: '500',
  },
});
export default styles;
