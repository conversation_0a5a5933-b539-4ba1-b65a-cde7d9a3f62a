import Clipboard from '@react-native-clipboard/clipboard';
import {useIsFocused, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import MButton from '@/components/MButton';
import {useAuth} from '@/hooks/redux';
import {Footer} from '@/styles/styled-components';
import theme from '@/styles/themes';
import {showInfoToast} from '@/utils/toast';
import Eye from '../../../../assets/icons/eye.svg';
import KeyWarning from '../../../../assets/icons/keyWarning.svg';
import GlobalStyles from '../../../../constants/GlobalStyles';

const KeyWarningSvg = () => <KeyWarning width={200} height={200} />;

const EyeSvg = () => <Eye width={24} height={24} />;

// Utility function to format crypto keys for better display
const formatCryptoKey = (key: string): string => {
  if (!key) return '';
  if (key.length <= 16) return key;

  const start = key.substring(0, 8);
  const end = key.substring(key.length - 8);
  return `${start}...${end}`;
};

const PrivateKeyScreen: React.FC = () => {
  const {t} = useTranslation();
  const route = useRoute() as {params: {index: number}};
  const params = route.params;
  const index = params.index || 0;
  const viewFocused = useIsFocused();

  const {userAddresses} = useAuth();

  const [warning, setWarning] = useState(true);

  // @ts-ignore
  const privateKey =
    userAddresses && userAddresses[index] && userAddresses[index].chain === 'kaspa'
      ? userAddresses[index].addresses && userAddresses[index].addresses[0]
        ? userAddresses[index].addresses[0].privateKey
        : ''
      : userAddresses && userAddresses[index] && userAddresses[index].privateKey
      ? userAddresses[index].privateKey
      : '';

  // @ts-ignore
  const publicKey =
    userAddresses && userAddresses[index] && userAddresses[index].chain === 'kaspa'
      ? userAddresses[index].addresses && userAddresses[index].addresses[0]
        ? userAddresses[index].addresses[0].publicKey
        : ''
      : userAddresses && userAddresses[index] && userAddresses[index].publicKey
      ? userAddresses[index].publicKey
      : '';

  const handlePress = (key: string) => {
    Clipboard.setString(key);
    showInfoToast(t('successCopy'));
  };

  useEffect(() => {
    if (viewFocused) {
      setWarning(true);
    }
  }, [viewFocused]);

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        {warning ? (
          <>
            <KeyWarningSvg />

            <View>
              <Text style={styles.warningText}>{t('settings.privateKey.warning')}</Text>
            </View>
          </>
        ) : (
          <View style={styles.content}>
            <View style={styles.row}>
              <Text style={styles.typeText} numberOfLines={1}>
                {t('settings.privateKey.title')}
              </Text>

              <View style={styles.valueView}>
                <TouchableOpacity
                  onPress={() => handlePress(privateKey)}
                  style={styles.keyContainer}
                >
                  <Text style={styles.valueText}>{formatCryptoKey(privateKey)}</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.line} />

            <View style={styles.row}>
              <Text style={styles.typeText} numberOfLines={1}>
                {t('settings.publicKey')}
              </Text>
              <View style={styles.valueView}>
                <TouchableOpacity
                  onPress={() => handlePress(publicKey)}
                  style={styles.keyContainer}
                >
                  <Text style={styles.valueText}>{formatCryptoKey(publicKey)}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </View>
      {warning && (
        <Footer style={{paddingHorizontal: theme.layout.ph.lg, paddingBottom: 20}}>
          <MButton
            text={t('settings.showPrivateKey')}
            onPress={() => {
              setWarning(false);
            }}
            icon={<EyeSvg />}
          />
        </Footer>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    paddingVertical: theme.layout.pv.screen,
  },
  warningContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  warningText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
    color: GlobalStyles.gray.gray900,
    lineHeight: 24,
  },
  row: {
    flexDirection: 'column',
    marginBottom: 16,
  },
  typeText: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    marginBottom: 8,
  },
  valueView: {
    width: '100%',
  },
  valueText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  line: {
    height: 1,
    backgroundColor: GlobalStyles.gray.gray600,
    marginVertical: 16,
  },
  bottomContainer: {
    padding: 16,
  },
  keyContainer: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: GlobalStyles.gray.gray600,
    borderRadius: 8,
  },
});

export default PrivateKeyScreen;
