import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    display: 'flex',
    width: '100%',
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  warningContainer: {
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    alignSelf: 'center',
    display: 'flex',
    width: '100%',
    marginTop: 40,
    paddingHorizontal: 10,
  },
  warningText: {
    fontSize: 18,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '400',
    lineHeight: 19,
    color: GlobalStyles.base.black,
    marginHorizontal: 2,
    marginTop: 30,
    marginBottom: 20,
  },
  row: {
    flexDirection: 'row',
    alignSelf: 'center',
    justifyContent: 'space-between',
    width: '90%',
    marginTop: 20,
    marginBottom: 20,
  },
  typeText: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.base.black,
  },
  valueText: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.gray.gray900,
    marginHorizontal: 2,
    alignSelf: 'center',
    width: '70%',
  },
  addressText: {
    fontSize: 14,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 18,
    marginTop: 5,
    marginBottom: -5,
    color: GlobalStyles.gray.gray900,
  },
  line: {
    width: '90%',
    height: 2,
    backgroundColor: GlobalStyles.gray.gray500,
  },
  valueView: {
    width: '20%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginVertical: -10,
    marginRight: 10,
  },
  switchView: {
    width: '43%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: -10,
  },
  deleteView: {
    width: '10%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginVertical: -30,
  },
  bottomContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    position: 'absolute',
    bottom: 20,
    width: '100%',
  },
});
