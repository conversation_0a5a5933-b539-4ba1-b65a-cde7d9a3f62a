import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';

import {useAppDispatch, useAuth} from '@/hooks/redux';
import {setIsAuthenticated} from '@/storage/actions/authActions';
import ArrowRight from '../../../../assets/icons/arrow right.svg';
import FixedText from '../../../../components/FixedText/FixedText';
import GlobalStyles from '../../../../constants/GlobalStyles';
import {navigate} from '../../../../navigation/utils/navigation';
import {NameParsing} from '../../../../utils/parsing';
import {styles} from './styles';

const ArrowRightSvg = () => <ArrowRight width={30} height={30} />;

const ChainsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const {user, userAddresses} = useAuth();

  const handlePrivateKey = async (index: number) => {
    dispatch(setIsAuthenticated(false));
    navigate('PrivateKey' as never, {index: index} as never);
  };

  return (
    <>
      <ScrollView style={GlobalStyles.THEME.mainContainer}>
        <View style={styles.container}>
          {user.wallet.map((item: any, index: number) => {
            return (
              <>
                <TouchableOpacity
                  style={styles.row}
                  onPress={() => {
                    handlePrivateKey(index);
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'column',
                      width: '75%',
                    }}
                  >
                    <FixedText style={styles.typeText} numberOfLines={1}>
                      {NameParsing(item.blockchain)}
                    </FixedText>
                    <FixedText style={styles.addressText} numberOfLines={1}>
                      {userAddresses[index].chain === 'kaspa' // @ts-ignore
                        ? userAddresses[index].addresses[0].address // @ts-ignore
                        : userAddresses[index].address}
                    </FixedText>
                  </View>
                  <View style={styles.valueView}>
                    <ArrowRightSvg />
                  </View>
                </TouchableOpacity>
                {index !== user.wallet.length - 1 && <View style={styles.line} />}
              </>
            );
          })}
        </View>
        <View style={{height: 20}}></View>
      </ScrollView>
    </>
  );
};

export default ChainsScreen;
