import React, {memo, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  View,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import MagnifyingGlass from '@/assets/icons/magnifying glass.svg';
import NavButton from '@/components/NavButton/NavButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {openLink} from '@/utils/in-app-browser';
import {NEWS_FEED_SERVICE} from '@env';
import {useQuery} from '@tanstack/react-query';
import NewsComponent from './components/News/News';
import {NewsData, fetcher} from './newsUtils';

const MagnifyingGlassSvg = () => <MagnifyingGlass width={24} height={24} />;

const NewsScreen = () => {
  const {t} = useTranslation();

  const news: NewsData[] = [];
  const newsFeedUrl = `${NEWS_FEED_SERVICE}/news-feed`;

  const [selectedNav, setSelectedNav] = useState<string>('Bitcoin');
  const [searchText, setSearchText] = useState<string>('');
  const [newsData, setNewsData] = useState<NewsData[]>([]);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const navigation: {[key: string]: string} = {
    Bitcoin: 'BTC',
    Ethereum: 'ETH',
    Solana: 'SOL',
    'Binance-Smart-Chain': 'BNB',
    Ripple: 'XRP',
    Tron: 'TRX',
    Avalanche: 'AVAX',
    Kaspa: 'KAS',
  };

  const {data: newsFeed, refetch: refetchNewsFeed} = useQuery({
    queryKey: ['newsFeed'],
    queryFn: () => fetcher(newsFeedUrl),
  });

  const updateNewsFeed = () => {
    if (newsFeed) {
      news.splice(0, news.length);
      const buffer = Buffer.from(newsFeed, 'base64').toString();
      if (buffer === '') {
        return;
      }
      const data = JSON.parse(buffer) as Record<string, NewsData[]>;
      const selectedKey = navigation[selectedNav];

      if (selectedKey && data[selectedKey]) {
        data[selectedKey].forEach((newsItem: NewsData) => {
          if (newsItem.title.toLowerCase().includes(searchText.toLowerCase())) {
            news.push(newsItem);
          }
        });
      }

      setNewsData([...news]);
    }
  };

  const handleNavPress = (nav: string) => {
    console.log('nav', nav);
    if (selectedNav === nav) {
      return;
    } else {
      setSelectedNav(nav);
    }
  };

  const handleOpenLink = async (url: string) => {
    await openLink(url);
  };

  useEffect(() => {
    updateNewsFeed();
  }, [newsFeed, selectedNav, searchText]);

  const onRefresh = () => {
    refetchNewsFeed();
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const renderHeader = () => (
    <>
      <View style={styles.searchContainer}>
        <MagnifyingGlassSvg />

        <TextInput
          style={styles.searchInput}
          value={searchText}
          onChangeText={(text) => setSearchText(text)}
          returnKeyType="done"
          autoComplete="off"
          autoCorrect={false}
          placeholder={t('news.search')}
        />
      </View>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.navContainer}
      >
        {Object.keys(navigation).map((item) => (
          <NavButton
            key={item}
            title={item}
            onPress={() => handleNavPress(item)}
            selected={selectedNav === item}
          />
        ))}
      </ScrollView>
    </>
  );

  const renderItem = ({item, index}: {item: NewsData; index: number}) => (
    <NewsComponent openLink={() => handleOpenLink(item.url)} news={item} index={index} />
  );

  const keyExtractor = (item: NewsData) => item.url;

  return (
    <SafeAreaView style={styles.container}>
      <FlatList
        data={newsData}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.newsContainer}
        showsVerticalScrollIndicator={true}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={GlobalStyles.primary.primary500}
            colors={[GlobalStyles.primary.primary500]}
          />
        }
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={5}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <TextInput style={styles.emptyText}>No news found</TextInput>
          </View>
        }
      />
    </SafeAreaView>
  );
};

export default memo(NewsScreen);

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    marginBottom: 20,
    marginTop: 16,
    paddingVertical: 6,
    width: '100%',
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    paddingLeft: 10,
  },
  searchInput: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 10,
    fontSize: 16,
    color: GlobalStyles.base.black,
    fontWeight: '500',
    marginLeft: 10,
  },
  navContainer: {
    flexDirection: 'row',
    width: '100%',
    paddingBottom: 10,
  },
  newsContainer: {
    flexGrow: 1,
    marginHorizontal: 18,
    borderRadius: 8,
    marginBottom: 30,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: GlobalStyles.gray.gray500,
  },
});
