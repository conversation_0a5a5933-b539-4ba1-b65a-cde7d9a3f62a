import {Image, ImageBackground, TouchableOpacity, View, Text} from 'react-native';
import {NewsData, timePassed} from '../../newsUtils';
import {styles} from './styles';

type NewsComponentProps = {
  news: NewsData;
  openLink: () => void;
  index: number;
};

const NewsComponent = ({news, openLink, index}: NewsComponentProps) => {
  if (index === 0) {
    return (
      <TouchableOpacity onPress={openLink} style={styles.titleNewsContainer}>
        <ImageBackground
          source={{uri: news.image}}
          style={styles.titleNewsContainer}
          imageStyle={styles.titleNewsImage}
        >
          <View style={styles.titleNewsTextContainer}>
            <Text style={{...styles.titleNewsTitle}}>{news.title}</Text>
            <Text style={styles.titleNewsTimePassed}>{timePassed(news)}</Text>
          </View>
        </ImageBackground>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity style={styles.container} onPress={openLink}>
      <View style={styles.tokenLogoContainer}>
        <Image source={{uri: news.image}} style={styles.tokenLogo} />
      </View>
      <View style={styles.textContainer}>
        <View style={styles.leftContainer}>
          <Text style={styles.title}>{news.title}</Text>
        </View>
        <View style={styles.tokenInfoContainer}>
          <Text style={styles.timePassed}>{timePassed(news)}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default NewsComponent;
