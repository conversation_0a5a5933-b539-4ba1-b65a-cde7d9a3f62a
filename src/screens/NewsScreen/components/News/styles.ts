import {Dimensions, StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 8,
    width: '100%',
  },
  textContainer: {
    display: 'flex',
    flex: 1,
  },
  leftContainer: {
    display: 'flex',
    flex: 1,
    alignItems: 'flex-start',
  },
  titleNewsContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    borderRadius: 8,
    paddingHorizontal: 8,
    height: Dimensions.get('window').width * 0.5,
    marginVertical: 10,

    width: Dimensions.get('window').width * 0.83,
  },
  titleNewsImage: {
    alignSelf: 'center',
    borderRadius: 8,
  },
  titleNewsTextContainer: {
    display: 'flex',
    flex: 1,
    justifyContent: 'flex-end',
    alignSelf: 'flex-end',
    alignItems: 'flex-start',
    paddingHorizontal: 10,
    paddingTop: 10,
    width: '80%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 8,
    bottom: 8,
  },
  titleNewsTitle: {
    fontSize: 16,
    lineHeight: 22,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    textShadowColor: 'rgba(0, 0, 0, 0.25)',
    textShadowOffset: {width: 0, height: 2},
    textShadowRadius: 3.84,
    fontStyle: 'normal',
    color: GlobalStyles.base.black,
  },
  titleNewsTimePassed: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
    marginBottom: 8,
    alignSelf: 'flex-end',
  },
  tokenLogoContainer: {
    width: 100,
    height: 70,
    borderRadius: 6,
    backgroundColor: GlobalStyles.primary.primary100,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  tokenLogo: {
    width: 100,
    height: 70,
    borderRadius: 6,
  },
  tokenInfoContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 14,
    lineHeight: 19,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
    width: '100%',
    height: 70,
    alignSelf: 'flex-start',
    paddingTop: 12,
  },
  timePassed: {
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray800,
    fontStyle: 'normal',
    marginBottom: 8,
  },
});
