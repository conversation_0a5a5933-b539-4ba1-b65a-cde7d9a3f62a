import axios from 'axios';

export type NewsData = {
  title: string;
  url: string;
  created_at: string;
  image: string;
};

export const fetcher = (url: string) => axios.get(url).then(res => res.data);

export const timePassed = (news: any) => {
  const date = new Date(news.created_at);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor(diff / (1000 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  if (days > 0) {
    return `${days} days ago`;
  } else if (hours > 0) {
    return `${hours} hours ago`;
  } else {
    return `${minutes} minutes ago`;
  }
};
