export type Asset = {
    symbol: string;
    logoUrl: string;
  };
  
  export type AssetProps = {
    assets: Asset[];
    selectedAsset: Asset | null;
    handleSelectOption: (index: any) => void;
    isAssetsLoading: boolean;
    amount: string;
    handleAmountChange: (amount: string) => void;
    isAssetExpanded: boolean;
    setIsAssetExpanded: (isExpanded: boolean) => void;
  };
  
  const parseAssetsToOptions = (assets: Asset[]) => {
    return assets.map(asset => ({
      value: asset.symbol,
      label: asset.symbol
    }));
  };
  
  export { parseAssetsToOptions };
  