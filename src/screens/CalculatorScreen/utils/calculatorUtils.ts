import {STAKING_CALCULATOR_BACKEND_SERVICE} from '@env';

export type Provider = {
  name: string;
  logoUrl: string;
  APR: number;
  _id: string;
};

export type Asset = {
  symbol: string;
  logoUrl: string;
};

export type Period = {
  name: string;
  value: number;
};

export type EarningsInput = {
  assetSymbol: string;
  assetProvider: string;
  stakeAmount: number;
  stakePeriod: number; // in months
};

export const fetcher = (url: string) => fetch(url).then(res => res.json());

export const assetsUrl = `${STAKING_CALCULATOR_BACKEND_SERVICE}/assets`;
