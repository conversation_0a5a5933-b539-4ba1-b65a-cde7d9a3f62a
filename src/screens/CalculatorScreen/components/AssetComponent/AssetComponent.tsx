import ArrowDown from '@/assets/icons/arrow down.svg';
import ArrowUp from '@/assets/icons/arrow up.svg';
import LoadingHandler from '@/components/LoadingHandler';
import Logo from '@/components/Logo/AssetLogo';
import GlobalStyles from '@/constants/GlobalStyles';
import {useTranslation} from 'react-i18next';
import {
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {Asset, AssetProps} from '../../utils/assetUtils';
import {styles} from './styles';

const ArrowUpSvg = () => <ArrowUp width={24} height={24} />;

const ArrowDownSvg = () => <ArrowDown width={24} height={24} />;

export const AssetComponent = (props: AssetProps) => {
  const {t} = useTranslation();

  const assetLogo = (asset: Asset | null) => {
    if (!asset) {
      return <Text style={styles.selectTitle}>{t('stakingCalculator.selectAsset')}</Text>;
    }

    let symbol = `${asset.symbol.toString()}Svg`.toUpperCase();

    if (symbol == 'ETHEREUMSVG') {
      symbol = 'ETHSVG';
    } else if (symbol == 'BITCOINSVG') {
      symbol = 'BTCSVG';
    }

    return <Logo name={symbol} />;
  };

  const assetComponents = (asset: Asset | null) => {
    if (!asset) {
      return <Text style={styles.selectTitle}>{t('stakingCalculator.selectAsset')}</Text>;
    }

    return (
      <View style={styles.optionContainer}>
        {assetLogo(asset)}
        <Text style={styles.optionTitle}>{asset.symbol}</Text>
      </View>
    );
  };

  if (props.isAssetsLoading) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <LoadingHandler />
      </View>
    );
  }

  return (
    <>
      <View style={styles.assetContainer}>
        <Text style={styles.title}>{t('stakingCalculator.asset')}</Text>
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            keyboardType="numeric"
            placeholder={'0.00'}
            value={props.amount}
            onChangeText={props.handleAmountChange}
            returnKeyType="done"
            placeholderTextColor={GlobalStyles.gray.gray900}
          />
          <View style={styles.assetDropdownContainer}>
            {props.isAssetExpanded && (
              <View style={styles.assetDropdownContent}>
                <ScrollView style={styles.assetDropdownScrollView}>
                  {props.assets?.map((asset, index) => (
                    <TouchableOpacity
                      key={index}
                      style={styles.assetDropdownItem}
                      onPress={() => {
                        props.handleSelectOption(index);
                        props.setIsAssetExpanded(false);
                      }}
                    >
                      {assetComponents(asset)}
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            )}
          </View>
        </View>
      </View>
    </>
  );
};
