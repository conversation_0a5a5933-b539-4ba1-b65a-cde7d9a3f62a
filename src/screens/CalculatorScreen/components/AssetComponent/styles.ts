import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  selectTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    marginVertical: 5,
  },
  optionContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '100%',
    height: 30,
    paddingVertical: 4,
    zIndex: 2,
    marginLeft: -10,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginLeft: 10,
  },
  assetContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    // position: 'absolute',
    // zIndex: 100,
    // marginVertical: 15,
    // paddingHorizontal: 10,
  },
  input: {
    width: '45%',
    height: 50,
    borderRadius: 8,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 15,
    paddingVertical: 10,
    fontSize: 18,
    color: GlobalStyles.base.black,
  },
  assetDropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 10,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 5,
    marginLeft: 20,
  },
  assetDropdownContainer: {
    position: 'relative',
    width: '55%',
    marginRight: 10,
    justifyContent: 'flex-end',
  },
  assetDropdownHeaderTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '60%',
    marginRight: 10,
  },
  assetDropdownContent: {
    // position: 'relative',
    top: '100%',
    right: 0,
    left: '13.5%',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    backgroundColor: GlobalStyles.base.white,
    width: '90%',
  },
  assetDropdownItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray900shadow,
    alignSelf: 'flex-start',
    width: '100%',
  },
  assetDropdownScrollView: {
    maxHeight: 5 * 53,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    marginBottom: 5,
  },
  inputContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // add border
    borderColor: GlobalStyles.gray.gray600,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 10,
  },
});
