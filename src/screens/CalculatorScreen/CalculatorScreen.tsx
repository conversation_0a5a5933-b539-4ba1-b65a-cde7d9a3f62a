import {memo, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import Profit from '../../assets/icons/profit.svg';
import GlobalStyles from '../../constants/GlobalStyles';
import {parsePrice} from '../../utils/parsing';
import {providerLogo} from '../../utils/provider-logo';
import {AssetComponent} from './components/AssetComponent/AssetComponent';
import {
  Asset,
  EarningsInput,
  Period,
  Provider,
  assetsUrl,
  fetcher,
} from './utils/calculatorUtils';
// import {StakingCalculatorInstance} from '../../services/BackendServices';
import SafeAreaInset from '@/components/SafeAreaInset';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {StakingCalculatorInstance} from '@/services/BackendServices';
import {useQuery} from '@tanstack/react-query';

const CalculatorScreen = () => {
  const {t} = useTranslation();

  const periods = [
    {name: `1 ${t('stakingCalculator.month')}`, value: 1},
    {name: `6 ${t('stakingCalculator.months')}`, value: 6},
    {name: `1 ${t('stakingCalculator.year')}`, value: 12},
    {name: `3 ${t('stakingCalculator.years')}`, value: 36},
    {name: `6 ${t('stakingCalculator.years')}`, value: 72},
  ];

  const [providers, setProviders] = useState<Provider[] | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<Period | null>(periods[2]);
  const [amount, setAmount] = useState<string>('');
  const [assets, setAssets] = useState<Asset[] | null>(null);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [profit, setProfit] = useState<number>(0);
  const {open: openSheet} = useBottomSheet('list');

  const {data: assetsData, isLoading: isAssetsLoading} = useQuery({
    queryKey: ['assets'],
    queryFn: () => fetcher(assetsUrl),
  });

  const handleAmountChange = (amount: string) => {
    const regex = /^[0-9]*\.?[0-9]*$/;
    if (regex.test(amount)) {
      setAmount(amount);
    }
  };

  const handleSelectOption = (index: any) => {
    setSelectedAsset(assets ? assets[index] : null);
  };

  const handleProviderSelect = (item: any) => {
    const index = providers?.findIndex((p) => p.name === item.label) ?? -1;
    if (index >= 0) {
      setSelectedProvider(providers?.[index] || null);
    }
  };

  const handlePeriodSelect = (item: BaseOption) => {
    const index = periods.findIndex((p) => p.name === item.label);
    setSelectedPeriod(periods[index]);
  };

  const providerComponents = (provider: Provider | null) => {
    if (!provider) {
      return (
        <Text style={styles.selectTitle}>{t('stakingCalculator.selectProvider')}</Text>
      );
    }

    return (
      <View style={styles.optionContainer}>
        {providerLogo(provider.name)}
        <Text style={styles.optionTitle}>{provider.name}</Text>
      </View>
    );
  };

  const periodComponents = (period: Period | null) => {
    return (
      <View style={styles.optionContainer}>
        <Text style={styles.optionTitle}>{period?.name}</Text>
      </View>
    );
  };

  useEffect(() => {
    if (assetsData) {
      setAssets(assetsData.assets);
      setSelectedAsset(assetsData.assets[0]);
    } else {
      setAssets(null);
      setSelectedAsset(null);
    }
  }, [assetsData]);

  useEffect(() => {
    if (selectedAsset) {
      StakingCalculatorInstance.get(`/assets/${selectedAsset.symbol}/`).then((res) => {
        setProviders(res.data.providers);
        setSelectedProvider(res.data.providers[0]);
      });
    }
  }, [selectedAsset]);

  useEffect(() => {
    if (selectedAsset && selectedProvider && selectedPeriod && amount != '') {
      const data: EarningsInput = {
        assetSymbol: selectedAsset.symbol,
        assetProvider: selectedProvider.name,
        stakeAmount: parseFloat(amount),
        stakePeriod: selectedPeriod.value,
      };
      StakingCalculatorInstance.post('/earnings/', data)
        .then((res) => res.data)
        .then((res) => {
          setProfit(parseFloat(res.earnings.toFixed(6)));
        });
    } else {
      setProfit(0);
    }
  }, [selectedAsset, selectedProvider, selectedPeriod, amount]);

  return (
    <SafeAreaView style={{flex: 1}}>
      <SafeAreaInset type="top" />

      <View style={styles.container}>
        <AssetComponent
          assets={assets || []}
          selectedAsset={selectedAsset || null}
          handleSelectOption={handleSelectOption}
          isAssetsLoading={isAssetsLoading}
          amount={amount}
          handleAmountChange={handleAmountChange}
        />

        <View style={styles.providerContainer}>
          <Text style={styles.title}>{t('stakingCalculator.chooseProvider')}</Text>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() =>
              openSheet(
                {
                  type: 'simple',
                  data:
                    providers?.map((provider) => ({
                      label: provider.name,
                      value: provider.name,
                      icon: providerLogo(provider.name),
                    })) || [],
                  onSelect: handleProviderSelect,
                },
                80,
                true,
              )
            }
          >
            {providerComponents(selectedProvider)}
          </TouchableOpacity>
        </View>

        <View style={styles.periodContainer}>
          <Text style={styles.title}>{t('stakingCalculator.period')}</Text>
          <TouchableOpacity
            style={styles.dropdownButton}
            onPress={() =>
              openSheet(
                {
                  type: 'simple',
                  data: periods.map((period) => ({
                    label: period.name,
                    value: period.value.toString(),
                  })),
                  onSelect: handlePeriodSelect,
                },
                50,
                false,
              )
            }
          >
            {periodComponents(selectedPeriod)}
          </TouchableOpacity>
        </View>

        <View style={styles.profitContainer}>
          <Text style={styles.title}>{t('stakingCalculator.potentialProfit')}</Text>
          <View style={styles.profitHeaderContainer}>
            <Text style={styles.profitTitle}>{parsePrice(profit.toString())}</Text>
            <Profit width={28} height={28} style={styles.icon} />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default memo(CalculatorScreen);

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    paddingHorizontal: 16,
  },
  providerContainer: {
    marginTop: 16,
    width: '100%',
  },
  periodContainer: {
    width: '100%',
    marginTop: 16,
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 30,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    marginBottom: 5,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginLeft: 10,
  },
  selectTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
  },
  profitContainer: {
    width: '100%',
    marginTop: 16,
    padding: 20,
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  profitHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    paddingHorizontal: 5,
    borderWidth: 1,
    borderColor: GlobalStyles.primary.primary500,
    borderRadius: 5,
    backgroundColor: '#fff',
    marginTop: 8,
  },
  profitTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.primary.primary500,
    marginLeft: 10,
  },
  icon: {
    marginRight: 10,
  },
  dropdownButton: {
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  contentContainer: {
    flex: 1,
    paddingBottom: 20,
  },
});
