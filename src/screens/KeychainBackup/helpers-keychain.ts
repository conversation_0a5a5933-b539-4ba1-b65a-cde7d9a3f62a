import {Platform} from 'react-native';
import * as Keychain from 'react-native-keychain';
import {verifyMnemonic} from '@/utils/uniqueness';

export const KEYCHAIN_BACKUP_PROVIDER = Platform.OS === 'ios' ? 'iCloud' : 'Google Drive';

export const getAllKeychainPasswordServices = async () => {
  const services = await Keychain.getAllGenericPasswordServices();
  return services.filter((service) => service.startsWith('assetify-password_'));
};

export const getAllKeychainRecoveryServices = async () => {
  const services = await Keychain.getAllGenericPasswordServices();
  return services.filter((service) => service.startsWith('assetify-recovery_'));
};

export const isWalletStoredInKeychain = async (
  encryptedMessage: string | undefined,
  mnemonic: string,
) => {
  if (!encryptedMessage) {
    return;
  }

  const result = verifyMnemonic(encryptedMessage, mnemonic);
  console.log('Result:', result);
  return result;
};

export const deleteKeychainWalletService = async (id: string, uniqueId: string) => {
  try {
    console.log('Deleting wallet service:', `assetify-password_${id}__${uniqueId}`);
    await Keychain.resetGenericPassword({
      service: `assetify-password_${id}__${uniqueId}`,
    });

    console.log('Deleting recovery service:', `assetify-recovery_${id}__${uniqueId}`);
    const result = await Keychain.resetGenericPassword({
      service: `assetify-recovery_${id}__${uniqueId}`,
    });
    console.log(
      'Recovery service deleted:',
      await Keychain.getAllGenericPasswordServices(),
    );
    return result;
  } catch (error) {
    console.error('[helpers-keychain] Error deleting wallet recovery service:', error);
    return false;
  }
};
