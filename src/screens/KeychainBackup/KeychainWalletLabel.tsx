import {useCallback, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, TextInput as RNTextInput, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import GlobalStyles from '@/constants/GlobalStyles';
import useKeyboard, {useFocusInput} from '@/hooks/keyboard';
import {useAppDispatch} from '@/hooks/redux';
import {setWalletIdentifier} from '@/storage/actions/sharedActions';
import MButton from '@components/MButton';
import {TextInput} from '@components/TextInput';
import {BodyM, Caption, Footer} from '@styles/styled-components';
import theme from '@/styles/themes';
import {KEYCHAIN_BACKUP_PROVIDER} from './helpers-keychain';

const KeychainWalletLabel = ({navigation, route}: {navigation: any; route: any}) => {
  const {bottomTabsPresent = false} = route?.params || {};

  const inputRef = useRef<RNTextInput>(null);
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  useFocusInput(inputRef);
  const {keyboardShown} = useKeyboard();

  const [walletLabel, setWalletLabel] = useState('');

  const handleContinue = useCallback(() => {
    if (walletLabel) {
      navigation.navigate('KeychainPassword', {walletLabel, bottomTabsPresent});
      dispatch(setWalletIdentifier(walletLabel));
    }
  }, [dispatch, navigation, walletLabel, bottomTabsPresent]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.content}>
          <Caption style={styles.caption}>
            {t('keychain.keychainBackup', {
              opt: KEYCHAIN_BACKUP_PROVIDER,
            })}
          </Caption>

          <BodyM style={styles.description}>
            {t('keychain.keychainLabelDescription')}
          </BodyM>

          <TextInput
            ref={inputRef}
            label="Wallet Label"
            placeholder="e.g. Main Wallet"
            value={walletLabel}
            onChangeText={setWalletLabel}
          />
        </View>
      </KeyboardAwareScrollView>

      <Footer
        style={[
          styles.footer,
          !bottomTabsPresent && styles.footerWithoutBottomTabs,
          keyboardShown && styles.footerWithoutBottomTabsWithKeyboard,
        ]}
      >
        <MButton text="Continue" onPress={handleContinue} disabled={!walletLabel} />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default KeychainWalletLabel;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen + 12,
    paddingVertical: theme.layout.pv.screen,
    justifyContent: 'flex-start',
    gap: theme.spacing.xl,
  },
  caption: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.sm,
    color: GlobalStyles.gray.gray900,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.light.border,
  },
  footer: {
    paddingHorizontal: theme.layout.ph.screen * 2,
  },
  footerWithoutBottomTabs: {
    paddingBottom: theme.spacing.xxl,
  },
  footerWithoutBottomTabsWithKeyboard: {
    paddingBottom: -theme.spacing.xxl,
  },
});
