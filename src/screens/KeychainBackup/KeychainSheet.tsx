import {useNavigation} from '@react-navigation/native';
import React, {memo, ReactElement, useCallback, useEffect} from 'react';
import {StyleSheet, View} from 'react-native';
import {CloudIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {useSnapPointsTwo} from '@/hooks/bottomSheet';
import BottomSheetScreen from '@components/BottomSheetModalLayout';
import BottomSheetWrapper from '@components/BottomSheetWrapper';
import {KEYCHAIN_BACKUP_PROVIDER} from './helpers-keychain';

type Props = {
  isOpen: boolean;
};

const KeychainSheet = ({isOpen}: Props): ReactElement => {
  const navigation = useNavigation();
  const snapPoints = useSnapPointsTwo('md');

  const handleContinue = useCallback(() => {
    navigation.navigate('KeychainCreatePassword' as never);
  }, [navigation]);

  const handleCancel = useCallback(() => {
    navigation.goBack();
  }, [navigation]);

  useEffect(() => {
    if (isOpen) {
      navigation.setOptions({
        headerStyle: {
          backgroundColor: GlobalStyles.base.white,
          elevation: 0,
          shadowOpacity: 0,
        },
        headerTransparent: false,
      });
    }

    return () => {
      navigation.setOptions({
        headerStyle: undefined,
        headerTransparent: undefined,
      });
    };
  }, [isOpen, navigation]);

  const CloudImage = (
    <View style={styles.iconContainer}>
      <CloudIcon size={48} color={GlobalStyles.primary.primary500} />
    </View>
  );

  return (
    <BottomSheetWrapper isOpen={isOpen} snapPoints={snapPoints}>
      <BottomSheetScreen
        title={`Back up seed phrase to ${KEYCHAIN_BACKUP_PROVIDER}?`}
        description={`Setting a password will encrypt your recovery phrase backup, adding an extra level of protection if your ${KEYCHAIN_BACKUP_PROVIDER} account is ever compromised.`}
        image={CloudImage}
        continueText="Continue"
        cancelText="Cancel"
        onContinue={handleContinue}
        onCancel={handleCancel}
      />
    </BottomSheetWrapper>
  );
};

export default memo(KeychainSheet);

const styles = StyleSheet.create({
  iconContainer: {
    backgroundColor: GlobalStyles.primary.primary100,
    borderRadius: 16,
    padding: 16,
    alignSelf: 'center',
    marginBottom: 16,
  },
});
