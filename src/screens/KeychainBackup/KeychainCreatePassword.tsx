import React, {memo, useCallback, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, TextInput as RNTextInput, StyleSheet, View} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import GlobalStyles from '@/constants/GlobalStyles';
import useKeyboard, {useFocusInput} from '@/hooks/keyboard';
import MButton from '@components/MButton';
import {PasswordInput} from '@components/PasswordInput';
import {BodyM, Caption, Footer} from '@styles/styled-components';
import theme from '@/styles/themes';
import {KEYCHAIN_BACKUP_PROVIDER} from './helpers-keychain';

const KeychainCreatePassword = ({navigation, route}: any) => {
  const {walletLabel, fromExistingBackup, bottomTabsPresent} = route.params;

  const {t} = useTranslation();
  const inputRef = useRef<RNTextInput>(null);

  useFocusInput(inputRef);
  const {keyboardShown} = useKeyboard();

  const [password, setPassword] = useState('');

  const handlePasswordChange = useCallback((text: string) => {
    setPassword(text);
  }, []);

  const handleContinue = useCallback(() => {
    navigation.navigate('KeychainConfirmPassword', {
      password,
      walletLabel,
      fromExistingBackup: fromExistingBackup || false,
      bottomTabsPresent,
    });
  }, [navigation, password, walletLabel, fromExistingBackup, bottomTabsPresent]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        keyboardShouldPersistTaps="handled"
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.content}>
          <Caption style={styles.caption}>
            {fromExistingBackup
              ? t('keychain.secureYourBackup', {
                  opt: KEYCHAIN_BACKUP_PROVIDER,
                })
              : t('keychain.keychainBackup', {
                  opt: KEYCHAIN_BACKUP_PROVIDER,
                })}
          </Caption>

          <BodyM style={styles.description}>
            {fromExistingBackup
              ? t('keychain.addPasswordDescription', {
                  opt: KEYCHAIN_BACKUP_PROVIDER,
                })
              : t('keychain.setPasswordDescription', {
                  opt: KEYCHAIN_BACKUP_PROVIDER,
                })}
          </BodyM>

          <PasswordInput
            ref={inputRef}
            label="Set password"
            value={password}
            onChangeText={handlePasswordChange}
            placeholder="Set a strong password"
          />
        </View>
      </KeyboardAwareScrollView>

      <Footer
        style={[
          styles.footer,
          !bottomTabsPresent && styles.footerWithoutBottomTabs,
          keyboardShown && styles.footerWithoutBottomTabsWithKeyboard,
        ]}
      >
        <MButton text="Continue" onPress={handleContinue} disabled={!password} />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default memo(KeychainCreatePassword);

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen + 12,
    paddingVertical: theme.layout.pv.screen,
    justifyContent: 'flex-start',
    gap: theme.spacing.xl,
  },
  caption: {
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.sm,
    color: GlobalStyles.gray.gray900,
  },
  footer: {
    paddingHorizontal: theme.layout.ph.screen * 2,
  },
  footerWithoutBottomTabs: {
    paddingBottom: theme.spacing.xxl,
  },
  footerWithoutBottomTabsWithKeyboard: {
    paddingBottom: -theme.spacing.xxl,
  },
});
