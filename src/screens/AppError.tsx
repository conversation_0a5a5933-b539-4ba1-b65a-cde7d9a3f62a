import {captureException, withScope} from '@sentry/react-native';
import React, {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {useAppDispatch} from '@/hooks/redux';
import {resetAllSheets} from '@/storage/slices/ui';
import AssetifyLogoSvg from '@assets/logo/Logo.svg';
import MButton from '@components/MButton';
import {BodyM, Caption} from '@styles/styled-components';
import theme from '@/styles/themes';

type Props = {
  error: Error;
  onReset?: () => void;
};

export const AppError: React.FC<Props> = ({error, onReset}) => {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const handleReset = () => {
    dispatch(resetAllSheets());
    if (onReset) {
      onReset();
    }
  };

  useEffect(() => {
    withScope((scope) => {
      scope.setLevel('fatal');
      captureException(error);
    });
  }, []);

  return (
    <SafeAreaView style={styles.root}>
      <View style={styles.content}>
        <AssetifyLogoSvg
          width={theme.layout.images.sm}
          height={theme.layout.images.sm}
          style={{}}
        />

        <Caption style={styles.caption}>{t('errorHandling.errorOccurred')}</Caption>

        <BodyM style={styles.description}>{t('errorHandling.weAreSorry')}</BodyM>

        <MButton text={'Try again'} onPress={handleReset} />
      </View>
    </SafeAreaView>
  );
};

export const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: theme.colors.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.layout.pv.screen,
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.xxl,
    marginBottom: theme.spacing.xxxl,
  },
  logo: {
    paddingBottom: theme.spacing.lg,
  },
  caption: {
    textAlign: 'center',
    fontSize: theme.typography.md,
  },
  description: {
    textAlign: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    fontSize: theme.typography.sm,
  },
});
