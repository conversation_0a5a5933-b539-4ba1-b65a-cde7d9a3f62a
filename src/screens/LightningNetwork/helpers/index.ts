import {PriceFetchingInstance} from '@/services/BackendServices';
import {formatNumber} from '@/utils';
import {CONSTANTS} from './lightningConstants';

interface PrefixText {
  title: string;
  truncatedUrl: string;
}

export const msatToSat = (amount: number): number => amount / CONSTANTS.MSAT_FACTOR;
export const satToMsat = (amount: number): number => amount * CONSTANTS.MSAT_FACTOR;
export const satToBTC = (amount: number): number => amount / CONSTANTS.BTC_FACTOR;

export const truncateUrl = (url: string = ''): string =>
  `${url.slice(0, CONSTANTS.URL_PREFIX_LENGTH)}...${url.slice(
    -CONSTANTS.URL_SUFFIX_LENGTH,
  )}`;

export const stringToNumberArray = (str: string): number[] => {
  if (!str) return [];

  const numberArray: number[] = [];
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    numberArray.push(charCode & 0xff); // Get the byte value (0-255)
  }
  return numberArray;
};

export const formatLnBalance = (balance: string): string => {
  const parsedBalance = parseFloat(balance);
  if (isNaN(parsedBalance)) return '0';
  return formatNumber(parsedBalance / CONSTANTS.MSAT_FACTOR, {
    decimals: 0,
  });
};

export const fetchBtcUsdPrice = async (amount: number): Promise<string> => {
  if (amount < 0) return '0.00';

  const balanceInBtc = amount / CONSTANTS.BTC_FACTOR;

  try {
    const response = await PriceFetchingInstance.get('/price/BTC?currency=USDT');
    const btcPrice = response.data.price.price;

    if (!btcPrice || typeof btcPrice !== 'number') {
      throw new Error('Invalid BTC price received');
    }

    const calculatedBalance = balanceInBtc * btcPrice;
    return formatNumber(calculatedBalance, {
      decimals: 2,
    });
  } catch (error) {
    console.error('[PriceFetching] Error fetching BTC price:', error);
    return '0.00';
  }
};

export const getTextBasedOnPrefix = (url: string): PrefixText | undefined => {
  if (!url) return undefined;

  if (url.startsWith('LNURL')) {
    return {
      title: 'LNURL',
      truncatedUrl: truncateUrl(url),
    };
  }

  if (url.startsWith('lnbc')) {
    return {
      title: 'Invoice',
      truncatedUrl: truncateUrl(url),
    };
  }

  if (url.startsWith('bc')) {
    return {
      title: 'BTC:',
      truncatedUrl: truncateUrl(url),
    };
  }

  return undefined;
};

// Date format: <Month> <Day>, <Year>
export const timestampToDateString = (timestamp: number) => {
  const date = new Date(timestamp);
  const month = date.toLocaleString('default', {month: 'short'});
  const day = date.getDate();
  const year = date.getFullYear();

  return `${month} ${day}`;
};
