import * as sdk from '@breeztech/react-native-breez-sdk';
import RNFS from 'react-native-fs';

import {setWebSocket} from '@/storage/actions/lightningActions';
import {store} from '@/storage/store';
import {DEVICE_TOKEN, SEND_LN_NOTIF} from '@env';
import {getBreezSDKStatus, setBreezSDKStatus} from '../hooks/useBreezSDK';
import {msatToSat} from './index';

interface DisconnectSDKParams {
  bitcoinAddress: string;
}

interface RefundParams {
  swapAddress: string;
  toAddress: string;
  txId: string;
}

export const getNodeWorkingDir = async (nodeId: string): Promise<string> => {
  const dirToUse = `${RNFS.DocumentDirectoryPath}/breezSdk/nodes/${nodeId}`;

  try {
    const dirExists = await RNFS.exists(dirToUse);
    if (!dirExists) {
      await RNFS.mkdir(dirToUse);
      console.log('[Breez SDK] Created directory:', dirToUse);
    }
    return dirToUse;
  } catch (error) {
    console.error('[Breez SDK] Error managing working directory:', error);
    throw error;
  }
};

export const disconnectBreezSDK = async ({
  bitcoinAddress,
}: DisconnectSDKParams): Promise<void> => {
  const isInitialized = getBreezSDKStatus();

  if (!isInitialized) {
    console.log('[Breez SDK] SDK already disconnected');
    return;
  }

  console.log('[Breez SDK] Disconnecting SDK', isInitialized);

  const webSocket = store.getState().lightning.webSocket;
  console.log('[Breez SDK] WebSocket:', webSocket);

  try {
    webSocket?.close();

    store.dispatch(setWebSocket(null));

    await sdk.unregisterWebhook(
      `${SEND_LN_NOTIF}${bitcoinAddress}?token=${DEVICE_TOKEN}`,
    );
    console.log('[Breez SDK] Webhook unregistered');
    await sdk.disconnect();
    setBreezSDKStatus(false);
    console.log('[Breez SDK] Successfully disconnected');
  } catch (error) {
    console.error('[Breez SDK] Error during disconnect:', error);
    throw error;
  }
};

/* ============================================================================================== */
/*                                               API                                              */
/* ============================================================================================== */

export const generateInvoice = async (
  amount: number,
  note?: string,
  setOpeningFeeInSat?: (fee: number) => void,
): Promise<string | undefined> => {
  const amountMsat = amount || 100_000;
  const description = note || `Invoice for ${msatToSat(amountMsat)} sats.`;

  try {
    const {feeParams} = await sdk.openChannelFee({amountMsat});

    const paymentReq: sdk.ReceivePaymentResponse = await sdk.receivePayment({
      amountMsat,
      description,
      openingFeeParams: feeParams,
    });

    if (setOpeningFeeInSat && paymentReq.openingFeeMsat) {
      setOpeningFeeInSat(msatToSat(paymentReq.openingFeeMsat));
    }

    return paymentReq.lnInvoice.bolt11;
  } catch (error) {
    console.error('[Breez SDK] Error generating invoice:', error);
    throw error;
  }
};

export const fetchRefundables = async (): Promise<sdk.SwapInfo[]> => {
  try {
    const refundables = await sdk.listRefundables();
    return refundables;
  } catch (error) {
    console.error('[Breez SDK] Error getting refundables:', error);
    throw error;
  }
};

export const fetchOnchainReceiveData = async () => {
  try {
    return await sdk.receiveOnchain({});
  } catch (error) {
    console.error('[Breez SDK] Error getting onchain receive data:', error);
    throw error;
  }
};

export const fetchOpeningChannelFee = async (
  amountMsat: number,
): Promise<sdk.OpenChannelFeeResponse> => {
  try {
    return await sdk.openChannelFee({amountMsat});
  } catch (error) {
    console.error('[Breez SDK] Error getting opening fee:', error);
    throw error;
  }
};

export const getReverseSwapFees = async () => {
  try {
    return await sdk.fetchReverseSwapFees({});
  } catch (err) {
    console.error('[Breez SDK] Error getting reverse swap fees:', err);
    throw err;
  }
};

export const processRefund = async ({
  swapAddress,
  toAddress,
  txId,
}: RefundParams): Promise<sdk.RefundResponse | undefined> => {
  try {
    const {fastestFee} = await sdk.recommendedFees();

    const prepareRefundResp = await sdk.prepareRefund({
      swapAddress,
      toAddress,
      satPerVbyte: fastestFee,
    });

    if (!prepareRefundResp) {
      throw new Error('Failed to prepare refund');
    }

    const refundRequest: sdk.RefundRequest = {
      swapAddress,
      toAddress: txId,
      satPerVbyte: fastestFee,
    };

    return await sdk.refund(refundRequest);
  } catch (error) {
    console.error('[Breez SDK] Error during refund:', error);
    throw error;
  }
};

export const processRedeem = async (toAddress: string) => {
  try {
    const {fastestFee} = await sdk.recommendedFees();

    const prepareRedeemOnchainFundsResp = await sdk.prepareRedeemOnchainFunds({
      toAddress,
      satPerVbyte: fastestFee,
    });
    if (prepareRedeemOnchainFundsResp) {
      const redeemOnchainFundsResp = await sdk.redeemOnchainFunds({
        toAddress,
        satPerVbyte: fastestFee,
      });
    }
  } catch (err) {
    console.error(err);
  }
};

export const addLogListener = async (): Promise<void> => {
  try {
    const logFilePath = `${RNFS.DocumentDirectoryPath}/breez-sdk-log.txt`;
    let writing = false;

    const listener: sdk.LogStream = async (log) => {
      if (writing) return;

      writing = true;

      try {
        await RNFS.appendFile(
          logFilePath,
          `${new Date().toISOString()} - ${JSON.stringify(log, null, 2)}\n`,
        );
        console.log('Log saved to file:', logFilePath);
      } catch (err) {
        console.error('Failed to write log to file:', err);
      } finally {
        writing = false;
      }
    };

    await sdk.setLogStream(listener);
  } catch (error) {
    console.warn('Failed to set log listener:', error);
  }
};
