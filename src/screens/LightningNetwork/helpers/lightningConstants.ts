import {QR_CODE_BACKEND_SERVICE} from '@env';
import {UseLightningSwapsState} from '../_types';
import {UseLightningReceiveState} from '../hooks/useLightningReceive';
import {UseLightningSendState} from '../hooks/useLightningSend';

export const BREEZ_SDK_QUERY_KEY = ['breezSDK'] as const;

export const CONSTANTS = {
  QR_CODE_URL: `${QR_CODE_BACKEND_SERVICE}/qr`,
  MSAT_FACTOR: 1_000,
  BTC_FACTOR: 100_000_000,
  URL_PREFIX_LENGTH: 10,
  URL_SUFFIX_LENGTH: 5,
} as const;

export const DEFAULT_SEND_STATE: UseLightningSendState = {
  recipientAddress: '',
  input: undefined,
  amountInSat: '',
  minAmount: 0,
  qrVisible: false,
  success: false,
  disabled: false,
  validInput: false,
  errorMessage: '',
  invoice: undefined,
  amountInUSD: '',
};

export const DEFAULT_RECEIVE_STATE: UseLightningReceiveState = {
  url: '',
  amountInSat: '',
  note: '',
  openingFeeInSat: 0,
  isSwitched: false,
  amountInUsd: '0.00',
  isLoading: false,
  feeMessage: '',
};

export const DEFAULT_SWAPS_STATE: UseLightningSwapsState = {
  swapInInfo: null,
  swapOutInfo: undefined,
  isCopyModalVisible: false,
  isWarningModalVisible: false,
};
