import {
  BreezEvent,
  NodeState,
  ReverseSwapPairInfo,
  SwapInfo,
} from '@breeztech/react-native-breez-sdk';

export type BreezSDKState = {
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isSuccess: boolean;
  nodeState: NodeState | null;
  events: BreezEvent[];
};

export interface WithdrawScreenProps {
  fetchedData: ReverseSwapPairInfo;
}

export interface UseLightningSwapsState {
  swapInInfo: SwapInfo | null;
  swapOutInfo: ReverseSwapPairInfo | undefined;
  isCopyModalVisible: boolean;
  isWarningModalVisible: boolean;
}

export interface UseLightningSwapsActions {
  fetchSwapInfo: () => Promise<void>;
}

export interface UseLightningSwapsReturn {
  state: UseLightningSwapsState;
  actions: UseLightningSwapsActions;
  isQrCodeLoading: boolean;
  qrCode: any;
}
