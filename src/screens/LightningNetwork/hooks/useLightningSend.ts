import {
  InputTypeVariant,
  parseInput,
  payLnurl,
  sendPayment,
} from '@breeztech/react-native-breez-sdk';
import {useIsFocused} from '@react-navigation/native';
import {useCallback, useEffect, useMemo, useState} from 'react';

import {fetchBtcUsdPrice, satToMsat} from '../helpers';
import {DEFAULT_SEND_STATE} from '../helpers/lightningConstants';
import {LightningSendProps} from '../LightningSend';
const lightningPayReq = require('bolt11');

export interface UseLightningSendState {
  recipientAddress: string;
  input?: LightningSendProps['input'];
  amountInSat: string;
  minAmount: number;
  qrVisible: boolean;
  success: boolean;
  disabled: boolean;
  validInput: boolean;
  errorMessage: string;
  invoice?: any;
  amountInUSD: string;
}

export interface UseLightningSendActions {
  handleAmountChange: (value: string) => Promise<void>;
  handleNextPress: () => Promise<boolean>;
  handleQRScan: (value: string) => void;
  handleAddressChange: (value: string) => void;
  resetState: () => void;
}

export interface UseLightningSendReturn {
  state: UseLightningSendState;
  actions: UseLightningSendActions;
  isNextDisabled: boolean;
}

export const useLightningSend = (params: LightningSendProps): UseLightningSendReturn => {
  const isFocused = useIsFocused();

  const [state, setState] = useState<UseLightningSendState>({
    ...DEFAULT_SEND_STATE,
    recipientAddress: params.lnurl || params.bolt11 || '',
    input: params.input,
    minAmount: params.input ? params.input.data.minSendable / 1000 : 0,
  });

  const updateState = useCallback((updates: Partial<UseLightningSendState>) => {
    setState((current) => ({...current, ...updates}));
  }, []);

  const handleAddressChange = useCallback(
    async (address: string) => {
      updateState({recipientAddress: address});

      if (!address) return;

      try {
        const newInput = await parseInput(address);
        if (newInput.type === InputTypeVariant.LN_URL_PAY) {
          updateState({
            input: newInput,
            minAmount: newInput.data.minSendable,
          });
        }
      } catch (error) {
        console.error('[LightningSend] Error parsing input:', error);
        updateState({errorMessage: 'Invalid address'});
      }
    },
    [updateState],
  );

  const handleAmountChange = useCallback(
    async (value: string) => {
      const numValue = Number(value);
      const updates: Partial<UseLightningSendState> = {
        amountInSat: value,
        validInput: false,
        errorMessage: '',
      };

      if (numValue > 0) {
        updates.validInput = true;
      }

      if (numValue < state.minAmount) {
        updates.validInput = false;
      }

      updateState(updates);
    },
    [state.minAmount, updateState],
  );

  const handleNextPress = useCallback(async () => {
    updateState({disabled: true, errorMessage: ''});

    try {
      if (state.input) {
        await payLnurl({
          data: state.input.data,
          amountMsat: satToMsat(+state.amountInSat),
          useTrampoline: true,
        });
      } else if (params.bolt11) {
        await sendPayment({
          bolt11: params.bolt11,
          useTrampoline: true,
        });
      }
      updateState({success: true, disabled: false});
      return true;
    } catch (error) {
      console.error('[LightningSend] Error sending payment:', error);
      updateState({
        errorMessage: 'Error sending payment',
        disabled: false,
      });
      return false;
    }
  }, [state.input, state.amountInSat, params.bolt11, updateState]);

  const handleQRScan = useCallback(
    (value: string) => {
      updateState({
        recipientAddress: value,
        qrVisible: false,
      });
    },
    [updateState],
  );

  const resetState = useCallback(() => {
    setState((current) => ({
      ...DEFAULT_SEND_STATE,
      recipientAddress: params.lnurl || '',
      input: params.input,
      minAmount: params.input ? params.input.data.minSendable / 1000 : 0,
    }));
  }, [params]);

  useEffect(() => {
    if (params.bolt11) {
      try {
        const decodedInvoice = lightningPayReq.decode(params.bolt11);
        updateState({
          invoice: decodedInvoice,
          amountInSat: decodedInvoice.satoshis.toString(),
        });
        fetchBtcUsdPrice(decodedInvoice.satoshis).then((usdAmount) => {
          updateState({amountInUSD: usdAmount});
        });
      } catch (error) {
        console.error('[LightningSend] Error decoding invoice:', error);
        updateState({errorMessage: 'Invalid invoice'});
      }
    }
  }, [params.bolt11, updateState]);

  useEffect(() => {
    if (!isFocused) {
      resetState();
    }
  }, [isFocused, resetState]);

  const isNextDisabled = useMemo(() => {
    return (
      !state.validInput ||
      Number(state.amountInSat) <= 0 ||
      state.disabled ||
      state.errorMessage !== ''
    );
  }, [state.validInput, state.amountInSat, state.disabled, state.errorMessage]);

  return {
    state,
    actions: {
      handleAmountChange,
      handleNextPress,
      handleQRScan,
      handleAddressChange,
      resetState,
    },
    isNextDisabled,
  };
};
