import {
  BreezEvent,
  BreezEventVariant,
  connect,
  ConnectRequest,
  defaultConfig,
  EnvironmentType,
  InvoicePaidDetails,
  mnemonicToSeed,
  NodeConfig,
  NodeConfigVariant,
  nodeInfo,
  NodeState,
  PaymentFailedData,
  registerWebhook,
  SwapInfo,
} from '@breeztech/react-native-breez-sdk';
import {useQuery, useQueryClient} from '@tanstack/react-query';

import {useAppDispatch} from '@/hooks/redux';
import {setBalanceMsat} from '@/storage/actions/lightningActions';
import {AppDispatch} from '@/storage/store';
import {showSuccessToast, showWarningToast} from '@/utils/toast';
import {
  BREEZ_API_KEY,
  DEVICE_TOKEN,
  GREENLIGHT_CERTIFICATE,
  GREENLIGHT_KEY,
  SEND_LN_NOTIF,
} from '@env';
import {getNodeWorkingDir} from '../helpers/breezApi';
import {msatToSat, stringToNumberArray} from '../helpers/index';
import {BREEZ_SDK_QUERY_KEY} from '../helpers/lightningConstants';

let breezSDKInitialized = false;
let breezSDKInitializing = false;

export const getBreezSDKStatus = () => breezSDKInitialized;
export const setBreezSDKStatus = (status: boolean) => {
  breezSDKInitialized = status;
  if (!status) {
    breezSDKInitializing = false;
  }
};

export const useBreezSDK = ({mnemonic, nodeId}: {mnemonic: string; nodeId: string}) => {
  const queryClient = useQueryClient();
  const dispatch = useAppDispatch();

  const {data, isLoading, isError, error} = useQuery({
    queryKey: BREEZ_SDK_QUERY_KEY,
    queryFn: () => initializeBreezSDK(mnemonic, nodeId, dispatch),
    enabled: !breezSDKInitialized && !breezSDKInitializing,
    retry: 3,
  });

  const invalidateBreezSDK = () => {
    breezSDKInitialized = false;
    breezSDKInitializing = false;
    return queryClient.invalidateQueries({queryKey: BREEZ_SDK_QUERY_KEY});
  };

  return {
    data,
    isLoading,
    isError,
    error,
    invalidateBreezSDK,
  };
};

const handleBreezEvent = async (event: BreezEvent, dispatch: AppDispatch) => {
  console.log('[Breez SDK] Event:', event.type);

  switch (event.type) {
    case BreezEventVariant.INVOICE_PAID: {
      const channelInfo = await nodeInfo();
      const eventDetails = event.details as InvoicePaidDetails;

      dispatch(setBalanceMsat(channelInfo.channelsBalanceMsat));
      showSuccessToast(
        `${msatToSat(eventDetails?.payment?.amountMsat ?? 0)} sats received!`,
      );
      break;
    }

    case BreezEventVariant.PAYMENT_SUCCEED: {
      console.log('[Breez SDK] Payment Succeed:', event);
      break;
    }

    case BreezEventVariant.PAYMENT_FAILED: {
      const payment = event.details as PaymentFailedData;
      showWarningToast(`${payment.error}`);
      break;
    }

    case BreezEventVariant.SWAP_UPDATED: {
      const swapInfo = event.details as SwapInfo;
      break;
    }

    case BreezEventVariant.SYNCED: {
      const channelInfo = await nodeInfo();
      dispatch(setBalanceMsat(channelInfo.channelsBalanceMsat));
      break;
    }

    default:
      console.log('[Breez SDK] Unhandled event:', event);
  }
};

const initializeBreezSDK = async (
  mnemonic: string,
  nodeId: string,
  dispatch: AppDispatch,
): Promise<NodeState> => {
  if (breezSDKInitialized || breezSDKInitializing) {
    return await nodeInfo();
  }

  breezSDKInitializing = true;

  const seed = await mnemonicToSeed(mnemonic);

  const workingDir = await getNodeWorkingDir(nodeId);

  try {
    const nodeConfig: NodeConfig = {
      type: NodeConfigVariant.GREENLIGHT,
      config: {
        partnerCredentials: {
          developerKey: stringToNumberArray(GREENLIGHT_KEY),
          developerCert: stringToNumberArray(GREENLIGHT_CERTIFICATE),
        },
      },
    };

    const config = await defaultConfig(
      EnvironmentType.PRODUCTION,
      BREEZ_API_KEY,
      nodeConfig,
    );

    config.workingDir = workingDir;
    config.mempoolspaceUrl = 'https://mempool.emzy.de/api';

    const connectRequest: ConnectRequest = {
      config,
      seed,
    };

    await connect(connectRequest, (event) => handleBreezEvent(event, dispatch));

    // Register notification webhook
    await registerWebhook(`${SEND_LN_NOTIF}${nodeId}?token=${DEVICE_TOKEN}`);

    await nodeInfo();
    setBreezSDKStatus(true);
    console.log('[useBreezSDK] Breez SDK initialized');
    const channelInfo = await nodeInfo();
    return channelInfo;
  } catch (error) {
    breezSDKInitializing = false;
    console.error('[Lightning] Error initializing Breez SDK:', error);
    throw error instanceof Error ? error : new Error('Failed to initialize Breez SDK');
  }
};
