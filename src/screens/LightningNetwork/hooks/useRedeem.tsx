import {
  SwapInfo,
  inProgressSwap,
  nodeInfo,
  receiveOnchain,
} from '@breeztech/react-native-breez-sdk';
import {useEffect, useState} from 'react';

import {processRedeem} from '../helpers/breezApi';
import {getBreezSDKStatus} from './useBreezSDK';

export const useRedeem = () => {
  const [pendingSwap, setPendingSwap] = useState<SwapInfo | null>(null);

  const checkClosedChannel = async () => {
    try {
      const nodeState = await nodeInfo();
      const receivedSwapInfo = await receiveOnchain({});

      if (nodeState.onchainBalanceMsat > receivedSwapInfo.minAllowedDeposit * 1000) {
        console.log(
          '[useRedeem] Found a closed channel with funds to redeem, processing...',
        );
        await processRedeem(receivedSwapInfo.bitcoinAddress);
        setTimeout(checkInProgressSwap, 500);
      }
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to check closed channel');
      console.error('[useRedeem] checkClosedChannel error:', error);
    }
  };

  const checkInProgressSwap = async () => {
    try {
      const swap = await inProgressSwap();
      setPendingSwap(swap);
    } catch (err) {
      const error =
        err instanceof Error ? err : new Error('Failed to check swap progress');
      console.error('[useRedeem] checkInProgressSwap error:', error);
    }
  };

  useEffect(() => {
    const initializeRedeem = async () => {
      if (getBreezSDKStatus()) {
        await checkClosedChannel();
      }
    };

    initializeRedeem();
  }, [getBreezSDKStatus]);

  return {
    pendingSwap,
    checkInProgressSwap,
  };
};
