import {useFocusEffect} from '@react-navigation/native';
import {useCallback, useState} from 'react';

import {
  fetchOnchainReceiveData,
  getReverseSwapFees,
} from '@/screens/LightningNetwork/helpers/breezApi';
import {UseLightningSwapsReturn, UseLightningSwapsState} from '../_types';
import {CONSTANTS, DEFAULT_SWAPS_STATE} from '../helpers/lightningConstants';
import {useQuery} from '@tanstack/react-query';

export const useLightningSwaps = (): UseLightningSwapsReturn => {
  const [state, setState] = useState<UseLightningSwapsState>(DEFAULT_SWAPS_STATE);
  const [swapInfo, setSwapInfo] = useState<any>(null);

  const fetchQrCode = async () => {
    if (!swapInfo?.bitcoinAddress) return null;

    const response = await fetch(CONSTANTS.QR_CODE_URL, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: `${swapInfo.bitcoinAddress}`,
      }),
    });

    return response.json();
  };

  const {data: qrCode, isLoading: isQrCodeLoading} = useQuery({
    queryKey: ['qrCode', swapInfo?.bitcoinAddress],
    queryFn: fetchQrCode,
    enabled: !!swapInfo?.bitcoinAddress,
  });

  const updateState = useCallback((updates: Partial<UseLightningSwapsState>) => {
    setState((current) => ({...current, ...updates}));
  }, []);

  const fetchSwapInfo = useCallback(async () => {
    try {
      const swapInData = await fetchOnchainReceiveData();
      setSwapInfo(swapInData);
      const swapOut = await getReverseSwapFees();
      updateState({
        swapInInfo: swapInData,
        swapOutInfo: swapOut,
      });
    } catch (error) {
      console.error('[Lightning] Error fetching swap info:', error);
    }
  }, [updateState]);

  useFocusEffect(
    useCallback(() => {
      fetchSwapInfo();
    }, [fetchSwapInfo]),
  );

  return {
    state,
    actions: {
      fetchSwapInfo,
    },
    isQrCodeLoading,
    qrCode,
  };
};
