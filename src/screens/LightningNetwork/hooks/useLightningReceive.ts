import {nodeInfo, openChannelFee} from '@breeztech/react-native-breez-sdk';
import {useFocusEffect} from '@react-navigation/native';
import {useQuery} from '@tanstack/react-query';
import {useCallback, useState} from 'react';

import {useAppDispatch} from '@/hooks/redux';
import {fetchBtcUsdPrice, satToMsat} from '@/screens/LightningNetwork/helpers';
import {generateInvoice} from '@/screens/LightningNetwork/helpers/breezApi';
import LightningService from '@/services/LightningService';
import {setWebSocket} from '@/storage/actions/lightningActions';
import {showWarningToast} from '@/utils/toast';
import {CONSTANTS, DEFAULT_RECEIVE_STATE} from '../helpers/lightningConstants';

export interface UseLightningReceiveState {
  url: string;
  amountInSat: string;
  note: string;
  openingFeeInSat: number;
  isSwitched: boolean;
  amountInUsd: string;
  isLoading: boolean;
  feeMessage: string;
}

export interface UseLightningReceiveActions {
  handleOnChangeAmount: (text: string) => void;
  handleAmountOnBlur: () => Promise<void>;
  handleSwitch: () => void;
  setNote: (note: string) => void;
}

export interface UseLightningReceiveReturn {
  state: UseLightningReceiveState;
  actions: UseLightningReceiveActions;
  qrCode: any;
  isQrCodeLoading: boolean;
}

export const useLightningReceive = (): UseLightningReceiveReturn => {
  const dispatch = useAppDispatch();

  const [state, setState] = useState<UseLightningReceiveState>(DEFAULT_RECEIVE_STATE);

  const fetchQrCode = async () => {
    if (!state.url) return null;

    const response = await fetch(CONSTANTS.QR_CODE_URL, {
      method: 'POST',
      headers: {
        accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: `${state.url}`,
      }),
    });

    return response.json();
  };

  const {data: qrCode, isLoading: isQrCodeLoading} = useQuery({
    queryKey: ['qrCode', state.url],
    queryFn: fetchQrCode,
    enabled: !!state.url,
  });

  const updateState = useCallback((updates: Partial<UseLightningReceiveState>) => {
    setState((current) => ({...current, ...updates}));
  }, []);

  const handleOnChangeAmount = useCallback(
    (text: string) => {
      updateState({amountInSat: text.replace(/,/g, '')});
    },
    [updateState],
  );

  const handleAmountOnBlur = useCallback(async () => {
    if (!state.amountInSat || +state.amountInSat <= 0) {
      showWarningToast('Invalid amount');
      return;
    }

    try {
      updateState({isLoading: true, openingFeeInSat: 0});

      const amountMsat = satToMsat(+state.amountInSat);
      const invoiceUrl = await generateInvoice(
        amountMsat,
        state.note || '',
        (fee: number) => updateState({openingFeeInSat: fee}),
      );

      if (invoiceUrl) {
        const usdAmount = await fetchBtcUsdPrice(+state.amountInSat);
        updateState({
          url: invoiceUrl,
          amountInUsd: usdAmount,
        });
      } else {
        showWarningToast('Invoice error');
      }
    } catch (error) {
      console.error('[Lightning] Error generating invoice:', error);
      showWarningToast('Invoice error. Please try again');
    } finally {
      updateState({isLoading: false});
    }
  }, [state.amountInSat, state.note, updateState]);

  const handleSwitch = useCallback(() => {
    updateState({
      isSwitched: !state.isSwitched,
      note: '',
    });
  }, [state.isSwitched, updateState]);

  const setNote = useCallback(
    (note: string) => {
      updateState({note});
    },
    [updateState],
  );

  const constructFeeMessage = useCallback(async () => {
    try {
      const nodeState = await nodeInfo();
      const inboundLiquidityMsat = nodeState.maxReceivableSinglePaymentAmountMsat;
      const inboundLiquiditySat =
        inboundLiquidityMsat != null ? inboundLiquidityMsat / 1_000 : 0;

      const openChannelFeeResponse = await openChannelFee({});
      const openingFees = openChannelFeeResponse.feeParams;
      const feePercentage = (openingFees.proportional * 100) / 1_000_000;
      const minFeeSat = openingFees.minMsat / 1_000;

      if (inboundLiquiditySat === 0) {
        return `A setup fee of ${feePercentage}% with a minimum of ${minFeeSat} sats will be applied.`;
      }
      return `A setup fee of ${feePercentage}% with a minimum of ${minFeeSat} sats will be applied for receiving more than ${inboundLiquiditySat} sats.`;
    } catch (error) {
      console.error('[Lightning] Error constructing setup fee message:', error);
      return '';
    }
  }, []);

  // Fetch LNURL and setup fee message when screen is focused
  useFocusEffect(
    useCallback(() => {
      const lnServer = new LightningService(generateInvoice, (url: string) =>
        updateState({url}),
      );
      dispatch(setWebSocket(lnServer.webSocket));
      lnServer.getLnurl();
      constructFeeMessage().then((feeMessage) => updateState({feeMessage}));
    }, []),
  );

  return {
    state,
    actions: {
      handleOnChangeAmount,
      handleAmountOnBlur,
      handleSwitch,
      setNote,
    },
    isQrCodeLoading,
    qrCode,
  };
};
