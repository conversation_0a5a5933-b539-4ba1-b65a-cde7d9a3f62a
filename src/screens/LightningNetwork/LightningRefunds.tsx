import {SwapInfo} from '@breeztech/react-native-breez-sdk';
import {useFocusEffect} from '@react-navigation/native';
import React, {memo, useCallback, useState} from 'react';
import {ActivityIndicator, FlatList, StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAuth} from '@/hooks/redux';
import RefundCard from './components/RefundCard';
import {fetchRefundables, processRefund} from './helpers/breezApi';

const LightningRefunds = () => {
  const {userAddresses} = useAuth();

  const [refundables, setRefundables] = useState<SwapInfo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefunding, setIsRefunding] = useState(false);

  const getRefundables = useCallback(async () => {
    try {
      setIsLoading(true);
      const refundableSwaps = await fetchRefundables();
      setRefundables(refundableSwaps);
    } catch (error) {
      console.error('[LightningRefunds] Error getting refundables:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleRefund = useCallback(
    async (swap: SwapInfo) => {
      if (isRefunding) return;

      try {
        setIsRefunding(true);
        await processRefund({
          swapAddress: swap.bitcoinAddress,
          toAddress: userAddresses[0].address,
          txId: swap.confirmedSats[0],
        });
        setRefundables((current) =>
          current.filter((s) => s.bitcoinAddress !== swap.bitcoinAddress),
        );
      } catch (error) {
        console.error('Error processing refund:', error);
      } finally {
        setIsRefunding(false);
      }
    },
    [isRefunding],
  );

  useFocusEffect(
    useCallback(() => {
      getRefundables();
    }, [getRefundables]),
  );

  const renderItem = useCallback(
    ({item}: {item: SwapInfo}) => <RefundCard swap={item} onRefund={handleRefund} />,
    [handleRefund],
  );

  const keyExtractor = useCallback((item: SwapInfo) => item.bitcoinAddress, []);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
          </View>
        ) : refundables.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              🎉 All clear. No refundable transactions to process! 🎉
            </Text>
          </View>
        ) : (
          <FlatList
            data={refundables}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </View>
  );
};

export default memo(LightningRefunds);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.gray.gray300,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listContainer: {
    paddingVertical: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 18,
    textAlign: 'center',
    color: GlobalStyles.gray.gray900,
    paddingHorizontal: 42,
    lineHeight: 32,
  },
});
