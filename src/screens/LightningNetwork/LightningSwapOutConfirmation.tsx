import {payOnchain} from '@breeztech/react-native-breez-sdk';
import {useFocusEffect, useRoute} from '@react-navigation/native';
import React, {memo, useCallback} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';
import {useSelector} from 'react-redux';

import Banner from '@/components/Banner';
import HoldToConfirmButton from '@/components/HoldToConfirmButton';
import TxSummary from '@/components/TxSummary';
import {truncateUrl} from '@/screens/LightningNetwork/helpers/index';

const LightningSwapOutConfirmation: React.FC<any> = () => {
  const {params} = useRoute() as any;
  const userAddresses = useSelector(
    (state: any) => state.auth.userAddress ?? state.auth.userAddresses,
  );

  const executeSwapOut = async () => {
    try {
      await payOnchain({
        recipientAddress: userAddresses[0].address,
        prepareRes: params.prepareResponse,
      });
      return true;
    } catch (err) {
      console.error(err);
      return false;
    }
  };

  useFocusEffect(
    useCallback(() => {
      // @ts-ignore
      const prepared = params!.prepareResponse;
    }, []),
  );

  const summaryFields = [
    {
      label: 'Recipient Address',
      value: truncateUrl(userAddresses[0].address),
    },
    {
      label: 'Recipient Amount (Sat)',
      value: params.prepareResponse.recipientAmountSat.toString(),
    },
    {
      label: 'Sender Amount (Sat)',
      value: params.prepareResponse.senderAmountSat.toString(),
    },
    {
      label: 'Total Fees',
      value: params.prepareResponse.totalFees.toString(),
    },
  ];

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.banner}>
          <Banner
            type="warning"
            message="Withdrawal is made to your BTC's wallet address."
            restBannerContentStyles={styles.banner}
          />
        </View>

        <TxSummary title="Swap Out Details" fields={summaryFields} />

        <View style={styles.footer}>
          <HoldToConfirmButton disabled={false} onPress={executeSwapOut} />
        </View>
      </ScrollView>
    </View>
  );
};

export default memo(LightningSwapOutConfirmation);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  banner: {
    marginTop: 4,
    marginBottom: 22,
    paddingHorizontal: 26,
  },
  footer: {
    alignSelf: 'center',
    paddingTop: 36,
    paddingBottom: 14,
  },
});
