import React, {memo, useCallback, useState} from 'react';
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

import HistoryIcon from '@/assets/icons/history.svg';
import ReverseIcon from '@/assets/icons/reverse.svg';
import SendReceiveButtons from '@/components/SendReceiveButtons/SendReceiveButtons';
import {LightningHomeSkeleton} from '@/components/loading-skeletons/LightningHomeSkeleton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useAppDispatch, useAppSelector, useAuth} from '@/hooks/redux';
import {useScreenSize} from '@/hooks/screen';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {setBalanceMsat, setRefundablesLength} from '@/storage/actions/lightningActions';
import {nodeInfo} from '@breeztech/react-native-breez-sdk';
import {formatLnBalance} from './helpers';
import {fetchRefundables} from './helpers/breezApi';
import {getBreezSDKStatus, useBreezSDK} from './hooks/useBreezSDK';
import {useRedeem} from './hooks/useRedeem';

const LightningHome: React.FC = () => {
  const dispatch = useAppDispatch();
  const {isSmallScreen} = useScreenSize();
  const {user, userAddresses} = useAuth();
  const {open: openSendSheet} = useBottomSheet('lightningSend');
  const {open: openHistorySheet} = useBottomSheet('lightningHistory');
  const {balanceMsat, refundablesLength} = useAppSelector((state) => state.lightning);

  const [isLoading, setIsLoading] = useState(false);

  const {
    data,
    isLoading: isBreezSDKLoading,
    isError,
    error,
  } = useBreezSDK({
    mnemonic: user.wallet[0].mnemonic,
    nodeId: userAddresses[0].address,
  });

  // Use the `pendingSwap` to show a pending balance
  const {pendingSwap, checkInProgressSwap} = useRedeem();

  const updateBalance = useCallback(async () => {
    try {
      const channelInfo = await nodeInfo();
      dispatch(setBalanceMsat(channelInfo.channelsBalanceMsat));
    } catch (err) {
      console.error('[Lightning] Error updating balance:', err);
    }
  }, [dispatch]);

  const updateRefundables = useCallback(async () => {
    const refundListLength = (await fetchRefundables()).length;
    dispatch(setRefundablesLength(refundListLength));
  }, [dispatch]);

  const handleRefresh = useCallback(async () => {
    setIsLoading(true);
    await Promise.all([updateBalance(), updateRefundables(), checkInProgressSwap()]);
    setIsLoading(false);
  }, [updateBalance, updateRefundables, checkInProgressSwap]);

  const handleSendPress = useCallback(() => {
    openSendSheet(
      {
        type: 'lightningSend',
        data: [],
      },
      90,
      false,
    );
  }, [openSendSheet]);

  const handleReceivePress = () => {
    navigateViaBottomTabs('Lightning', 'LightningReceive');
  };

  const handleSwapPress = () => {
    navigateViaBottomTabs('Lightning', 'LightningSwaps');
  };

  const handleHistoryPress = useCallback(() => {
    openHistorySheet(
      {
        type: 'lightningHistory',
        data: [],
      },
      80,
      false,
    );
  }, [openHistorySheet]);

  const handleRefundPress = useCallback(() => {
    navigateViaBottomTabs('Lightning', 'LightningRefunds');
  }, []);

  const balanceLabelFontSize = {fontSize: isSmallScreen ? 18 : 18};
  const balanceFontSize = {fontSize: isSmallScreen ? 38 : 44};
  const menuItemFontSize = {fontSize: isSmallScreen ? 19 : 21};
  const balanceMarginStyle = {
    marginTop: isSmallScreen ? 32 : 42,
    marginBottom: isSmallScreen ? 56 : 82,
  };
  const menuItemMarginStyle = {marginBottom: isSmallScreen ? 0 : 40};

  if (!getBreezSDKStatus() || isBreezSDKLoading) {
    return (
      <View style={styles.container}>
        <LightningHomeSkeleton />
      </View>
    );
  }

  if (isError || error) {
    console.error('[Breez SDK] Error:', error);
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorTitle}>Oops!</Text>
        <Text style={styles.errorText}>
          We are experiencing issues with our Lightning Network Provider. Please try again
          later. We apologize for the inconvenience!
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[styles.scrollContent, {flex: 1}]}
      >
        <View style={styles.content}>
          <View style={[styles.balanceSection, balanceMarginStyle]}>
            <Text style={[styles.balanceLabel, balanceLabelFontSize]}>
              Available Satoshis
            </Text>
            <Text style={[styles.balanceText, balanceFontSize]}>
              {formatLnBalance(balanceMsat)}
            </Text>
          </View>

          <View style={styles.menuItemsContainer}>
            <TouchableOpacity
              style={[styles.menuItem, menuItemMarginStyle]}
              onPress={handleHistoryPress}
              activeOpacity={0.7}
            >
              <HistoryIcon
                width={32}
                height={32}
                color={GlobalStyles.primary.primary900}
              />
              <Text style={[styles.menuItemText, menuItemFontSize]}>History</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.menuItemsContainer}>
            {refundablesLength > 0 && (
              <TouchableOpacity
                style={[styles.menuItem, menuItemMarginStyle]}
                onPress={handleRefundPress}
                activeOpacity={0.7}
              >
                <ReverseIcon
                  width={32}
                  height={32}
                  color={GlobalStyles.primary.primary900}
                />
                <Text style={[styles.menuItemText, menuItemFontSize]}>
                  {`Refunds (${refundablesLength})`}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>

      <View style={[styles.footer, isSmallScreen && styles.footerSmall]}>
        <SendReceiveButtons
          handleReceivePress={handleReceivePress}
          handleSendPress={handleSendPress}
          handleBuyPress={handleSwapPress}
          buyButtonTitle={'Top Up Sats'}
        />
      </View>
    </View>
  );
};

export default memo(LightningHome);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  balanceSection: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  balanceLabel: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.gray.gray800,
    marginBottom: 12,
  },
  balanceText: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.base.black,
    letterSpacing: 1,
  },
  menuItemsContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  menuItem: {
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'center',
  },
  menuItemText: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.base.black,
    marginLeft: 16,
    marginTop: 6,
  },
  footer: {
    backgroundColor: GlobalStyles.gray.gray500,
    borderTopLeftRadius: 36,
    borderTopRightRadius: 36,
    padding: 24,
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
  },
  footerSmall: {
    padding: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorTitle: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.base.black,
    fontSize: 24,
    textAlign: 'center',
    marginBottom: 8,
  },
  errorText: {
    fontFamily: GlobalStyles.fonts.poppins,
    color: GlobalStyles.gray.gray900,
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 22,
  },
});
