import {
  ReverseSwapPairInfo,
  SwapAmountType,
  prepareOnchainPayment,
  recommendedFees,
} from '@breeztech/react-native-breez-sdk';
import React, {memo, useCallback, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import CurrencyInput from '@/components/CurrencyInput';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {showWarningToast} from '@/utils/toast';
import {satToBTC} from '../helpers/index';
import RangeContainer from './RangeContainer';

interface SwapOutFormProps {
  fetchedData: ReverseSwapPairInfo;
}

const SwapOutForm: React.FC<SwapOutFormProps> = ({fetchedData}) => {
  const [amountInSat, setAmountInSat] = useState(0);
  const [isValid, setIsValid] = useState(false);

  const handleAmountChange = useCallback(
    (text: string) => {
      const amount = Number(text.replace(/,/g, ''));
      setAmountInSat(amount);
      setIsValid(amount >= fetchedData.min && amount <= fetchedData.max);
    },
    [fetchedData.min, fetchedData.max],
  );

  const handleAmountBlur = useCallback(async () => {
    if (amountInSat < fetchedData.min || amountInSat > fetchedData.max) {
      showWarningToast('Amount is out of range');
      return;
    }
    setIsValid(true);
  }, [amountInSat, fetchedData.min, fetchedData.max]);

  const handleConfirmWithdrawPress = useCallback(async () => {
    try {
      const rFees = await recommendedFees();
      const satPerVbyte = rFees.fastestFee;

      const prepareResponse = await prepareOnchainPayment({
        amountSat: amountInSat,
        amountType: SwapAmountType.SEND,
        claimTxFeerate: satPerVbyte,
      });

      navigateViaBottomTabs('Lightning', 'LightningSwapOutConfirmation', {
        prepareResponse,
      });
    } catch (err) {
      console.error('[Lightning] Error preparing onchain payment:', err);
    }
  }, [amountInSat]);

  return (
    <View style={styles.container}>
      <RangeContainer
        minSats={fetchedData.min}
        maxSats={fetchedData.max}
        minBtc={satToBTC(fetchedData.min)}
        maxBtc={satToBTC(fetchedData.max)}
      />
      <View style={styles.content}>
        <View style={styles.formSection}>
          <View style={styles.inputContainer}>
            <CurrencyInput
              label="You Withdraw"
              selectedOption={{fullName: 'Satoshis', label: 'SATS', type: 'crypto'}}
              value={amountInSat ? amountInSat.toString() : ''}
              onChangeText={handleAmountChange}
              onBlur={handleAmountBlur}
              decimals={0}
              onOptionPress={() => {}}
              optionsDisabled={true}
            />

            <Text style={styles.approximateValue}>
              {`Approximate value: ${satToBTC(amountInSat)} BTC`}
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <MButton
          onPress={handleConfirmWithdrawPress}
          text="Confirm Withdraw"
          disabled={!isValid}
        />
      </View>
    </View>
  );
};

export default memo(SwapOutForm);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  content: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
  },
  formSection: {
    width: '100%',
    marginTop: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  approximateValue: {
    color: GlobalStyles.gray.gray900,
    fontSize: 14,
    marginLeft: 8,
    marginTop: 6,
    fontFamily: GlobalStyles.fonts.poppins,
  },
  buttonContainer: {
    width: '90%',
    alignSelf: 'center',
    paddingBottom: 16,
  },
});
