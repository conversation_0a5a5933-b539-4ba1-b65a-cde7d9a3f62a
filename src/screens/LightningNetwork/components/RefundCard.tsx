import {SwapInfo} from '@breeztech/react-native-breez-sdk';
import React, {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';

interface RefundCardProps {
  swap: SwapInfo;
  onRefund: (swap: SwapInfo) => void;
}

export const RefundCard: React.FC<RefundCardProps> = ({swap, onRefund}) => {
  const formattedDate = new Date(swap.createdAt * 1000);

  return (
    <View style={styles.card}>
      <View style={styles.header}>
        <Text style={styles.amount}>{swap.unconfirmedSats} sats</Text>
        <Text style={styles.date}>{formattedDate.toLocaleDateString()}</Text>
      </View>

      <View style={styles.details}>
        <Text style={styles.label}>Address:</Text>
        <Text style={styles.value} numberOfLines={1}>
          {swap.bitcoinAddress}
        </Text>
      </View>

      <TouchableOpacity style={styles.refundButton} onPress={() => onRefund(swap)}>
        <Text style={styles.refundButtonText}>Refund Now</Text>
      </TouchableOpacity>
    </View>
  );
};

export default memo(RefundCard);

const styles = StyleSheet.create({
  card: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  amount: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.primary.primary500,
  },
  date: {
    fontSize: 14,
    color: GlobalStyles.gray.gray600,
  },
  details: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: GlobalStyles.gray.gray600,
    marginBottom: 4,
  },
  value: {
    fontSize: 14,
    color: GlobalStyles.gray.gray900,
  },
  refundButton: {
    backgroundColor: GlobalStyles.primary.primary500,
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
  },
  refundButtonText: {
    color: GlobalStyles.base.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
