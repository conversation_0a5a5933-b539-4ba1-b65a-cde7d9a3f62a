import {SwapInfo} from '@breeztech/react-native-breez-sdk';
import React, {memo} from 'react';
import {StyleSheet, View} from 'react-native';

import Logo from '@/components/Logo/AssetLogo';
import GlobalStyles from '@/constants/GlobalStyles';
import {satToBTC} from '../helpers';
import QrForm from './QrForm';
import RangeContainer from './RangeContainer';

interface SwapInFormProps {
  swapInInfo: SwapInfo;
  qrCode: string;
  isQrCodeLoading: boolean;
}

const SwapInForm: React.FC<SwapInFormProps> = ({swapInInfo, qrCode, isQrCodeLoading}) => {
  return (
    <View style={styles.container}>
      <RangeContainer
        minSats={swapInInfo.minAllowedDeposit}
        maxSats={swapInInfo.maxAllowedDeposit}
        minBtc={satToBTC(swapInInfo.minAllowedDeposit)}
        maxBtc={satToBTC(swapInInfo.maxAllowedDeposit)}
      />
      <View style={styles.content}>
        {!isQrCodeLoading && (
          <View style={styles.qrContainer}>
            <QrForm
              icon={<Logo name="LIGHTNINGSVG" />}
              qrCode={qrCode}
              url={swapInInfo.bitcoinAddress}
              isLoading={isQrCodeLoading}
            />
          </View>
        )}
      </View>
    </View>
  );
};

export default memo(SwapInForm);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  content: {
    flex: 1,
    width: '100%',
    alignItems: 'center',
  },
  qrContainer: {
    marginTop: 32,
    width: '100%',
    maxWidth: 350,
    // alignItems: 'center',
  },
});
