import React, {memo} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {formatNumber} from '@/utils';

interface RangeContainerProps {
  minSats: number;
  maxSats: number;
  minBtc: number;
  maxBtc: number;
}

const RangeContainer: React.FC<RangeContainerProps> = ({
  minSats,
  maxSats,
  minBtc,
  maxBtc,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.row}>
          <Text style={styles.label}>Min:</Text>
          <View style={styles.valuesContainer}>
            <Text style={styles.satsValue}>{formatNumber(minSats)} sats</Text>
            <Text style={styles.btcValue}>{formatNumber(minBtc)} BTC</Text>
          </View>
        </View>

        <View style={styles.separator} />

        <View style={styles.row}>
          <Text style={styles.label}>Max:</Text>
          <View style={styles.valuesContainer}>
            <Text style={styles.satsValue}>{formatNumber(maxSats)} sats</Text>
            <Text style={styles.btcValue}>{formatNumber(maxBtc)} BTC</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(RangeContainer);

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 24,
    marginHorizontal: 22,
    marginVertical: 16,
    paddingVertical: 18,
    // Shadows
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  content: {
    width: '100%',
    paddingHorizontal: 24,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  label: {
    fontSize: 17,
    color: GlobalStyles.gray.gray900,
    width: 60,
    marginLeft: 13,
  },
  valuesContainer: {
    flex: 1,
    alignItems: 'center',
  },
  satsValue: {
    fontSize: 18,
    color: GlobalStyles.base.black,
    textAlign: 'center',
    marginBottom: 4,
  },
  btcValue: {
    fontSize: 16,
    color: GlobalStyles.gray.gray900,
    letterSpacing: 0.5,
    textAlign: 'center',
  },
  separator: {
    height: 1,
    backgroundColor: GlobalStyles.gray.gray500,
    marginVertical: 6,
  },
});
