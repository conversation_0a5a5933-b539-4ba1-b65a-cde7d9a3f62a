import React, {memo} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import CopySvg from '@/assets/icons/copy.svg';
import ProfitSvg from '@/assets/icons/profit.svg';
import LoadingHandler from '@/components/LoadingHandler';
import GlobalStyles from '@/constants/GlobalStyles';
import {handleCopy, handleSharePress} from '@/utils';
import {getTextBasedOnPrefix} from '../helpers';

interface QrFormProps {
  qrCode: any;
  url: string;
  isLoading: boolean;
  openingFeeInSat?: number;
}

// Track when the error state started
let errorStartTime = 0;

const QrForm = ({qrCode, url, isLoading, openingFeeInSat}: QrFormProps): JSX.Element => {
  const config = getTextBasedOnPrefix(url);

  const renderQrContent = () => {
    // Reset error timer if we have a URL or are loading
    if (url || isLoading) {
      errorStartTime = 0;

      return isLoading ? (
        <View style={styles.qr}>
          <LoadingHandler />
        </View>
      ) : (
        <TouchableOpacity onPress={() => handleCopy(url)}>
          <Image style={styles.qr} source={{uri: qrCode?.data}} />

          <View style={styles.urlContainer}>
            <Text style={styles.urlTitle}>{config?.title}</Text>
            <Text style={styles.urlText}>{config?.truncatedUrl}</Text>
          </View>
        </TouchableOpacity>
      );
    }

    // Start error timer if not already started
    if (!errorStartTime) {
      errorStartTime = Date.now();
    }

    // Show loading for 2 seconds before error
    if (Date.now() - errorStartTime < 2000) {
      return (
        <View style={styles.qr}>
          <LoadingHandler />
        </View>
      );
    }

    // Show error after 2 seconds
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Network error. Please check your connection and try again.
        </Text>
      </View>
    );
  };

  const renderFeeMessage = () => {
    if (openingFeeInSat && openingFeeInSat > 0) {
      return (
        <Text style={styles.openingFeeText}>
          {`A setup fee of ${openingFeeInSat} sats has been applied.`}
        </Text>
      );
    }
    return null;
  };

  return (
    <View style={styles.qrBoxContainer}>
      <View style={styles.qrContainer}>{renderQrContent()}</View>

      {renderFeeMessage()}

      {url && (
        <View style={styles.miscContainer}>
          <TouchableOpacity style={styles.copyContainer} onPress={() => handleCopy(url)}>
            <CopySvg />
            <Text style={styles.copyText}>Copy</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.shareContainer}
            onPress={() => handleSharePress(url)}
          >
            <ProfitSvg />
            <Text style={styles.shareText}>Share</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default memo(QrForm);

const styles = StyleSheet.create({
  qrBoxContainer: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 8,
  },

  qrContainer: {
    width: 300,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    padding: 4,
    paddingTop: 32,
    marginBottom: 12,
  },
  qr: {
    width: 290,
    height: 290,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  urlContainer: {
    alignItems: 'center',
  },
  urlTitle: {
    color: GlobalStyles.base.black,
    fontWeight: 'bold',
  },
  urlText: {
    fontSize: 13,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 18,
    textAlign: 'center',
    color: GlobalStyles.primary.primary500,
  },
  openingFeeText: {
    color: GlobalStyles.error.error700,
    marginTop: 9,
    textAlign: 'center',
    justifyContent: 'center',
    marginHorizontal: 15,
    fontWeight: '500',
  },
  miscContainer: {
    width: 300,
    flexDirection: 'row',
    marginTop: 12,
  },
  copyContainer: {
    flex: 1,
    flexDirection: 'row',
    margin: 10,
    alignItems: 'center',
    alignSelf: 'flex-start',
    justifyContent: 'flex-start',
    marginLeft: 20,
  },
  copyText: {
    fontSize: 12,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '600',
    fontStyle: 'normal',
    lineHeight: 14,
    textAlign: 'center',
    color: GlobalStyles.primary.primary500,
    textTransform: 'capitalize',
    marginLeft: 2,
  },
  shareContainer: {
    flex: 1,
    flexDirection: 'row',
    margin: 10,
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    marginRight: 20,
  },
  shareText: {
    fontSize: 12,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '600',
    fontStyle: 'normal',
    lineHeight: 29,
    textAlign: 'center',
    color: GlobalStyles.primary.primary500,
    textTransform: 'capitalize',
    marginLeft: 2.5,
  },
  errorContainer: {
    width: 290,
    height: 290,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.poppins,
    lineHeight: 24,
  },
});
