import {InputTypeVariant, LnUrlPayRequestData} from '@breeztech/react-native-breez-sdk';
import {useRoute} from '@react-navigation/native';
import React, {memo} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import ScanIcon from '@/assets/icons/scan.svg';
import Banner from '@/components/Banner';
import Camera from '@/components/Camera';
import CurrencyInput from '@/components/CurrencyInput';
import HoldToConfirmButton from '@/components/HoldToConfirmButton';
import Logo from '@/components/Logo/AssetLogo';
import MButton from '@/components/MButton';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import Success from '@/screens/Success';
import {useLightningSend} from './hooks/useLightningSend';

export interface LightningSendProps {
  input?: {
    type: InputTypeVariant.LN_URL_PAY;
    data: LnUrlPayRequestData;
  };
  lnurl?: string;
  bolt11?: string;
}

const LightningSend: React.FC<any> = () => {
  const route = useRoute();
  const params = route.params as LightningSendProps;

  const {state, actions, isNextDisabled} = useLightningSend(params);

  const renderAddressInput = () => (
    <View style={styles.assetContainer}>
      <View style={styles.inputContainer}>
        <TextInput
          label={params.bolt11 ? 'Invoice Address' : 'LNURL Address'}
          value={state.recipientAddress}
          onChangeText={actions.handleAddressChange}
          styles={{
            inputContainer: {
              borderWidth: 0,
            },
            input: {
              paddingRight: 40,
            },
          }}
        />

        <TouchableOpacity
          style={styles.scanIcon}
          onPress={() => actions.handleQRScan('')}
        >
          <ScanIcon width={28} height={28} />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAmountInput = () => (
    <View style={styles.assetContainer}>
      <View style={styles.currencyInput}>
        <CurrencyInput
          label="You Send"
          selectedOption={{fullName: 'Satoshis', label: 'SATS', type: 'crypto'}}
          value={state.invoice ? state.invoice.satoshis.toString() : state.amountInSat}
          onChangeText={actions.handleAmountChange}
          decimals={0}
          editable={!state.invoice}
          onOptionPress={() => {}}
          optionsDisabled={true}
          styles={{
            container: {
              marginBottom: 6,
            },
          }}
        />
      </View>
    </View>
  );

  if (state.qrVisible) {
    return (
      <View style={styles.footer}>
        <Camera
          onBarcodeRead={(e: any) => {
            actions.handleQRScan(e);
          }}
        >
          <View style={styles.cameraContainer} />
        </Camera>

        <MButton text="Close" onPress={() => actions.handleQRScan('')} />
      </View>
    );
  }

  if (state.success) {
    return (
      <Success
        title="Success"
        description="Your transaction has been completed!"
        onFinish={() => navigateViaBottomTabs('Lightning', 'LightningHome')}
        buttonText="Back to Home"
        isSuccess={state.success}
      />
    );
  }

  return (
    <>
      <View style={styles.screen}>
        <View style={styles.sendContainer}>
          <View style={styles.logoContainer}>
            <Logo name="LIGHTNINGSVG" />
          </View>

          {renderAddressInput()}

          {renderAmountInput()}

          {state.errorMessage && (
            <Text style={styles.errorText}>{state.errorMessage}</Text>
          )}
        </View>

        <View style={styles.banner}>
          <Banner
            type="warning"
            message="Once send crypto transactions are irreversible."
          />
        </View>

        <View style={styles.footer}>
          <HoldToConfirmButton
            disabled={isNextDisabled}
            onPress={actions.handleNextPress}
          />
        </View>
      </View>
    </>
  );
};

export default memo(LightningSend);

export const styles = StyleSheet.create({
  screen: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  sendContainer: {
    marginTop: 42,
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  logoContainer: {
    width: 84,
    height: 84,
    borderRadius: 42,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -40,
    marginBottom: 18,
    backgroundColor: GlobalStyles.base.white,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  scanIcon: {
    position: 'absolute',
    right: 12,
  },
  approximateValue: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    marginLeft: 6,
  },
  banner: {
    marginVertical: 22,
    paddingHorizontal: 10,
  },
  assetContainer: {
    width: '100%',
    flexDirection: 'column',
    paddingHorizontal: 10,
    paddingBottom: 26,
  },
  inputContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray600,
  },
  currencyInput: {
    flexDirection: 'row',
  },
  cameraContainer: {
    marginTop: -80,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    width: '90%',
    paddingBottom: 14,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.error.error500,
    marginTop: 5,
    marginBottom: 10,
  },
});
