import React, {memo, useCallback, useState} from 'react';
import {ScrollView, StyleSheet, View} from 'react-native';

import BottomSheetScreen from '@/components/BottomSheetScreen';
import DetectSwipe from '@/components/DetectSwipe';
import Slider from '@/components/Slider';
import {LightningSwapsSkeleton} from '@/components/loading-skeletons/LightningSwapsSkeleton';
import {useScreenSize} from '@/hooks/screen';
import {useLightningSwaps} from '@/screens/LightningNetwork/hooks/useLightningSwaps';
import SwapInForm from './components/SwapInForm';
import SwapOutForm from './components/SwapOutForm';

const LightningSwaps: React.FC = () => {
  const {isSmallScreen} = useScreenSize();

  const {
    state: {swapInInfo, swapOutInfo},
    qrCode,
    isQrCodeLoading,
  } = useLightningSwaps();

  const [isSwapIn, setIsSwapIn] = useState(true);
  const [isSheetOpen, setIsSheetOpen] = useState(true);

  const handleTabChange = useCallback(
    (tabName: string) => setIsSwapIn(tabName === 'deposit'),
    [setIsSwapIn],
  );

  if (!swapInInfo || !swapOutInfo) {
    return (
      <View style={styles.container}>
        <LightningSwapsSkeleton />
      </View>
    );
  }

  const bottomSheetHeight = isSmallScreen ? 40 : 35;

  return (
    <View style={styles.container}>
      <View style={styles.sliderContainer}>
        <Slider
          tabs={['deposit', 'withdraw']}
          activeTab={isSwapIn ? 'deposit' : 'withdraw'}
          onTabClick={handleTabChange}
        />
      </View>

      <View style={styles.mainContainer}>
        <View style={styles.swipeContainer}>
          <DetectSwipe
            onSwipeLeft={() => setIsSwapIn(false)}
            onSwipeRight={() => setIsSwapIn(true)}
          >
            <ScrollView
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              <View style={styles.content}>
                {isSwapIn ? (
                  <SwapInForm
                    swapInInfo={swapInInfo}
                    qrCode={qrCode}
                    isQrCodeLoading={isQrCodeLoading}
                  />
                ) : (
                  <SwapOutForm fetchedData={swapOutInfo} />
                )}
              </View>
            </ScrollView>
          </DetectSwipe>
        </View>
      </View>

      <BottomSheetScreen
        isOpen={isSheetOpen}
        onClose={() => setIsSheetOpen(false)}
        title="Important Notice"
        description="Please ensure your transaction amount is within the specified range limits. Transactions outside these limits may result in loss of funds."
        height={bottomSheetHeight}
      />
    </View>
  );
};

export default memo(LightningSwaps);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  sliderContainer: {
    alignItems: 'center',
  },
  mainContainer: {
    flex: 1,
  },
  swipeContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
});
