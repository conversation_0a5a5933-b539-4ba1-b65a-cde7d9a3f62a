import {memo, useCallback, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAuth} from '@/hooks/redux';
import WalletService from '@/services/WalletService';
import {setIsLoggedIn, setUser, setUserAddresses} from '@/storage/actions/authActions';
import {AuthAddress, AuthAddresses} from '@/types/authTypes';
import {enableNotifications, handleCopy} from '@/utils';
import Banner from '@components/Banner';
import CopyPasteButton from '@components/CopyPasteBtn';
import MButton from '@components/MButton';
import {Footer} from '@styles/styled-components';
import theme from '@/styles/themes';
import {getBitcoinWallet, getExistingWallets} from '../helpers/import-wallet-helpers';

const ImportNewWallet: React.FC = ({navigation}: any) => {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();
  const {user} = useAuth();

  const walletService = new WalletService();

  const [loading, setLoading] = useState<boolean>(false);

  const mnemonic = user?.wallet[0]?.mnemonic || '';
  const seedWords = mnemonic.split(' ');

  const handleImportWallet = useCallback(() => {
    setLoading(true);

    navigation.setParams({loading: true});

    setTimeout(async () => {
      try {
        const {wallet, address} = await getBitcoinWallet(mnemonic);

        const bitcoinChains = await walletService.walletLoggerBTC(
          address.address,
          wallet.xPubsList?.[0]?.accountXpub ?? '',
        );

        if (bitcoinChains.status === 409) {
          const {wallets, addresses} = await getExistingWallets(wallet, address);

          dispatch(setUserAddresses(addresses));

          dispatch(
            setUser({
              wallet: wallets,
              pinCode: '',
            }),
          );

          const firstAddress = addresses[0] as AuthAddress | undefined;
          if (firstAddress && 'address' in firstAddress && firstAddress.address) {
            await enableNotifications(firstAddress.address);
          }

          dispatch(setIsLoggedIn());
        } else {
          const createdWallets = await walletService.createWallets(mnemonic);

          const createdAddresses = await walletService.createAddresses(createdWallets!);

          const {wallets, addresses, loggerRequest} =
            await walletService.createLoggerRequest(
              createdWallets!,
              createdAddresses as AuthAddresses,
            );

          await walletService.walletLogger(loggerRequest);

          dispatch(setUserAddresses(addresses));
          dispatch(setUser({wallet: wallets, pinCode: ''}));

          const firstAddress = addresses[0] as AuthAddress | undefined;
          if (firstAddress && 'address' in firstAddress && firstAddress.address) {
            await enableNotifications(firstAddress.address);
          }

          dispatch(setIsLoggedIn());
        }
      } catch (error) {
        console.error('[ImportNewWallet] Error importing wallet:', error);
      } finally {
        setLoading(false);
      }
    }, 500);
  }, [dispatch, mnemonic, navigation, walletService]);

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <View style={styles.seedPhrase}>
          {seedWords.map((word: string, index: number) => (
            <TouchableOpacity
              onPress={() => {}}
              key={index}
              style={styles.seedWord}
              disabled={false}
            >
              <View key={index} style={styles.wordContainer}>
                <Text style={styles.wordIndex}>{`${index + 1}.`}</Text>
                <Text style={styles.seedWordText}>{`${word}`}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.copyButtonContainer}>
          <CopyPasteButton clipboardAction={() => handleCopy(mnemonic)} title="copy" />
        </View>

        <Banner
          type="warning"
          message={t('messages.alert.never_share_your_secret_phrase')}
        />
      </View>

      <Footer style={styles.footer}>
        <MButton text="Continue" onPress={handleImportWallet} isLoading={loading} />
      </Footer>
    </View>
  );
};

export default memo(ImportNewWallet);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen * 2,
    paddingTop: theme.layout.pv.screen,
    gap: theme.spacing.lg,
  },
  seedPhrase: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xl,
    gap: theme.spacing.md,
  },
  copyButtonContainer: {
    marginTop: theme.spacing.sm,
  },
  seedWord: {
    width: '30%',
    height: 40,
    padding: theme.spacing.sm,
    justifyContent: 'center',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray800,
    borderRadius: 8,
    backgroundColor: GlobalStyles.base.white,
  },
  seedWordText: {
    fontSize: theme.typography.xs,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    marginTop: 2,
  },
  wordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  wordIndex: {
    fontSize: theme.typography.xs + 2,
    color: GlobalStyles.gray.gray500,
  },
  footer: {
    gap: theme.isSmallDevice ? theme.spacing.sm : theme.spacing.md,
    paddingHorizontal: theme.isSmallDevice
      ? theme.layout.ph.screen * 3
      : theme.layout.ph.screen * 2,
  },
});
