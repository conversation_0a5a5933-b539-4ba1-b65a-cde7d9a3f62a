import {useFocusEffect} from '@react-navigation/native';
import {memo, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAuth} from '@/hooks/redux';
import {KEYCHAIN_BACKUP_PROVIDER} from '@/screens/KeychainBackup/helpers-keychain';
import {setOnboardingSavedToICloud} from '@/storage/actions/sharedActions';
import {handleCopy, shuffleArray} from '@/utils';
import Banner from '@components/Banner';
import CopyPasteButton from '@components/CopyPasteBtn';
import MButton from '@components/MButton';
import {Footer} from '@styles/styled-components';
import theme from '@/styles/themes';

const NewWalletScreen: React.FC = ({navigation}: any) => {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();
  const {user} = useAuth();

  const mnemonic = user?.wallet[0].mnemonic || '';
  const seedWords = mnemonic.split(' ');

  const handleSaveToICloud = useCallback(() => {
    dispatch(setOnboardingSavedToICloud(true));
    navigation.navigate('Keychain', {
      screen: 'KeychainWalletLabel',
      params: {bottomTabsPresent: true},
    });
  }, [dispatch, navigation]);

  const handleSaveManually = useCallback(() => {
    navigation.replace('VerifyImport', {
      shuffledPhrases: shuffleArray(seedWords),
      seedPhrase: mnemonic,
    });
  }, [mnemonic, navigation, seedWords]);

  useFocusEffect(
    useCallback(() => {
      if (mnemonic === '') {
        navigation.goBack();
        navigation.setParams({loading: false});
      }
    }, []),
  );

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <View style={styles.seedPhrase}>
          {seedWords.map((word: any, index: number) => (
            <View key={index} style={styles.seedWord}>
              <View style={styles.wordContainer}>
                <Text style={styles.wordIndex}>{`${index + 1}.`}</Text>

                <Text style={styles.seedWordText}>{`${word}`}</Text>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.copyButtonContainer}>
          <CopyPasteButton clipboardAction={() => handleCopy(mnemonic)} title="copy" />
        </View>

        <Banner
          type="warning"
          message={t('messages.alert.never_share_your_secret_phrase')}
        />
      </View>

      <Footer style={styles.footer}>
        <MButton
          text={t('keychain.keychainBackup', {opt: KEYCHAIN_BACKUP_PROVIDER})}
          onPress={handleSaveToICloud}
        />

        <MButton text="Save Manually" variant="secondary" onPress={handleSaveManually} />
      </Footer>
    </View>
  );
};

export default memo(NewWalletScreen);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen * 2,
    paddingTop: theme.layout.pv.screen,
    gap: theme.spacing.lg,
  },
  seedPhrase: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: theme.spacing.xl,
    gap: theme.spacing.md,
  },
  copyButtonContainer: {
    marginTop: theme.spacing.sm,
  },
  seedWord: {
    width: '30%',
    height: 40,
    padding: theme.spacing.sm,
    justifyContent: 'center',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray800,
    borderRadius: 8,
    backgroundColor: GlobalStyles.base.white,
  },
  seedWordText: {
    fontSize: theme.typography.xs,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    marginTop: 2,
  },

  wordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  wordIndex: {
    fontSize: theme.typography.xs + 2,
    color: GlobalStyles.gray.gray500,
  },
  footer: {
    gap: theme.isSmallDevice ? theme.spacing.sm : theme.spacing.md,
    paddingHorizontal: theme.isSmallDevice
      ? theme.layout.ph.screen * 3
      : theme.layout.ph.screen * 2,
  },
});
