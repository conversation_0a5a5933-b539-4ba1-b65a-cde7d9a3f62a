import LottieView from 'lottie-react-native';
import {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import lottie from '@assets/lottie/creating.json';

type Props = {
  isWalletBeingCreated: boolean;
};

const WalletLoader: React.FC<Props> = ({isWalletBeingCreated}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <Text style={styles.titleText}>
        {isWalletBeingCreated ? t('wallet.creatingWallet') : t('wallet.importingWallet')}
      </Text>

      <LottieView
        source={lottie}
        style={styles.animation}
        renderMode="SOFTWARE"
        autoPlay
        loop
      />
      <Text style={styles.subText}>{t('please_wait')}</Text>
    </View>
  );
};

export default memo(WalletLoader);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleText: {
    color: GlobalStyles.primary.primary600,
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  animation: {
    width: '100%',
    height: '50%',
  },
  subText: {
    color: GlobalStyles.base.black,
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 20,
  },
});
