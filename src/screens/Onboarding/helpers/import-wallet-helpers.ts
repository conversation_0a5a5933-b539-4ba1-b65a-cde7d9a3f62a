import {NativeModules} from 'react-native';

import {ChainsOrder} from '@/constants/Chains';
import {WalletLoggerInstance} from '@/services/BackendServices';
import {AuthAddress, NativeUserWallet} from '@/types/authTypes';

const {WalletManagerBridge} = NativeModules;
const {Enumerations, Services} = require('@AssetifyNet/cryptoapis-kms');

const parseChains = (chain: string) => {
  switch (chain) {
    case 'btc':
      return 'bitcoin';
    case 'eth':
      return 'ethereum';
    case 'bsc':
      return 'binance-smart-chain';
    case 'tron':
      return 'trx';
    case 'btc_cash':
      return 'bitcoin-cash';
    case 'ltc':
      return 'litecoin';
    case 'doge':
      return 'dogecoin';
    case 'avax':
      return 'avalanche';
    case 'avalanche':
      return 'avalanche';
    default:
      return chain;
  }
};

export const getBitcoinWallet = async (mnemonic: string) => {
  const wallet: NativeUserWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
    mnemonic,
    Enumerations.Blockchains.BITCOIN,
  );
  const address: AuthAddress = {
    address: wallet.address!,
    privateKey: wallet.privateKey!,
    publicKey: wallet.publicKey!,
    chain: wallet.blockchain,
  };

  return {wallet, address};
};

const fetchExistingWallet = (address: string) =>
  WalletLoggerInstance.get(`/v2/wallet/xpub-data/${address}/mainnet`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  })
    .then((response) => response.data)
    .catch((error) => {
      console.log('fetchExistingWallet', error);
      throw error;
    });

export const getExistingWallets = async (
  bitcoinWallet: NativeUserWallet,
  address: any,
) => {
  let wallets = [];
  let addresses = [];

  const existingWallets = await fetchExistingWallet(address.address);

  let kaspaAddresses: AuthAddress[] = [];

  const addressService = new Services.AddressService(
    Enumerations.Blockchains.KASPA,
    'mainnet',
  );

  const walletService = new Services.WalletService(
    Enumerations.Blockchains.KASPA,
    'mainnet',
  );

  const kaspaHDWallet = await walletService.createHDWalletFromMnemonic(
    bitcoinWallet?.mnemonic,
  );

  for (let i = 0; i < 10; i++) {
    const address = await addressService.generateAddressFromHDWalletWithCustomPath(
      kaspaHDWallet,
      `/0/${i}`,
    );

    kaspaAddresses.push(address._data === undefined ? address : address._data);
  }

  console.log('existingWallets', existingWallets);

  const mnemonic = bitcoinWallet.mnemonic;

  for (let i = 0; i < existingWallets.chains.length; i++) {
    const wallet = existingWallets.chains[i];

    const createdWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
      mnemonic,
      parseChains(wallet.chain),
    );

    let index = ChainsOrder.indexOf(parseChains(wallet.chain));

    const address = {
      address: createdWallet.address.includes('bitcoincash:')
        ? createdWallet.address.replace('bitcoincash:', '')
        : createdWallet.address,
      privateKey: createdWallet.privateKey,
      publicKey: createdWallet.publicKey,
      chain: createdWallet.blockchain,
    };

    console.log('address', address);
    console.log('wallet', createdWallet);

    wallets[index] = {
      seed: createdWallet.seed,
      zPub: createdWallet.zPub ? createdWallet.zPub : createdWallet.xPubsList[0],
      mnemonic: createdWallet.mnemonic,
      blockchain: createdWallet.blockchain,
      network: createdWallet.network,
    };
    addresses[index] =
      createdWallet.blockchain !== 'kaspa'
        ? address
        : {
            addresses: kaspaAddresses,
            chain: Enumerations.Blockchains.KASPA,
          };
  }

  return {wallets, addresses};
};

export const stripServicePrefix = (service: string): string => {
  const prefixes = ['assetify-password_', 'assetify-recovery_'];

  for (const prefix of prefixes) {
    if (service.startsWith(prefix)) {
      return service.substring(prefix.length);
    }
  }

  return service;
};
