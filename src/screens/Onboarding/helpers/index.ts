import {FC} from 'react';
import {SvgProps} from 'react-native-svg';

import Onboarding1 from '@assets/images/onboarding-1.svg';
import Onboarding2 from '@assets/images/onboarding-2.svg';
import Logo from '@assets/logo/Logo.svg';

export type OnboardingSlide = {
  id: string;
  title: string;
  description: string;
  image: FC<SvgProps>;
};

export default function seedSuggestions(
  origWord: string,
  wordlist: string[],
  numberOfWords = 6,
): Array<string> {
  const word = origWord.toLowerCase().trim();

  // 1. Words that have same beginning
  let result: Array<string> = wordlist.filter((w) => w.startsWith(word));
  if (result.length === numberOfWords) {
    return result;
  }

  return [...result].slice(0, numberOfWords);
}

/* ============================================================================================== */
/*                                           FIXED DATA                                           */
/* ============================================================================================== */

export const ONBOARDING_CAROUSEL_DATA: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Make your crypto work for you',
    description: 'Leverage the power of your crypto',
    image: Logo,
  },
  {
    id: '2',
    title: 'No Wallet?\nNo Problem!',
    description:
      'Set up your secure crypto wallet in just a few taps—fast, simple, and safe',
    image: Onboarding1,
  },
  {
    id: '3',
    title: 'You Own Your Crypto—Always',
    description:
      'Your private keys remain in your hands, ensuring full control over your assets',
    image: Onboarding2,
  },
];

export const confirmSwitches = [
  {
    checked: false,
    text: 'confirmations.seedPhrase1',
  },
  {
    checked: false,
    text: 'confirmations.seedPhrase2',
  },
  {
    checked: false,
    text: 'confirmations.seedPhrase3',
  },
];
