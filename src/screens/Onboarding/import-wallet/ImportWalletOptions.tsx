import React, {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import {CloudArrowUpIcon, KeyIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {KEYCHAIN_BACKUP_PROVIDER} from '@/screens/KeychainBackup/helpers-keychain';
import {Assetify} from '@assets/index';
import {Caption} from '@styles/styled-components';
import theme from '@/styles/themes';
import {ImportWalletOptionButton} from './components/ImportWalletOptionButton';

const ImportWalletOptions = ({navigation}: {navigation: any}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <Assetify width={theme.layout.images.xs} height={theme.layout.images.xs} />

          <Caption style={styles.title}>{t('wallet.howToImport')}</Caption>
        </View>

        <View style={styles.optionsContainer}>
          <ImportWalletOptionButton
            title={t('wallet.import_wallet')}
            subtitle={t('wallet.importWalletSubtitle')}
            leftIcon={<KeyIcon size={34} stroke={GlobalStyles.gray.gray800} />}
            onPress={() => navigation.navigate('ImportWallet')}
          />

          <ImportWalletOptionButton
            title={t('wallet.restoreWallet')}
            subtitle={t('wallet.restoreWalletSubtitle', {
              opt: KEYCHAIN_BACKUP_PROVIDER,
            })}
            leftIcon={<CloudArrowUpIcon size={34} stroke={GlobalStyles.gray.gray800} />}
            onPress={() => navigation.navigate('RestoreWalletCheck')}
          />
        </View>
      </View>
    </View>
  );
};

export default memo(ImportWalletOptions);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.light.background,
  },
  content: {
    flex: 1,
    paddingVertical: theme.layout.pv.screen,
    paddingHorizontal: theme.layout.ph.screen,
    justifyContent: 'center',
    gap: theme.layout.gap.screen,
  },
  header: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.lg,
  },
  title: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.xxl,
  },
  optionsContainer: {
    flex: 1,
    paddingHorizontal: theme.spacing.sm,
    gap: theme.spacing.md,
  },
});
