import analytics from '@react-native-firebase/analytics';
import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  KeyboardAvoidingView,
  Platform,
  TextInput as RNTextInput,
  StyleSheet,
  View,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import GlobalStyles from '@/constants/GlobalStyles';
import {useFocusInput} from '@/hooks/keyboard';
import {useAppDispatch} from '@/hooks/redux';
import WalletLoader from '@/screens/Onboarding/components/WalletLoader';
import WalletService from '@/services/WalletService';
import {setIsLoggedIn, setUser, setUserAddresses} from '@/storage/actions/authActions';
import {AuthAddress, AuthAddresses} from '@/types/authTypes';
import {enableNotifications} from '@/utils/index';
import MButton from '@components/MButton';
import {BodyM, Caption, Footer} from '@styles/styled-components';
import theme from '@/styles/themes';
import {getBitcoinWallet, getExistingWallets} from '../helpers/import-wallet-helpers';
import InputMnemonic from './components/InputMnemonic';

const ImportWalletScreen = ({navigation}: any) => {
  const inputRef = useRef<RNTextInput>(null);

  const {t} = useTranslation();
  const dispatch = useAppDispatch();

  useFocusInput(inputRef);

  const walletService = new WalletService();

  const [mnemonic, setMnemonic] = useState('');
  const [loading, setLoading] = useState(false);

  const handleMnemonicsSubmit = (text: string) => {
    const mnemonicsArray = text.trim().toLowerCase().split(/\s+/);

    setMnemonic(mnemonicsArray.join(' '));
  };

  const handleMnemonics = useCallback((text: string) => {
    const mnemonicsArray = text.toLowerCase().split(/\s+/);

    if (mnemonicsArray.length > 12) {
      mnemonicsArray.length = 12;
    }

    setMnemonic(mnemonicsArray.join(' '));
  }, []);

  const handleContinue = useCallback(() => {
    navigation.setParams({loading: true});
    setLoading(true);

    setTimeout(async () => {
      const {wallet, address} = await getBitcoinWallet(mnemonic);

      const bitcoinChains = await walletService.walletLoggerBTC(
        address.address,
        wallet.xPubsList?.[0]?.accountXpub ?? '',
      );

      if (bitcoinChains.status === 409) {
        const {wallets, addresses} = await getExistingWallets(wallet, address);

        dispatch(setUserAddresses(addresses));

        dispatch(
          setUser({
            wallet: wallets,
            pinCode: '',
          }),
        );

        // Check if the address is a regular AuthAddress (not a Kaspa address)
        const firstAddress = addresses[0] as AuthAddress | undefined;
        if (firstAddress && 'address' in firstAddress && firstAddress.address) {
          await enableNotifications(firstAddress.address);
        }

        dispatch(setIsLoggedIn());
      } else {
        const createdWallets = await walletService.createWallets(mnemonic);

        const createdAddresses = await walletService.createAddresses(createdWallets!);

        const {wallets, addresses, loggerRequest} =
          await walletService.createLoggerRequest(
            createdWallets!,
            createdAddresses as AuthAddresses,
          );

        await walletService.walletLogger(loggerRequest);

        dispatch(setUserAddresses(addresses));
        dispatch(setUser({wallet: wallets, pinCode: ''}));

        // Check if the address is a regular AuthAddress (not a Kaspa address)
        const firstAddress = addresses[0] as AuthAddress | undefined;
        if (firstAddress && 'address' in firstAddress && firstAddress.address) {
          await enableNotifications(firstAddress.address);
        }

        dispatch(setIsLoggedIn());
      }
    }, 500);
  }, [mnemonic, navigation, dispatch, walletService]);

  useFocusEffect(
    useCallback(() => {
      analytics().logEvent('ImportWallet_screen_open');
      setLoading(false);
      setMnemonic('');
      navigation.setParams({loading: false});
    }, []),
  );

  if (loading) {
    return <WalletLoader isWalletBeingCreated={false} />;
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? (theme.isSmallDevice ? 60 : 90) : 0}
      style={styles.keyboardAvoidingView}
    >
      <KeyboardAwareScrollView
        style={styles.scrollView}
        enableAutomaticScroll={false}
        enableOnAndroid
      >
        <View style={styles.container}>
          <View style={styles.content}>
            <Caption style={styles.caption}>Enter your recovery phrase</Caption>

            <BodyM style={styles.subHeaderText}>
              Your recovery phrase will only be stored locally on your device
            </BodyM>

            <InputMnemonic
              ref={inputRef}
              handleMnemonics={handleMnemonics}
              handleMnemonicsSubmit={handleMnemonicsSubmit}
              mnemonics={mnemonic}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>

      <Footer style={styles.footer}>
        <MButton
          text="Import Wallet"
          onPress={handleContinue}
          disabled={mnemonic.split(' ').length !== 12 || loading}
          isLoading={loading}
        />
      </Footer>
    </KeyboardAvoidingView>
  );
};

export default ImportWalletScreen;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    paddingTop: theme.layout.pv.screen * 2,
    gap: theme.spacing.lg,
  },
  caption: {
    textAlign: 'center',
  },
  subHeaderText: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.xl,
    color: GlobalStyles.gray.gray900,
  },
  footer: {
    paddingHorizontal: theme.isSmallDevice
      ? theme.layout.ph.screen * 3
      : theme.layout.ph.screen * 2,
  },
});
