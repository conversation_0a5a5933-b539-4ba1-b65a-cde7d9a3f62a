import React from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {ChevronRightIcon} from 'react-native-heroicons/outline';

import GlobalStyles from '@/constants/GlobalStyles';
import {BodySSB, Title} from '@styles/styled-components';
import theme from '@/styles/themes';

type Props = {
  title: string;
  leftIcon: React.ReactNode;
  onPress: () => void;
  subtitle?: string;
};

export const ImportWalletOptionButton: React.FC<Props> = ({
  title,
  subtitle,
  leftIcon,
  onPress,
}) => {
  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>{leftIcon}</View>

        <View style={styles.textContainer}>
          <Title>{title}</Title>

          <BodySSB>{subtitle}</BodySSB>
        </View>

        <ChevronRightIcon size={28} stroke={theme.colors.brand.coral} />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: theme.layout.borderRadius.sm,
    borderColor: GlobalStyles.gray.gray500,
    borderWidth: 1,
    // Shadows
    shadowColor: GlobalStyles.base.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
  },
  iconContainer: {
    marginRight: theme.spacing.lg,
    paddingLeft: theme.spacing.md,
  },
  textContainer: {
    flex: 1,
    paddingRight: theme.spacing.lg,
  },
  title: {
    color: GlobalStyles.base.black,
    fontSize: theme.typography.md,
  },
  subtitle: {
    color: GlobalStyles.gray.gray800,
    fontSize: theme.typography.sm,
  },
});
