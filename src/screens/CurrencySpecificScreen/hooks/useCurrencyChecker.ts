import {useQuery} from '@tanstack/react-query';
import {useCallback, useMemo} from 'react';
import RNFS from 'react-native-fs';

import {CACHE_DURATIONS, getCacheFilePath} from '@/screens/Ramps/helpers/cacheUtils';
import {fetchDataAndStore, readCachedData} from '../../Ramps/helpers/currencies';

const useCurrencyData = (type) => {
  return useQuery({
    queryKey: [`cachedCurrencies_${type}`],
    queryFn: async () => {
      const cachedData = await readCachedData(type);
      try {
        const fileInfo = await RNFS.stat(getCacheFilePath(type, 'buy'));
        const cacheTimestamp = new Date(fileInfo.mtime).getTime();
        if (!cachedData || Date.now() - cacheTimestamp > CACHE_DURATIONS.CURRENCIES) {
          return fetchDataAndStore(type);
        }
        return cachedData;
      } catch (e) {
        return fetchDataAndStore(type);
      }
    },
    staleTime: CACHE_DURATIONS.CURRENCIES,
  });
};

const useCurrencyMap = (apiResponse: any[] | null) => {
  return useMemo(() => {
    if (!apiResponse || !Array.isArray(apiResponse)) {
      return new Map<string, any>();
    }

    return new Map(
      apiResponse.map((currency) => {
        if (!currency || typeof currency.ticker !== 'string') {
          return ['', {}];
        }
        return [currency.ticker.toUpperCase(), currency];
      }),
    );
  }, [apiResponse]);
};

const useCurrencyChecker = (type) => {
  const {data: apiResponse, isLoading, error} = useCurrencyData(type);
  const currencyMap = useCurrencyMap(apiResponse as []);

  const isSymbolSupported = useCallback(
    (networkTicket: string, stablecoinTicket: any) => {
      // Convert inputs to uppercase for case-insensitive comparison
      const stablecoinsNetwork = networkTicket?.toLowerCase();

      if (networkTicket === 'trx') {
        networkTicket = 'tron';
      } else if (networkTicket === 'xrp') {
        networkTicket = 'ripple';
      } else if (networkTicket === 'bitcoin-cash') {
        networkTicket = 'bitcoin_cash';
      } else if (networkTicket === 'dogecoin') {
        networkTicket = 'doge';
      } else if (networkTicket === 'binance-smart-chain') {
        networkTicket = 'binance_smart_chain';
      } else if (networkTicket === 'avalanche') {
        networkTicket = 'avaxc';
      }

      if (stablecoinTicket === 'USDT') {
        stablecoinTicket = 'USDT20';
      }

      if (stablecoinTicket) {
        // Search for the stablecoin with the specified network
        return Array.from(currencyMap.entries()).some(([ticker, currency]) => {
          // console.log(ticker, currency);

          return ticker === stablecoinTicket && currency.network === stablecoinsNetwork;
        });
      } else {
        // Search for any currency with the specified network
        return Array.from(currencyMap.values()).some(
          (currency) => currency.network === networkTicket,
        );
      }
    },
    [currencyMap],
  );

  return {
    isSymbolSupported,
    isLoading,
    error,
  };
};

export default useCurrencyChecker;
