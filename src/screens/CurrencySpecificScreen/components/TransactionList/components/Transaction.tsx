import {useIsFocused} from '@react-navigation/native';
import {t} from 'i18next';
import {useEffect, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';

import {PriceFetchingInstance} from '@/services/BackendServices';
import {AuthAddressType, AuthTransaction} from '@/types/authTypes';
import {parsePrice} from '@/utils/parsing';
import ReceiveArrow from '@assets/icons/receiveArrow.svg';
import SendArrow from '@assets/icons/sendArrow.svg';
import {styles} from './styles';
import {amount, limitString, timeStampToDateString} from './transactionComponentUtils';

const handleBCHPrefix = (address: string | undefined) => {
  if (address && address.includes('bitcoincash:')) {
    return address.split('bitcoincash:')[1];
  } else if (address) {
    return address;
  } else {
    return '';
  }
};

const TxIcon = (props: {
  tx: AuthTransaction;
  address: string;
  isSentTransaction: boolean;
}) => {
  if (props.isSentTransaction) {
    return <SendArrow width={40} height={40} />;
  } else {
    return <ReceiveArrow width={40} height={40} />;
  }
};

const TransactionComponent = (props: TransactionComponentPrfdsops) => {
  const tx = props.tx;
  const viewFocused = useIsFocused();

  const userAddress =
    props.address.chain === 'kaspa' //@ts-ignore
      ? props.address.addresses[0].address //@ts-ignore
      : props.address.address;

  const senderAddress =
    tx.tokensTransfered.length > 0
      ? handleBCHPrefix(tx.tokensTransfered[0]?.senderAddress)
      : handleBCHPrefix(tx.senders[0].address);

  const recipientAddress =
    tx.tokensTransfered.length > 0
      ? handleBCHPrefix(tx.tokensTransfered[0]?.recipientAddress)
      : handleBCHPrefix(tx.recipients[0].address);

  const txStatusString =
    tx.status === 'confirmed'
      ? t('currencySpecific.done')
      : t('currencySpecific.canceled');

  const [calculatedPrice, setCalculatedPrice] = useState('0.00');

  const txStatusStyle = StyleSheet.flatten([
    styles.statusContainer,
    tx.status === 'confirmed' ? styles.done : styles.canceled,
  ]);

  const txStatusTextStyle = StyleSheet.flatten([
    styles.status,
    tx.status === 'confirmed' ? styles.doneText : styles.canceledText,
  ]);

  useEffect(() => {
    if (viewFocused) {
      const unit =
        tx.tokensTransfered &&
        tx.tokensTransfered.length > 0 &&
        tx.tokensTransfered[0]?.symbol
          ? tx.tokensTransfered[0].symbol
          : tx.fee?.unit || '';

      PriceFetchingInstance.get(
        `/price/${unit}?currency=${props.currency === 'EUR' ? 'EUR' : 'USDT'}`,
      )
        .then((res) => res.data)
        .then((res) => {
          setCalculatedPrice(
            (parseFloat(res.price.price) * parseFloat(amount(tx))).toFixed(2),
          );
        })
        .catch((err) => {
          console.log('err', err);
        });
    }
  }, [viewFocused]);

  const isSentTransaction = tx.sentOrReceived === 'sent';

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => props.onPress && props.onPress(tx)}
    >
      <View style={styles.topContainer}>
        <View style={styles.leftContainer}>
          <View style={styles.tokenLogoContainer}>
            <TxIcon tx={tx} address={userAddress} isSentTransaction={isSentTransaction} />
          </View>
          <View style={styles.tokenInfoContainer}>
            <Text style={styles.tokenName}>
              {isSentTransaction
                ? t('currencySpecific.sent')
                : t('currencySpecific.received')}
            </Text>
            <Text style={styles.otherParty}>
              {isSentTransaction
                ? limitString(recipientAddress || '', 10)
                : limitString(senderAddress || '', 10)}
            </Text>
          </View>
        </View>
        <View style={styles.rightContainer}>
          <Text style={isSentTransaction ? styles.tokenPriceDown : styles.tokenPriceUp}>
            {isSentTransaction ? '-' : '+'}
            {parsePrice(amount(tx))}{' '}
            {tx.tokensTransfered &&
            tx.tokensTransfered.length > 0 &&
            tx.tokensTransfered[0]?.symbol
              ? tx.tokensTransfered[0].symbol
              : tx.fee?.unit || ''}
          </Text>
          <Text style={styles.calculatedPrice}>
            {props.currency === 'EUR' ? '€' : '$'}
            {parsePrice(calculatedPrice)}
          </Text>
        </View>
      </View>
      <View style={styles.line} />
      <View style={styles.bottomContainer}>
        <Text style={styles.date}>{timeStampToDateString(tx.timestamp)}</Text>
        <View style={txStatusStyle}>
          <Text style={txStatusTextStyle}>{txStatusString}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const MapStateToProps = (state: any) => {
  return {
    currency: state.common.currency,
  };
};

export default connect(MapStateToProps)(TransactionComponent);
