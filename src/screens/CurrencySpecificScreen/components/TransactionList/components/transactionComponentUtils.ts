import {AuthTransaction} from '@/types/authTypes';
import {TrailingZeroRegex} from '../../../../../utils/parsing';

export const limitString = (str: string, limit: number) => {
  if (str.length > limit) {
    return str.slice(0, 4) + '...' + str.slice(-4);
  } else {
    return str;
  }
};

export const timeStampToDateString = (timeStamp: number) => {
  // data format: <Month> <Day>, <Year>
  const date = new Date(timeStamp);

  const month = date.toLocaleString('default', {month: 'short'});

  const day = date.getDate();

  const year = date.getFullYear();

  return `${month} ${day}, ${year}`;
};

export const amount = (tx: AuthTransaction) => {
  if (
    tx.tokensTransfered &&
    tx.tokensTransfered.length > 0 &&
    tx.tokensTransfered[0]?.confirmedBalance
  ) {
    // Convert scientific notation to fixed notation
    const value = Number(tx.tokensTransfered[0].confirmedBalance).toFixed(12);
    return value.replace(TrailingZeroRegex, '');
  }

  if (tx.recipients && tx.recipients.length > 0) {
    if (tx.recipients[0]?.amount == tx.fee?.amount) return '0.00';
    else if (tx.recipients[0]?.amount) {
      // Convert scientific notation to fixed notation
      const value = Number(tx.recipients[0].amount).toFixed(12);
      return value.replace(TrailingZeroRegex, '');
    }
  }

  return '0.00';
};
