import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    paddingHorizontal: 12,
    width: '100%',
    paddingVertical: 10,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginVertical: 4,
  },
  topContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  tokenLogoContainer: {
    width: 40,
    height: 40,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  tokenLogo: {
    width: 40,
    height: 40,
  },
  tokenInfoContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginLeft: 6,
  },
  tokenName: {
    fontSize: 13,
    lineHeight: 24,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  rightContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  tokenPriceUp: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.success.success700,
    fontStyle: 'normal',
  },
  tokenPriceDown: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.error.error500,
    fontStyle: 'normal',
  },
  calculatedPrice: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray900,
    fontStyle: 'normal',
  },
  otherParty: {
    fontSize: 14,
    lineHeight: 19,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray800,
    fontStyle: 'normal',
  },
  line: {
    height: 1,
    width: '85%',
    backgroundColor: GlobalStyles.gray.gray600,
    marginVertical: 10,
    alignSelf: 'flex-end',
  },
  bottomContainer: {
    display: 'flex',
    flexDirection: 'row',
    width: '85%',
    alignSelf: 'flex-end',
  },
  date: {
    fontSize: 14,
    lineHeight: 19,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.gray.gray800,
    fontStyle: 'normal',
  },
  statusContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 'auto',
    borderRadius: 200,
    width: 60,
    paddingVertical: 4,
  },
  status: {
    fontSize: 10,
    lineHeight: 12,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontStyle: 'normal',
  },
  done: {
    backgroundColor: GlobalStyles.success.success100,
  },
  doneText: {
    color: GlobalStyles.success.success900,
  },
  pending: {
    backgroundColor: GlobalStyles.orange.orange100,
  },
  pendingText: {
    color: GlobalStyles.orange.orange900,
  },
  canceled: {
    backgroundColor: GlobalStyles.error.error100,
  },
  canceledText: {
    color: GlobalStyles.error.error900,
  },
});
