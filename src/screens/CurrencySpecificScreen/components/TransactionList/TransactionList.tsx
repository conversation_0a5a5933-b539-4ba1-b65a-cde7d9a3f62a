import {View, Text} from 'react-native';
import {styles} from './styles';
import TransactionComponent from './components/Transaction';
import {useTranslation} from 'react-i18next';
import EmptyBitcoin from '../../../../assets/icons/emptyBitcoin.svg';

const EmptyBitcoinSvg = () => <EmptyBitcoin width={40} height={40} />;

type TransactionListProps = {
  txs: AuthTransaction[];
  handleTxPress: (tx: AuthTransaction) => void;
  address: AuthAddressType;
  stableCoin: string;
};

const TransactionList = ({
  txs,
  handleTxPress,
  address,
  stableCoin,
}: TransactionListProps) => {
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <Text style={styles.titleText}>{t('currencySpecific.transactions')}</Text>
      </View>
      <View style={styles.listContainer}>
        {txs && txs?.length !== 0 ? (
          txs.map((item, index) => {
            return (
              <TransactionComponent
                key={index}
                tx={item}
                onPress={() => handleTxPress(item)}
                address={address}
                stableCoin={stableCoin}
              />
            );
          })
        ) : (
          <View style={styles.noAssetContainer}>
            <View style={styles.topContainer}>
              <View style={styles.tokenLogoContainer}>
                <EmptyBitcoinSvg />
              </View>
              <View style={styles.tokenInfoContainer}>
                <Text style={styles.tokenName}>
                  {t('currencySpecific.noTransactions')}
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
};

export default TransactionList;
