import {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {AuthAsset} from '@/types/authTypes';
import {parsePrice, symbolToSvg} from '@utils/parsing';
import Logo from '../../../components/Logo/AssetLogo';

type AssetProps = {
  asset: AuthAsset;
  stableCoin: string;
  stableCoinAmount: string;
  calculatedPrices: string;
  tokenPrices: string;
  currency: string;
};

export const Asset = ({
  asset,
  stableCoin,
  stableCoinAmount,
  calculatedPrices,
  tokenPrices,
  currency,
}: AssetProps) => {
  const {t} = useTranslation();

  const tokenSymbol = stableCoin
    ? stableCoin
    : asset.tokenSymbol === 'BSC'
    ? 'BNB'
    : asset.tokenSymbol;

  const logoName = stableCoin
    ? symbolToSvg(`${stableCoin}${asset.tokenSymbol}`)
    : symbolToSvg(asset.tokenSymbol === 'BSC' ? 'BNB' : asset.tokenSymbol);

  const currencySymbol = currency === 'EUR' ? '€' : '$';

  // Ensure we have valid values with fallbacks
  const safeTokenPrice = tokenPrices || '0.00';
  const safeCalculatedPrice = calculatedPrices || '0.00';

  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <Logo name={logoName} />
      </View>

      <View style={styles.infoContainer}>
        <Text style={styles.currencyText}>1 {tokenSymbol}</Text>

        <Text style={styles.priceText}>
          = {currencySymbol}
          {parsePrice(safeTokenPrice)}
        </Text>
      </View>

      <View style={styles.divider} />

      <View style={styles.topLine}>
        <Text style={styles.youHaveText}>{t('currencySpecific.youHave')}</Text>

        <Text style={styles.balanceText} numberOfLines={1}>
          {parsePrice(stableCoin ? stableCoinAmount || '0.00' : asset.amount || '0.00')}{' '}
          {tokenSymbol} | {currencySymbol}
          {parsePrice(safeCalculatedPrice)}
        </Text>
      </View>
    </View>
  );
};

export default memo(Asset);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 6,
    marginTop: 24,
    width: '100%',
  },
  logoContainer: {
    width: 84,
    height: 84,
    borderRadius: 42,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -40,
    marginBottom: 16,
    backgroundColor: GlobalStyles.base.white,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '90%',
    paddingHorizontal: 16,
  },
  currencyText: {
    fontSize: 17,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  priceText: {
    fontSize: 14,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  divider: {
    width: '90%',
    height: 1,
    backgroundColor: GlobalStyles.gray.gray600,
    marginVertical: 16,
  },
  topLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '90%',
    paddingHorizontal: 16,
    backgroundColor: 'transparent',
  },
  youHaveText: {
    fontSize: 17,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  balanceText: {
    fontSize: 14,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
});
