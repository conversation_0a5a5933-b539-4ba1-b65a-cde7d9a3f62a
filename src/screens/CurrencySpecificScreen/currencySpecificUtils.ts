import {Dimensions} from 'react-native';
import {ChainToSymbol, ChainsOrder, SymbolToChain} from '../../constants/Chains';
import GlobalStyles from '@/constants/GlobalStyles';

export const debugging = `const consoleLog = (type, log) => window.ReactNativeWebView.postMessage(JSON.stringify({'type': 'Console', 'data': {'type': type, 'log': log}}));
console = {
    log: (log) => consoleLog('log', log),
    debug: (log) => consoleLog('debug', log),
    info: (log) => consoleLog('info', log),
    warn: (log) => consoleLog('warn', log),
    error: (log) => consoleLog('error', log),
  };

  console.log('Hello World');`;

export const onMessage = (payload: any) => {
  let dataPayload;

  try {
    dataPayload = JSON.parse(payload.nativeEvent.data);
  } catch (e) {
    console.log(e);
  }

  if (dataPayload.type === 'Console') {
    console.log(dataPayload.data);
  }
};

export let simpleSwapHTML = `<html><head><style>.scaled {transform: scale(0.9); margin-left: -5px; margin-top: -40px;}</style></head><body width="${
  Dimensions.get('screen').width
}" height="${
  Dimensions.get('screen').height
}"><iframe class="scaled" id="simpleswap-frame" name="SimpleSwap Widget" width="${
  Dimensions.get('screen').width
}" height="${
  Dimensions.get('screen').height - 200
}" src="https://simpleswap.io/widget/00b7871d-6d38-49a8-bfee-0a455fa46c19" frameborder="0" ></iframe></body></html>`;

export const findWalletIndex = (symbol: string) => {
  return ChainsOrder.findIndex((chain) => chain === SymbolToChain[symbol.toLowerCase()]);
};

export const findChainIndex = (symbol: string) => {
  return ChainsOrder.findIndex(
    (chain) => chain === SymbolToChain[ChainToSymbol[symbol.toLowerCase()]],
  );
};

export const getChainLinks = (chain: string) => {
  if (chain === 'bitcoin') {
    return {
      explorer: 'https://blockchair.com/bitcoin',
      whitepaper: 'https://bitcoin.org/bitcoin.pdf',
    };
  } else if (chain === 'ethereum') {
    return {
      explorer: 'https://blockchair.com/ethereum',
      whitepaper: 'https://ethereum.org/en/whitepaper/',
    };
  } else if (chain === 'solana') {
    return {
      explorer: 'https://blockchair.com/solana',
      whitepaper: 'https://solana.com/solana-whitepaper.pdf',
    };
  } else if (chain === 'trx') {
    return {
      explorer: 'https://blockchair.com/tron',
      whitepaper: 'https://whitepaper.io/document/4/tron-whitepaper',
    };
  } else if (chain === 'avalanche') {
    return {
      explorer: 'https://blockchair.com/avalanche',
      whitepaper: 'https://www.avalabs.org/whitepapers',
    };
  } else if (chain === 'dogecoin') {
    return {
      explorer: 'https://blockchair.com/dogecoin',
      whitepaper: 'https://dogechain.dog/DogechainWP.pdf',
    };
  } else if (chain === 'xrp') {
    return {
      explorer: 'https://blockchair.com/xrp-ledger',
      whitepaper: 'https://ripple.com/files/ripple_consensus_whitepaper.pdf',
    };
  } else if (chain === 'kaspa') {
    return {
      explorer: 'https://explorer.kaspa.org/',
      whitepaper: 'https://kaspa.org/',
    };
  } else if (chain === 'binance-smart-chain') {
    return {
      explorer: 'https://bscscan.com/',
      whitepaper: 'https://docs.bnbchain.org/',
    };
  } else if (chain === 'litecoin') {
    return {
      explorer: 'https://blockchair.com/litecoin',
      whitepaper: 'https://api-new.whitepaper.io/documents/pdf?id=SkGJbIbEO',
    };
  } else if (chain === 'bitcoin-cash') {
    return {
      explorer: 'https://blockchair.com/bitcoin-cash',
      whitepaper: 'https://bitcoincash.org/',
    };
  } else if (chain === 'solana') {
    return {
      explorer: 'https://blockchair.com/solana',
      whitepaper: 'https://solana.com/whitepaper.pdf',
    };
  } else {
    return {
      explorer: '',
      whitepaper: '',
    };
  }
};

/**
 * Returns the gradient view color based on the selected asset.
 */
export const getAssetColor = (symbol: string) => {
  const colors = {
    BTC: '#f9c795',
    ETH: '#aab4e0',
    SOL: '#9945FF',
    BNB: '#e8ca78',
    TRX: '#f6cacb',
    XRP: '#adacac',
    AVAX: '#f6cacb',
    KAS: '#b0e9df',
    BCH: '#8cc1b0',
    LTC: '#8ba5d0',
    DOGE: '#d8c997',
    SOL: '#9945FF',
    // Stables
    USDT: '#36A07D',
    USDC: '#3F71AE',
    TUSD: '#203F6F',
    DAI: '#F6B75F',
    // Solana Stables
    USDS: '#F7931A',
    JUP: '#34AADC',
    RAY: '#2669F5',
  };

  // Return the color for the symbol, or a default color if not found
  return [colors[symbol] || '#8ba5d0', GlobalStyles.gray.gray500];
};
