import {useIsFocused, useNavigation, useRoute} from '@react-navigation/native';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Dimensions,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import WebView from 'react-native-webview';

import {ChainToSymbol} from '@/constants/Chains';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAuth, useCommon} from '@/hooks/redux';
import {navigate} from '@/navigation/utils/navigation';
import {WalletLoggerInstance} from '@/services/BackendServices';
import {setWalletTxs} from '@/storage/actions/authActions';
import {AuthAddressType, AuthAsset, AuthTransaction} from '@/types/authTypes';
import {capitalize} from '@/utils';
import ExplorerSvg from '@assets/icons/explorer.svg';
import WhitePaper from '@assets/icons/whitepaper.svg';
import {GradientView} from '@components/GradientView';
import MButton from '@components/MButton';
import SendReceiveButtons from '@components/SendReceiveButtons/SendReceiveButtons';
import TopNavigation from '@components/TopNavigation';
import {openLink} from '@utils/in-app-browser';
import Asset from './components/Asset';
import TransactionList from './components/TransactionList/TransactionList';
import {
  debugging,
  findWalletIndex,
  getAssetColor,
  getChainLinks,
  onMessage,
  simpleSwapHTML,
} from './currencySpecificUtils';
import {useCombinedCurrencyChecker} from './hooks/useCombined';
import {styles} from './styles';

type CurrencySpecificParams = {
  asset: AuthAsset;
  stableCoin: string;
  stableCoinIndex: number;
  stableCoinAmount: string;
  address: AuthAddressType;
  calculatedPrices: string[];
  tokenPrices: string[];
};

const CurrencySpecificScreen: React.FC = () => {
  const {t} = useTranslation();
  const navigation = useNavigation();
  const viewFocused = useIsFocused();
  const dispatch = useAppDispatch();

  const route = useRoute() as {params: CurrencySpecificParams};
  const {
    asset,
    stableCoin,
    stableCoinIndex,
    stableCoinAmount,
    address: paramAddress,
    calculatedPrices,
    tokenPrices,
  } = route.params;

  const {walletTxs, userAddresses} = useAuth();
  const {currency} = useCommon();

  const {isSupported} = useCombinedCurrencyChecker(
    asset?.blockchain,
    stableCoin.toUpperCase(),
  );

  const [refreshing, setRefreshing] = useState(false);
  const [txs, setTxs] = useState<AuthTransaction[]>([]);
  const [simpleswap, setSimpleswap] = useState(false); // DEV
  const [isExpanded, setIsExpanded] = useState(false);

  const handleBuyPress = useCallback(() => {
    navigate('RampDetails', {
      currency: stableCoin ? stableCoin : asset?.title,
      currencySymbol: stableCoin ? stableCoin : asset?.tokenSymbol,
      tokenPrice: stableCoin ? tokenPrices[stableCoinIndex] : tokenPrices[0],
      addressToUse: userAddresses[findWalletIndex(asset?.tokenSymbol)]?.address,
      blockchainSymbol: stableCoin ? asset?.tokenSymbol : null,
    });
  }, [asset, stableCoin, stableCoinIndex, tokenPrices, userAddresses]);

  const address =
    paramAddress?.chain === 'kaspa' ? paramAddress?.addresses[0] : paramAddress;

  const handleSendPress = useCallback(() => {
    navigate('SendAssetInput', {
      asset: asset,
      address: address,
      tokenPrices: tokenPrices,
      calculatedPrices: calculatedPrices,
      stableCoin: stableCoin,
      stableCoinIndex: stableCoinIndex,
      stableCoinAmount: stableCoinAmount,
      stableCoins: null,
      assets: null,
      balance: stableCoin != '' ? stableCoinAmount : asset?.amount,
    });
  }, [
    asset,
    address,
    calculatedPrices,
    stableCoin,
    stableCoinAmount,
    stableCoinIndex,
    tokenPrices,
  ]);

  const handleReceivePress = useCallback(() => {
    navigate('ReceiveAsset', {
      asset: asset,
      address: address,
      tokenPrices: tokenPrices,
      calculatedPrices: calculatedPrices,
      stableCoin: stableCoin,
      stableCoinIndex: stableCoinIndex,
      stableCoinAmount: stableCoinAmount,
    });
  }, [
    asset,
    address,
    calculatedPrices,
    stableCoin,
    stableCoinAmount,
    stableCoinIndex,
    tokenPrices,
  ]);

  const handleTxPress = useCallback(
    (tx: AuthTransaction) => {
      navigate('TxDetails', {
        tx: tx,
        asset: asset,
        address: paramAddress,
        tokenPrices: tokenPrices,
        calculatedPrices: calculatedPrices,
        stableCoin: stableCoin,
        stableCoinIndex: stableCoinIndex,
        stableCoinAmount: stableCoinAmount,
      });
    },
    [
      asset,
      address,
      calculatedPrices,
      stableCoin,
      stableCoinAmount,
      stableCoinIndex,
      tokenPrices,
      paramAddress,
    ],
  );

  const refreshData = useCallback(async () => {
    const fixedBlockchains =
      asset?.blockchain.toLowerCase() === 'trx'
        ? 'tron'
        : asset?.blockchain.toLowerCase();

    const addressToUse =
      paramAddress?.chain === 'kaspa'
        ? paramAddress?.addresses[0]
        : typeof address === 'object'
        ? address.address
        : address;

    WalletLoggerInstance.get(
      `/v1/chain/${fixedBlockchains}/${asset?.network}/${addressToUse}/transactions`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )
      .then((response) => response.data)
      .then((data) => {
        // const filteredTxs = stableCoin != '' ? data.transactions.filter((tx: ITransaction) => tx.tokensTransfered.length > 0) : data.transactions.filter((tx: ITransaction) => tx.tokensTransfered.length == 0);
        const filteredTxs =
          stableCoin != ''
            ? data.transactions.filter(
                (tx: AuthTransaction) => tx.tokensTransfered[0]?.symbol === stableCoin,
              )
            : data.transactions;
        const filterApproveTxs = filteredTxs.filter((tx: AuthTransaction) =>
          tx.senders[0].amount === tx.fee.amount ? tx.tokensTransfered.length > 0 : true,
        );
        setTxs(filterApproveTxs);
        if (stableCoin != '') {
          let newWalletTxs = {...walletTxs};
          newWalletTxs[stableCoin + asset?.blockchain] = filterApproveTxs;
          dispatch(setWalletTxs(newWalletTxs));
        } else {
          let newWalletTxs = {...walletTxs};
          newWalletTxs[asset?.blockchain] = filterApproveTxs;
          dispatch(setWalletTxs(newWalletTxs));
        }
        // console.log('path', `${WALLET_LOGGER_SERVICE}/v1/chain/${fixedBlockchains}/${asset?.network}/${address.address}/transactions`)
        // console.log('Success:', data);
        setRefreshing(false);
      })
      .catch((error) => {
        console.error('Error on refreshData:', error);
      });
  }, [
    asset,
    address,
    stableCoin,
    stableCoinIndex,
    stableCoinAmount,
    walletTxs,
    dispatch,
  ]);

  const onRefresh = useCallback(async () => {
    // check if asset?.blockchain is in evm chains
    let chain = ChainToSymbol[asset?.blockchain.toLowerCase()];

    if (chain === 'avax') {
      chain = 'avalanche';
    }

    setRefreshing(true);
    WalletLoggerInstance.get(
      // @ts-ignore
      `v1/wallet/${userAddresses[0].address}/refresh?chain=${chain.toLowerCase()}`,
    ).then(() => {
      refreshData();
    });
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, [userAddresses, asset]);

  useEffect(() => {
    if (!viewFocused) setIsExpanded(false);

    setSimpleswap(false);
    if (stableCoin === '' && walletTxs && walletTxs[asset?.blockchain]) {
      setTxs(walletTxs[asset?.blockchain]);
    } else if (
      stableCoin !== '' &&
      walletTxs &&
      walletTxs[stableCoin + asset?.blockchain]
    ) {
      setTxs(walletTxs[stableCoin + asset?.blockchain]);
    } else {
      setTxs([]);
    }

    refreshData();
  }, [viewFocused]);

  const handleReadMore = () => {
    setIsExpanded(!isExpanded);
  };

  const tokenSymbol = stableCoin
    ? stableCoin
    : asset.tokenSymbol === 'BSC'
    ? 'BNB'
    : asset.tokenSymbol;
  const gradientColorStyle = getAssetColor(tokenSymbol);

  const capitalizedAssetName = capitalize(asset?.title);
  const handleOpenLink = async (url: string) => await openLink(url);
  const {explorer, whitepaper} = getChainLinks(asset?.title);

  return (
    <>
      {simpleswap && debugging ? (
        <View
          style={{
            flex: 1,
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <WebView
            ref={
              // @ts-ignore
              (ref) => (this.webview = ref)
            }
            source={{
              html: simpleSwapHTML,
            }}
            javaScriptEnabled={true}
            onMessage={onMessage}
            injectedJavaScript={`const meta = document.createElement('meta'); meta.setAttribute('content', 'width=width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'); meta.setAttribute('name', 'viewport'); document.getElementsByTagName('head')[0].appendChild(meta); `}
            style={{
              flex: 1,
              width: Dimensions.get('screen').width,
              height: Dimensions.get('screen').height,
              resizeMode: 'cover',
            }}
            scalesPageToFit={true}
            onLoad={() => {
              // @ts-ignore
              this.webview.injectJavaScript(debugging);
            }}
          />
        </View>
      ) : (
        <View style={{flex: 1, backgroundColor: GlobalStyles.gray.gray500}}>
          <GradientView colors={gradientColorStyle}>
            <TopNavigation
              screenTitle={`${capitalizedAssetName} (${
                stableCoin != '' ? stableCoin : asset?.tokenSymbol
              })`}
              leftIcon={true}
              leftIconAction={() => navigation.goBack()}
              titleContainerStyle={{
                marginLeft: 16,
              }}
            />

            <ScrollView
              style={{flex: 1}}
              contentContainerStyle={{flexGrow: 1}}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  tintColor={GlobalStyles.base.black}
                  progressBackgroundColor={gradientColorStyle[0]}
                />
              }
            >
              <View style={{marginTop: 16}}>
                <Asset
                  asset={asset}
                  stableCoin={stableCoin}
                  stableCoinAmount={stableCoinAmount}
                  calculatedPrices={
                    Array.isArray(calculatedPrices) &&
                    calculatedPrices.length > stableCoinIndex
                      ? calculatedPrices[stableCoinIndex]
                      : '0.00'
                  }
                  tokenPrices={
                    Array.isArray(tokenPrices) && tokenPrices.length > stableCoinIndex
                      ? tokenPrices[stableCoinIndex]
                      : '0.00'
                  }
                  currency={currency}
                />
              </View>

              <View style={styles.mainContainer}>
                <View style={styles.chainInfoContainer}>
                  <Text style={styles.discTitleText}>
                    {t('about')} {capitalizedAssetName}
                  </Text>

                  <Text
                    style={styles.discText}
                    numberOfLines={isExpanded ? undefined : 2}
                  >
                    {t(`currencySpecific.${asset?.title}Disc`)}
                  </Text>

                  <TouchableOpacity onPress={handleReadMore}>
                    <Text style={styles.readMoreText}>
                      {isExpanded ? 'Read Less' : 'Read More'}
                    </Text>
                  </TouchableOpacity>

                  <View style={styles.infoButtonsContainer}>
                    <TouchableOpacity
                      style={styles.infoButton}
                      onPress={() => handleOpenLink(explorer)}
                    >
                      <ExplorerSvg width="20" height="20" />
                      <Text style={styles.infoButtonText}>Explorer</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={styles.infoButton}
                      onPress={() => handleOpenLink(whitepaper)}
                    >
                      <WhitePaper width="20" height="20" />
                      <Text style={styles.infoButtonText}>Whitepaper</Text>
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.separationLine} />

                {isSupported ? (
                  <SendReceiveButtons
                    handleSendPress={handleSendPress}
                    handleReceivePress={handleReceivePress}
                    handleBuyPress={handleBuyPress}
                    buyButtonTitle="Buy / Sell"
                  />
                ) : (
                  <SendReceiveButtons
                    handleSendPress={handleSendPress}
                    handleReceivePress={handleReceivePress}
                  />
                )}

                <View style={styles.txsContainer}>
                  <TransactionList
                    txs={txs}
                    handleTxPress={handleTxPress}
                    address={paramAddress}
                    stableCoin={stableCoin}
                  />
                </View>
              </View>
            </ScrollView>
          </GradientView>
        </View>
      )}

      {simpleswap && (
        <View style={styles.bottomContainer}>
          <MButton
            text={`Close SimpleSwap`}
            onPress={() => {
              setSimpleswap(!simpleswap);
            }}
          />
        </View>
      )}
    </>
  );
};

export default memo(CurrencySpecificScreen);
