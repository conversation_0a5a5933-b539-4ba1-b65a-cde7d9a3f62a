import {Dimensions, StyleSheet} from 'react-native';
import GlobalStyles from '../../constants/GlobalStyles';
import {getFontSize, getScreenAlignment} from '../../utils/parsing';

const {width, height} = Dimensions.get('window');

export const styles = StyleSheet.create({
  mainContainer: {
    width: width * 0.9,
    alignSelf: 'center',
    marginTop: 25,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
  },
  separationLine: {
    borderColor: GlobalStyles.gray.gray600,
    borderTopWidth: 4,
    borderRadius: 8,
    justifyContent: 'center',
    alignSelf: 'center',
    width: '100%',
    marginTop: 30,
    marginBottom: 11,
  },
  chainInfoContainer: {
    width: '100%',
    alignSelf: 'center',
    justifyContent: 'center',
    marginTop: +getScreenAlignment(height, '10', '6'),
    marginLeft: 9,
  },
  discTitleText: {
    color: GlobalStyles.primary.primary800,
    fontWeight: '600',
    marginBottom: 5,
    fontSize: getFontSize(20),
    marginTop: 10,
  },
  discText: {
    color: GlobalStyles.gray.gray900,
    marginBottom: 7,
    paddingRight: 11,
    fontSize: getFontSize(18),
  },
  readMoreText: {
    color: GlobalStyles.primary.primary800,
    fontWeight: 'bold',
    fontSize: getFontSize(16),
  },
  infoButtonsContainer: {
    flexDirection: 'row',
    marginTop: 15,
  },
  infoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: GlobalStyles.gray.gray600,
    borderRadius: 20,
    marginRight: 16,
  },
  infoButtonText: {
    marginLeft: 5,
    fontWeight: '500',
    color: GlobalStyles.primary.primary800,
    fontSize: getFontSize(15),
  },
  txsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    display: 'flex',
    alignSelf: 'center',
    width: '100%',
    marginBottom: 90,
  },
  icon: {
    alignSelf: 'center',
    justifyContent: 'center',
    marginRight: 5,
  },
  container: {
    paddingTop: 45,
    paddingStart: 25,
    paddingEnd: 25,
    alignItems: 'stretch',
    justifyContent: 'center',
  },
  switchContainer: {
    flexDirection: 'row',
    paddingTop: 15,
    paddingBottom: 15,
    justifyContent: 'space-between',
  },
  textContainer: {
    paddingTop: 15,
    paddingBottom: 15,
  },
});
