import {QR_CODE_BACKEND_SERVICE} from '@env';
import {useQuery} from '@tanstack/react-query';
import React, {memo, useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {useAuth} from '@/hooks/redux';
import {ReceiveAssetParams} from '@/navigation/types';
import {AuthAddress, AuthAsset} from '@/types/authTypes';
import {handleCopy} from '@/utils/index';
import CopySvg from '@assets/icons/copy.svg';
import ProfitSvg from '@assets/icons/profit.svg';
import {BaseOption, CurrencyOption} from '@components/BottomSheetList';
import LoadingHandler from '@components/LoadingHandler';
import Logo from '@components/Logo/AssetLogo';
import {capitalize, handleSharePress} from '@utils/index';
import {symbolToSvg} from '@utils/parsing';
import {findWalletIndex} from '../currencySpecificUtils';
import {fetcher} from './receiveAssetUtils';

const QR_CODE_URL = `${QR_CODE_BACKEND_SERVICE}/qr`;

const ReceiveAsset: React.FC<ReceiveAssetParams> = ({route}: any) => {
  const {t} = useTranslation();
  const {userAddresses} = useAuth();
  const {open: openSheet, close: closeSheet} = useBottomSheet('list');

  const [asset, setAsset] = useState<AuthAsset | null>(null);
  const [assets, setAssets] = useState<AuthAsset[] | null>(null);
  const [address, setAddress] = useState<AuthAddress | null>(null);

  const {
    data: qrCode,
    isLoading: isQrCodeLoading,
    error: qrCodeError,
    refetch: refetchQrCode,
  } = useQuery({
    queryKey: ['qrCode', address ? address.address : null],
    queryFn: () =>
      fetcher(
        typeof address === 'string'
          ? [QR_CODE_URL, JSON.stringify({data: address})]
          : [QR_CODE_URL, JSON.stringify({data: address.address})],
      ),
    enabled: !!address,
  });

  const handlePickAsset = useCallback(
    (value: string) => {
      if (!assets) return;

      const index = assets.findIndex((asset) => asset.tokenSymbol === value);

      setAsset(assets[index]);

      const chain =
        assets[index]?.tokenSymbol === 'BNB' ? 'BSC' : assets[index]?.tokenSymbol;

      setAddress(
        userAddresses[findWalletIndex(chain)]?.chain === 'kaspa'
          ? userAddresses[findWalletIndex(chain)]?.addresses[0]
          : userAddresses[findWalletIndex(chain)],
      );
    },
    [assets, userAddresses],
  );

  const handleOpenBottomSheet = useCallback(() => {
    if (!assets) return;

    openSheet(
      {
        type: 'currency',
        data: assetToOptions(assets),
        onSelect: (item: BaseOption | CurrencyOption) => {
          handlePickAsset(item.value);
          closeSheet();
        },
      },
      80,
      true,
    );
  }, [assets, openSheet, handlePickAsset, closeSheet]);

  const assetLogo = useCallback(
    (asset: AuthAsset | null) => {
      if (!asset) {
        return (
          <Text style={styles.selectTitle}>{t('stakingCalculator.selectAsset')}</Text>
        );
      }

      let symbol = `${asset.tokenSymbol.toString()}Svg`.toUpperCase();

      if (symbol == 'ETHEREUMSVG') {
        symbol = 'ETHSVG';
      } else if (symbol == 'BITCOINSVG') {
        symbol = 'BTCSVG';
      }

      return (
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'flex-start',
            marginRight: 10,
            height: 30,
            paddingVertical: 4,
            zIndex: 2,
          }}
        >
          <Logo name={symbol} />
        </View>
      );
    },
    [t],
  );

  const assetComponents = useCallback(
    (asset: AuthAsset | null) => {
      if (!asset) {
        return (
          <Text style={styles.selectTitle}>{t('stakingCalculator.selectAsset')}</Text>
        );
      }

      return (
        <View style={styles.optionContainer}>
          {assetLogo(asset)}
          <Text style={styles.optionTitle}>{asset.tokenSymbol}</Text>
        </View>
      );
    },
    [t, assetLogo],
  );

  const assetToOptions = useCallback(
    (assets: AuthAsset[]): CurrencyOption[] => {
      return assets.map((asset) => ({
        label: asset.tokenSymbol,
        value: asset.tokenSymbol,
        fullName: capitalize(asset.title),
        type: 'crypto',
      }));
    },
    [t],
  );

  useEffect(() => {
    setAsset(route.params.asset ? route.params.asset : route.params.assets![0]);
    setAssets(route.params.assets ? route.params.assets : null);
    setAddress(route.params.address ? route.params.address : userAddresses[0]);

    refetchQrCode();
  }, [route.params]);

  if (
    qrCodeError ||
    isQrCodeLoading ||
    asset === null ||
    address === null ||
    address.address === ''
  ) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <LoadingHandler />
      </View>
    );
  }

  const symbol = asset
    ? symbolToSvg(asset.tokenSymbol === 'BNB' ? 'BSC' : asset.tokenSymbol)
    : '';

  return (
    <>
      <View style={GlobalStyles.THEME.mainContainer}>
        <View style={styles.container}>
          <View style={styles.logoContainer}>
            <Logo name={symbol} />
          </View>

          <View style={styles.qrContainer}>
            <Image
              style={styles.qr}
              source={{
                uri: `${address.qrCode ? address.qrCode : qrCode.data}`,
              }}
            />
          </View>

          <Text style={styles.address}>
            {typeof address === 'string' ? address : address.address}
          </Text>

          <View style={styles.miscellaneousContainer}>
            <TouchableOpacity
              style={styles.copyContainer}
              onPress={() =>
                handleCopy(typeof address === 'string' ? address : address.address)
              }
            >
              <CopySvg />

              <Text style={styles.copyText}>{t('copy')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.shareContainer}
              onPress={() =>
                handleSharePress(typeof address === 'string' ? address : address.address)
              }
            >
              <ProfitSvg />

              <Text style={styles.shareText}>{t('share')}</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.assetContainer}>
            {assets && (
              <View style={styles.assetContainer}>
                <TouchableOpacity
                  style={{
                    ...styles.assetsDropdownHeader,
                    alignItems: 'center',
                    paddingRight: 20,
                    width: '40%',
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderRadius: 8,
                    borderColor: GlobalStyles.gray.gray600,
                  }}
                  onPress={handleOpenBottomSheet}
                >
                  <View style={styles.assetDropdownHeaderTitleContainer}>
                    {assetComponents(asset)}
                  </View>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </View>
    </>
  );
};

export default memo(ReceiveAsset);

export const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    padding: 6,
    display: 'flex',
    width: '100%',
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginTop: 33,
  },
  logoContainer: {
    width: 84,
    height: 84,
    borderRadius: 42,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -40,
    backgroundColor: GlobalStyles.base.white,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  logo: {
    width: 52,
    height: 52,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  qrContainer: {
    width: 230,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    padding: 6,
    marginTop: 6,
    marginBottom: 14,
  },
  qr: {
    width: 220,
    height: 220,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  address: {
    fontSize: 16,
    letterSpacing: 1,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    fontStyle: 'normal',
    textAlign: 'center',
    color: GlobalStyles.gray.gray800,
    width: '90%',
    marginVertical: 10,
  },
  miscellaneousContainer: {
    width: 230,
    flexDirection: 'row',
  },
  copyContainer: {
    flex: 1,
    flexDirection: 'row',
    margin: 10,
    alignItems: 'center',
    alignSelf: 'flex-start',
    justifyContent: 'flex-start',
    marginLeft: 20,
  },
  copyText: {
    fontSize: 12,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '600',
    fontStyle: 'normal',
    lineHeight: 14,
    textAlign: 'center',
    color: GlobalStyles.primary.primary500,
    textTransform: 'capitalize',
  },
  shareContainer: {
    flex: 1,
    flexDirection: 'row',
    margin: 10,
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    marginRight: 20,
  },
  shareText: {
    fontSize: 12,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '600',
    fontStyle: 'normal',
    lineHeight: 14,
    textAlign: 'center',
    color: GlobalStyles.primary.primary500,
    textTransform: 'capitalize',
  },
  optionContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    height: 30,
    paddingVertical: 4,
    zIndex: 2,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
  },
  selectTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    marginVertical: 5,
  },
  assetsDropdownHeader: {
    position: 'relative',
    width: '55%',
    marginRight: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 10,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 5,
  },
  assetDropdownHeaderTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '60%',
    marginRight: 10,
  },
  assetContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
});
