import {AuthAsset} from '@/types/authTypes';

export const fetcher = async (data: any) => {
  // if data is [url, payload] then payload is not null
  const [url, payload] = data;

  console.log('url', url);
  const options = {
    method: payload ? 'POST' : 'GET',
    ...(payload && {body: payload}),
    headers: {
      accept: 'application/json',
      'Content-Type': 'application/json',
    },
  };
  return fetch(url, options).then((r) => r.json());
};

export const parseAssetsToOptions = (assets: AuthAsset[]) => {
  return assets.map((asset) => {
    return {
      value: asset.tokenSymbol,
      label: asset.tokenSymbol,
    };
  });
};
