import {useIsFocused, useRoute} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, View} from 'react-native';
import WebView from 'react-native-webview';
import {connect} from 'react-redux';

import Check from '@/assets/icons/check.svg';
import HoldToConfirmButton from '@/components/HoldToConfirmButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {navigate} from '@/navigation/utils/navigation';
import {findWalletIndex} from '@/screens/CurrencySpecificScreen/currencySpecificUtils';
import {debugging} from '@/screens/CurrencySpecificScreen/SendAsset/sendAssetUtils';
import Success from '@/screens/Success';
import {WalletBalanceInstance} from '@/services/BackendServices';
import WalletService from '@/services/WalletService';
import {
  AuthAddress,
  AuthAddresses,
  AuthAddressType,
  AuthAsset,
  AuthUser,
} from '@/types/authTypes';
import {triggerBiometrics} from '@/utils/biometrics';
import FixedText from '@components/FixedText/FixedText';
import {
  getPreparedTx,
  sendAndBroadcastSolanaTransaction,
} from '../SendAssetInput/SendAssetInputUtils';
import {styles} from '../SendAssetInput/styles';

const CheckSvg = () => <Check width={52} height={52} />;

type SendAssetConfirmationProps = {
  user: AuthUser;
  userAddresses: AuthAddresses;
};

type SendAssetConfirmationParams = {
  builtTx: any;
  recipientAddress: string;
  amount: string;
  stableCoin: string;
  asset: AuthAsset;
  assets: AuthAsset[] | null;
  calculatedAmount: string;
  calculatedFee: string;
  tronFee: string;
  approximateFee: string;
  address: AuthAddressType | null;
  calculatedPrices: string[][];
  tokenPrices: string[][];
  stableCoinIndex: number;
  stableCoinAmount: string;
  solanaBlockhash: string;
  solanaPDAWallet: string[];
};

const SendAssetConfirmation: React.FC = (props: any) => {
  const route = useRoute();
  const {
    builtTx,
    recipientAddress,
    amount,
    stableCoin,
    asset,
    assets,
    calculatedAmount,
    calculatedFee,
    tronFee,
    approximateFee,
    address,
    calculatedPrices,
    tokenPrices,
    stableCoinIndex,
    stableCoinAmount,
    solanaBlockhash,
    solanaPDAWallet,
  } = route.params as SendAssetConfirmationParams;

  const {t} = useTranslation();
  const viewFocused = useIsFocused();
  const dispatch = useAppDispatch();

  const {user, userAddresses} = props as SendAssetConfirmationProps;

  const [disabled, setDisabled] = useState(false);
  const [signedKAS, setSignedKAS] = useState(null);
  const [payload, setPayload] = useState(debugging);
  const [success, setSuccess] = useState(false);

  const confirmationLogoContainer = StyleSheet.flatten([
    styles.logoContainer,
    {shadowColor: GlobalStyles.success.success600},
  ]);

  const squeezeAddress = (address: string) => {
    return address.substring(0, 6) + '...' + address.substring(38, 42);
  };

  const onMessage = (payload: any) => {
    let dataPayload;

    try {
      dataPayload = JSON.parse(payload.nativeEvent.data);
    } catch (e) {
      console.log(e);
    }

    console.log(dataPayload);

    if (dataPayload.type === 'Console') {
      if (dataPayload.data.type === 'info') {
        const log = JSON.parse(dataPayload.data.log);
        setSignedKAS(log);
      }
    }
  };

  useEffect(() => {
    if (signedKAS !== null) {
      WalletBalanceInstance.post(`/broadcast-kaspa`, {
        //@ts-ignore
        transaction: signedKAS.tx,
      })
        .then((res) => {
          console.log('res', res);
          setDisabled(false);
          setSuccess(true);
        })
        .catch((error) => {
          console.log('error', error.response);
          setDisabled(false);
        });
    }
  }, [signedKAS]);

  const handleConfirmPress = async (): Promise<boolean> => {
    setDisabled(true);

    if (asset?.tokenSymbol === 'SOL') {
      const result = await triggerBiometrics();

      if (result.success) {
        await sendAndBroadcastSolanaTransaction(
          address,
          builtTx,
          stableCoin,
          asset,
          userAddresses,
          recipientAddress,
          amount,
          solanaPDAWallet,
        );

        setSuccess(true);
        return true;
      }

      setDisabled(false);
      return false;
    }

    const result = await triggerBiometrics();

    if (result.success) {
      const {tx, wallet, privKey} = await getPreparedTx(
        stableCoin,
        asset,
        user.wallet,
        recipientAddress,
        Number(amount),
        userAddresses,
      );

      if (tx && asset.blockchain !== 'kaspa') {
        // Handle UTXO chains with the new method
        if (
          wallet &&
          (wallet.blockchain === 'bitcoin' ||
            wallet.blockchain === 'bitcoin-cash' ||
            wallet.blockchain === 'litecoin' ||
            wallet.blockchain === 'dogecoin')
        ) {
          if(wallet.blockchain === 'bitcoin-cash') {
            await new WalletService().signAndBroadcastBchTx(tx, wallet);
          } else {
            await new WalletService().signAndBroadcastUTXOTransaction(tx, wallet);
          }

          // await new WalletService().signAndBroadcastUTXOTransaction(tx, wallet);
        } else if (wallet && privKey) {
          // Handle other chains with existing method
          await new WalletService().sendPreparedTx(tx, privKey, wallet);
        } else {
          console.error('Missing wallet or private key for transaction');
          setDisabled(false);
          return false;
        }
      } else if (tx && asset.blockchain === 'kaspa') {
        const walletIndex = findWalletIndex(asset.tokenSymbol);
        const kaspaUserAddress = userAddresses[walletIndex];

        if (kaspaUserAddress && 'addresses' in kaspaUserAddress) {
          const privKeys = kaspaUserAddress.addresses.map(
            (address: AuthAddress) => address.privateKey,
          );
          setPayload(
            debugging +
              `\nmultipleHashTx(${JSON.stringify(tx)}, ${JSON.stringify(
                privKeys,
              )}).then((res) => {console.info(res)});`,
          );
          return true;
        } else {
          console.error('Kaspa addresses not found');
          setDisabled(false);
          return false;
        }
      }

      setSuccess(true);
      return true;
    } else {
      setDisabled(false);
      return false;
    }
  };

  useEffect(() => {
    setDisabled(false);
    setSuccess(false);
    setPayload(debugging);
    setSignedKAS(null);
  }, [viewFocused]);

  return (
    <>
      {!success ? (
        <>
          <View style={GlobalStyles.THEME.mainContainer}>
            <View style={styles.container}>
              <View style={confirmationLogoContainer}>
                <CheckSvg />
              </View>
              <View style={styles.infoContainer}>
                <View style={styles.receivingContainer}>
                  <FixedText style={styles.leftText}>
                    {t('currencySpecific.receivingAddr')}
                  </FixedText>
                  <FixedText style={styles.rightText}>
                    {squeezeAddress(recipientAddress)}
                  </FixedText>
                </View>
              </View>
              <View style={styles.line} />
              <View style={styles.infoContainer}>
                <View style={styles.receivingContainer}>
                  <FixedText style={styles.leftText}>Amount</FixedText>
                  <View
                    style={{
                      ...styles.rightContainer,
                      ...{flexDirection: 'row'},
                    }}
                  >
                    <FixedText style={styles.rightText}>
                      {amount} {stableCoin != '' ? stableCoin : asset?.tokenSymbol}
                    </FixedText>
                  </View>
                </View>
              </View>
              <View style={styles.line} />
              <View style={styles.infoContainer}>
                <View
                  style={{
                    ...styles.receivingContainer,
                    ...{height: 50, marginBottom: 0},
                  }}
                >
                  <View style={{width: '20%'}}>
                    <FixedText style={styles.leftText}>Fee</FixedText>
                  </View>
                  <View style={styles.rightContainer}>
                    <FixedText style={styles.rightText}>
                      {calculatedFee}{' '}
                      {asset?.tokenSymbol === 'TRX' ? 'Bandwidth' : asset?.tokenSymbol}
                      {asset?.tokenSymbol === 'TRX' && tronFee !== '0'
                        ? ` | ${tronFee} TRX`
                        : ''}
                    </FixedText>
                    <FixedText style={styles.approximateFee}>
                      {approximateFee !== '' && approximateFee !== 'NaN'
                        ? `≈ ${approximateFee} USD`
                        : null}
                    </FixedText>
                  </View>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.bottomContainer}>
            <HoldToConfirmButton onPress={handleConfirmPress} disabled={disabled} />
          </View>

          <View style={{height: 0, width: 0, paddingBottom: 20}}>
            {payload != debugging && (
              <WebView
                ref={
                  // @ts-ignore
                  (ref) => (this.webview = ref)
                }
                source={{
                  uri: 'https://dev.assetify.net/kaspa-worker/index.html',
                }}
                javaScriptEnabled={true}
                onLoad={() => {
                  // @ts-ignore
                  this.webview.injectJavaScript(payload);
                }}
                onMessage={onMessage}
              />
            )}
          </View>
        </>
      ) : (
        <Success
          isSuccess={success}
          title={t('currencySpecific.txSent')}
          description={t('currencySpecific.txSentDescription')}
          onFinish={() => {
            navigate('BottomTabs', {
              screen: 'Wallet',
              params: {
                screen: 'WalletHome',
              },
            });
          }}
        />
      )}
    </>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.auth.user,
  userAddresses: state.auth.userAddress ?? state.auth.userAddresses,
});

export default connect(mapStateToProps)(SendAssetConfirmation);
