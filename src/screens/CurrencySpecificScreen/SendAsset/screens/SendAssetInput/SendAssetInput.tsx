import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';

import ArrowDown from '@/assets/icons/arrow down.svg';
import ArrowUp from '@/assets/icons/arrow up.svg';
import Scan from '@/assets/icons/scan.svg';
import Banner from '@/components/Banner';
import Camera from '@/components/Camera';
import FixedText from '@/components/FixedText/FixedText';
import Logo from '@/components/Logo/AssetLogo';
import MButton from '@/components/MButton';
import {coinToData} from '@/constants/AbiConstants';
import {ChainToTokens, StableChains} from '@/constants/Chains';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {navigate} from '@/navigation/utils/navigation';
import {findWalletIndex} from '@/screens/CurrencySpecificScreen/currencySpecificUtils';
import type {
  AuthAddresses,
  AuthAddressType,
  AuthAsset,
  AuthUser,
} from '@/types/authTypes';
import {useIsFocused, useRoute} from '@react-navigation/native';
import {
  calculateApproximateValue,
  getPreparedTx,
  prepareSolanaTransaction,
  validateAddressForAsset,
} from './SendAssetInputUtils';
import {styles} from './styles';

const getChainFullName = (chainSymbol: string): string => {
  const chainMap: Record<string, string> = {
    ETH: 'Ethereum',
    BSC: 'Binance Smart Chain',
    BTC: 'Bitcoin',
    AVAX: 'Avalanche',
    TRX: 'Tron',
    XRP: 'Ripple',
    KAS: 'Kaspa',
    BCH: 'Bitcoin Cash',
    LTC: 'Litecoin',
    DOGE: 'Dogecoin',
    SOL: 'Solana',
  };
  return chainMap[chainSymbol] || chainSymbol;
};

const getAssetSvgName = (chain: string, token?: string): string => {
  if (!token) {
    if (chain === 'ETHEREUM') return 'ETHSVG';
    if (chain === 'BITCOIN') return 'BTCSVG';
    if (chain === 'BSC') return 'BSCSVG';
    return `${chain}SVG`;
  }

  if (chain === 'BSC') {
    return `${token}BNBSVG`;
  }

  const tokenClean = token.replace('.e', '').toUpperCase();

  if (chain === 'ETH') {
    return `${tokenClean}ETHSVG`;
  } else if (chain === 'BSC') {
    return `${tokenClean}BNBSVG`;
  } else if (chain === 'TRX') {
    return `${tokenClean}TRXSVG`;
  } else if (chain === 'AVAX') {
    if (token.includes('.e')) {
      return `${tokenClean}EAVAXSVG`;
    }
    return `${tokenClean}AVAXSVG`;
  } else if (chain === 'SOL') {
    return `${tokenClean}SOLSVG`;
  }

  return `${tokenClean}SVG`;
};

const ScanSvg = () => <Scan width={24} height={24} />;
const ArrowUpSvg = () => <ArrowUp width={24} height={24} />;
const ArrowDownSvg = () => <ArrowDown width={24} height={24} />;

type SendAssetInputProps = {
  user: AuthUser;
  userAddresses: AuthAddresses;
};

type SendAssetInputParams = {
  asset: AuthAsset | null;
  stableCoin: string;
  stableCoinIndex: number;
  stableCoinAmount: string;
  address: AuthAddressType | null;
  assets: AuthAsset[] | null;
  stableCoins: string[][] | null;
  tokenPrices: string[] | string[][];
  calculatedPrices: string[] | string[][];
  balance: string;
  recipientAddr: string;
  amount: string;
};

const SendAssetInput: React.FC = (props: any) => {
  const {t} = useTranslation();
  const viewFocused = useIsFocused();
  const route = useRoute() as {params: SendAssetInputParams};
  const {user, userAddresses} = props as SendAssetInputProps;
  const {open: openBottomSheet} = useBottomSheet('list');

  const [asset, setAsset] = useState<AuthAsset>(
    route.params.asset ||
      (route.params.assets && route.params.assets[0]) ||
      ({} as AuthAsset),
  );
  const [assets, setAssets] = useState<AuthAsset[] | null>(route.params.assets);
  const [stableCoin, setStableCoin] = useState<string>(route.params.stableCoin || '');
  const [walletIndex, setWalletIndex] = useState<number>(
    findWalletIndex(asset.tokenSymbol),
  );
  const [stableCoinIndex, setStableCoinIndex] = useState(
    route.params.stableCoinIndex || 0,
  );

  const stableCoins = route.params.stableCoins;
  const address = route.params.address;

  const [recipientAddress, setRecipientAddress] = useState('');
  const [amount, setAmount] = useState('0.000');
  const [balance, setBalance] = useState('0.000');
  const [approximateValue, setApproximateValue] = useState('0');
  const [coins, setCoins] = useState<string[]>([]);
  const [tokenPrices, setTokenPrices] = useState<string[] | string[][]>(
    route.params.tokenPrices,
  );

  const [qrVisible, setQrVisible] = useState<boolean>(false);
  const [disabled, setDisabled] = useState(false);

  const [valideInput, setValideInput] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState('');

  const logoContainer = StyleSheet.flatten([
    styles.logoContainer,
    {shadowColor: GlobalStyles.gray.gray900shadow},
  ]);
  const logoInputContainer = StyleSheet.flatten([
    styles.inputContainer,
    {borderColor: GlobalStyles.gray.gray600},
  ]);
  const assetInputContainer = StyleSheet.flatten([
    styles.inputContainer,
    {
      backgroundColor: GlobalStyles.base.white,
      borderColor: valideInput
        ? GlobalStyles.primary.primary500
        : GlobalStyles.gray.gray600,
    },
  ]);
  const assetsInputContainer = StyleSheet.flatten([
    styles.assetsInputContainer,
    {
      backgroundColor: GlobalStyles.base.white,
      borderColor: valideInput
        ? GlobalStyles.primary.primary500
        : GlobalStyles.gray.gray600,
    },
  ]);
  const assetDropdownHeader = StyleSheet.flatten([
    styles.assetDropdownHeader,
    {backgroundColor: GlobalStyles.primary.primary50},
  ]);

  const handleAmountChange = async (value: string) => {
    if (Number(value) > Number('0')) {
      setValideInput(true);
    } else {
      setValideInput(false);
    }

    if (Number(value) >= Number(balance)) {
      setValideInput(false);
    }

    if (asset?.tokenSymbol === 'KAS' && Number(value) < 10) {
      setValideInput(false);
    }

    if (asset?.tokenSymbol === 'DOGE' && Number(value) < 1) {
      setValideInput(false);
    }

    value = value.replace(/,/g, '.');
    if (value.includes('.')) {
      const decimalPlaces = value.split('.')[1];
      if (stableCoin !== '' && asset?.tokenSymbol && decimalPlaces) {
        const coinData = coinToData[asset.tokenSymbol]?.[stableCoin];
        const decimals = coinData?.decimals;
        if (typeof decimals === 'number' && decimals < decimalPlaces.length) {
          value = value.slice(
            0,
            value.indexOf('.') +
              (coinToData[asset.tokenSymbol]?.[stableCoin.toUpperCase()]?.decimals || 0) +
              1,
          );
        }
      }

      if (decimalPlaces && decimalPlaces.length > 18) {
        value = value.slice(0, value.indexOf('.') + 19);
      }
    }

    setAmount(value);
  };

  const handleCalculatePrice = (amount: string) => {
    let prices: string[] | string[][] = [];

    if (stableCoin !== '') {
      if (Array.isArray(tokenPrices)) {
        prices = tokenPrices as string[] | string[][];
      }
    } else {
      if (Array.isArray(tokenPrices) && Array.isArray(tokenPrices[0])) {
        const walletPrices = tokenPrices[walletIndex];
        prices = Array.isArray(walletPrices) ? walletPrices : [];
      } else if (Array.isArray(tokenPrices)) {
        prices = tokenPrices as string[] | string[][];
      }
    }

    setApproximateValue(
      calculateApproximateValue(amount, prices, walletIndex, stableCoinIndex),
    );
  };

  const setCoin = (coinValue: string) => {
    if (!coinValue.includes('-')) {
      const foundAsset = assets?.find((a) => a.tokenSymbol === coinValue);

      if (foundAsset) {
        setAsset(foundAsset);
        setWalletIndex(findWalletIndex(foundAsset.tokenSymbol));
        setStableCoin('');
        setStableCoinIndex(0);
        setBalance(foundAsset.amount);
      }
    } else {
      const [chainPart = '', tokenPart = ''] = coinValue.split('-');

      const foundChainAsset = assets?.find((a) => a.tokenSymbol === chainPart);

      if (foundChainAsset && foundChainAsset.blockchain) {
        let tokenIndex = 0;
        ChainToTokens[foundChainAsset.blockchain]?.forEach((token, idx) => {
          if (token.tokenSymbol === tokenPart) {
            tokenIndex = idx + 1;
          }
        });

        setAsset(foundChainAsset);
        setWalletIndex(findWalletIndex(foundChainAsset.tokenSymbol));
        setStableCoin(tokenPart);
        setStableCoinIndex(tokenIndex);

        if (stableCoins && stableCoins[walletIndex] && tokenIndex > 0) {
          setBalance(stableCoins[walletIndex][tokenIndex - 1] || '0');
        } else {
          setBalance('0');
        }
      }
    }
  };

  const getAssetLogo = (chainSymbol: string | undefined, tokenSymbol?: string) => {
    const chainValue = chainSymbol || '';
    // TEMP solution for BSC
    const chainValueTransformer = chainValue.toUpperCase() === 'BNB' ? 'BSC' : chainValue;
    const svgName = getAssetSvgName(chainValueTransformer, tokenSymbol);
    return <Logo name={svgName} />;
  };

  const renderAssetComponent = (
    assetName: string | undefined,
    showAmount: boolean = false,
  ) => {
    if (!assetName) return null;

    let chainSymbol = '';
    let tokenSymbol = '';

    if (assetName.includes('-')) {
      const parts = assetName.split('-');
      chainSymbol = parts[0] || '';
      tokenSymbol = parts[1] || '';
    } else {
      chainSymbol = assetName;
    }

    const assetAmount = tokenSymbol
      ? assets?.find((a) => a.tokenSymbol === tokenSymbol)?.amount || '0'
      : assets?.find((a) => a.tokenSymbol === chainSymbol)?.amount || '0';

    return (
      <View style={styles.optionContainer}>
        {getAssetLogo(chainSymbol, tokenSymbol)}
        {showAmount && (
          <View style={{flex: 1, justifyContent: 'center'}}>
            <FixedText
              style={{
                textAlign: 'right',
                fontSize: 18,
                color: GlobalStyles.base.black,
              }}
            >
              {assetAmount}
            </FixedText>
          </View>
        )}
      </View>
    );
  };

  const disabledNext = () => {
    if (
      !valideInput ||
      disabled ||
      parseFloat(amount) <= 0 ||
      parseFloat(amount) > parseFloat(balance) ||
      errorMessage
    ) {
      return true;
    }

    return !(recipientAddress && amount);
  };

  const handleMaxPress = async () => {
    const convertedAmount = Number(balance) - 0.1 * Number(balance);
    setAmount(convertedAmount.toString());
    handleCalculatePrice(convertedAmount.toString());
    setValideInput(true);
  };

  const handleNextPress = async () => {
    setDisabled(true);

    if (asset?.tokenSymbol === 'SOL') {
      // const tx = await WalletManagerBridge.buildUnsignedSolanaTransaction(
      //   props.userAddresses[walletIndex].address,
      //   recipientAddress,
      //   Number(amount),
      // );

      // try {
      //   const response = await fetch(
      //     'https://dev.assetify.net/balance-fetcher/transaction/solana/mainnet/v2/getfee',
      //     {
      //       method: 'POST',
      //       headers: {
      //         'Content-Type': 'application/json',
      //       },
      //       body: tx,
      //     },
      //   );

      //   if (!response.ok) {
      //     throw new Error('Failed to fetch fee information');
      //   }

      //   const feeData = await response.json();

      //   const baseFeeLamports = Number(feeData.solana.fees.baseFee);
      //   const calculatedFee = (baseFeeLamports / 1_000_000_000).toFixed(6);

      const {tx, calculatedFee, feeData} = await prepareSolanaTransaction(
        recipientAddress,
        Number(amount),
        userAddresses,
      );

      (navigate as any)('SendAssetConfirmation', {
        builtTx: tx,
        recipientAddress: recipientAddress,
        amount: amount,
        stableCoin: stableCoin,
        asset: asset,
        assets: assets,
        calculatedAmount: (Number(amount) * Number(tokenPrices[stableCoinIndex])).toFixed(
          2,
        ),
        calculatedFee,
        tronFee: '0',
        approximateFee: calculateApproximateValue(
          calculatedFee,
          Array.isArray(tokenPrices) ? tokenPrices : [],
          walletIndex,
          stableCoinIndex,
        ),
        address: address,
        calculatedPrices: route.params.calculatedPrices,
        tokenPrices: tokenPrices,
        stableCoinIndex: stableCoinIndex,
        stableCoinAmount: route.params.stableCoinAmount,
        solanaBlockhash: feeData.solana.blockhash,
        solanaPDAWallet: feeData.solana.PDA.wallets,
      });
    }

    let approximateFee;
    let tronFee = '0';
    let calculatedFee = '0';
    let calculatedAmount = (
      Number(amount) * Number(tokenPrices[stableCoinIndex])
    ).toFixed(2);

    console.log('Preparing Transaction >>> >>> >>>');
    const {tx} = await getPreparedTx(
      stableCoin,
      asset,
      user.wallet,
      recipientAddress,
      Number(amount),
      userAddresses,
    );

    // console.log('tx', tx);

    if (tx && asset?.tokenSymbol !== 'TRX') {
      let fee = tx.data
        ? tx.data.fee
          ? tx.data.fee.toString()
          : (tx.gasPrice / 10 ** 18).toFixed(10)
        : tx.Fee
        ? (tx.Fee / 10 ** 6).toFixed(6)
        : (tx.gasPrice / 10 ** 18).toFixed(10);

      fee = fee.includes('.') ? fee.replace(/0+$/, '') : fee;

      console.log('fee >>>>', fee);

      // Visualized fee denominated
      if (
        asset?.tokenSymbol === 'BTC' ||
        asset?.tokenSymbol === 'DOGE' ||
        asset?.tokenSymbol === 'LTC' ||
        asset?.tokenSymbol === 'BCH' ||
        asset?.tokenSymbol === 'KAS'
      ) {
        fee = (Number(fee) / 10 ** 8).toFixed(8);
      }

      calculatedFee = fee;
      approximateFee = calculateApproximateValue(
        fee,
        Array.isArray(tokenPrices) ? tokenPrices : [],
        walletIndex,
        stableCoinIndex,
      );
    } else {
      calculatedFee = tx.bandwidthEstimate;
      if (tx.estimateTRX) {
        tronFee = tx.estimateTRX;
        let approximatePrice = calculateApproximateValue(
          tx.estimateTRX,
          Array.isArray(tokenPrices) ? tokenPrices : [],
          walletIndex,
          0,
        );
        approximateFee =
          Number(approximatePrice).toFixed(2) == '0.00'
            ? '0'
            : Number(approximatePrice).toFixed(2);
      } else {
        approximateFee = (
          (Number(tx.bandwidthEstimate) / 1000) *
          Number(tokenPrices[0])
        ).toFixed(2);
      }
    }

    console.log('calculatedFee >>>>', calculatedFee);

    (navigate as any)('SendAssetConfirmation', {
      recipientAddress: recipientAddress,
      amount: amount,
      stableCoin: stableCoin,
      asset: asset,
      assets: assets,
      calculatedAmount: calculatedAmount,
      calculatedFee: calculatedFee,
      tronFee: tronFee,
      approximateFee: approximateFee,
      address: address,
      calculatedPrices: route.params.calculatedPrices,
      tokenPrices: tokenPrices,
      stableCoinIndex: stableCoinIndex,
      stableCoinAmount: route.params.stableCoinAmount,
    });
  };

  const handleOpenBottomSheet = () => {
    const listData = coins.map((coin) => {
      let chainPart = '';
      let tokenPart = '';
      let bridgedToken = false;
      let bridgedChains = ['AVAX'];

      if (coin.includes('-')) {
        const parts = coin.split('-');
        chainPart = parts[0] || '';
        tokenPart = parts[1] || '';
      } else {
        chainPart = coin;
      }

      const foundAsset = assets?.find((a) =>
        tokenPart ? a.tokenSymbol === chainPart : a.tokenSymbol === chainPart,
      );

      // Get appropriate label for the icon
      let iconLabel = '';

      if (tokenPart) {
        // This is a token on a chain
        bridgedToken = tokenPart.includes('.e');
        const cleanToken = tokenPart.replace('.e', '').toLowerCase();

        console.log('cleanToken', cleanToken);
        console.log('chainPart', chainPart);

        if (chainPart === 'ETH') {
          iconLabel = cleanToken === 'usdt' ? 'usdt20' : `${cleanToken}eth`;
        } else if (chainPart === 'BSC') {
          iconLabel = `${cleanToken}bsc`;
        } else if (chainPart === 'TRX') {
          iconLabel = `${cleanToken}trx`;
        } else if (chainPart === 'AVAX') {
          // Properly handle Avalanche tokens with .e suffix
          iconLabel = `${cleanToken}avax`;
        } else if (chainPart === 'SOL') {
          iconLabel = `${cleanToken}sol`;
        } else {
          iconLabel = cleanToken;
        }
      } else {
        // This is a main chain
        iconLabel = chainPart.toLowerCase();

        // Special cases for chain labels
        if (chainPart === 'BSC') {
          iconLabel = 'bscbsc'; // Use 'bnbbsc' for BSC chain icon
        } else if (chainPart === 'ETH') {
          iconLabel = 'eth';
        } else if (chainPart === 'BTC') {
          iconLabel = 'btc';
        }
      }

      const chainFullName = getChainFullName(chainPart);

      let tokenFullName = '';
      if (tokenPart && foundAsset?.blockchain) {
        const tokenInfo = ChainToTokens[foundAsset.blockchain]?.find(
          (t) => t.tokenSymbol === tokenPart,
        );
        tokenFullName = tokenInfo?.tokenName || tokenPart;
      }

      const displayName = tokenPart
        ? `${tokenFullName} (${chainFullName}${
            bridgedChains.includes(chainPart) && bridgedToken ? ' Bridge' : ''
          })`
        : chainFullName;

      console.log('iconLabel', iconLabel);
      console.log('displayName', displayName);

      return {
        value: coin,
        label: iconLabel,
        fullName: displayName,
        type: 'crypto' as const,
      };
    });

    openBottomSheet(
      {
        type: 'currency',
        data: listData,
        onSelect: (item) => {
          setCoin(item.value);
        },
      },
      80,
      true,
    );
  };

  const getUserAddress = (
    walletIndex: number,
  ): {
    address: string;
    chain: string;
  } => {
    const userAddress = userAddresses?.[walletIndex];

    if (!userAddress)
      return {
        address: '',
        chain: '',
      };

    // Check if it's a Kaspa address (has addresses array)
    if ('addresses' in userAddress) {
      return {
        address: userAddress.addresses?.[0]?.address?.toLowerCase() || '',
        chain: userAddress.addresses?.[0]?.chain || '',
      };
    }

    // Regular address structure
    if ('address' in userAddress) {
      return {
        address: userAddress.address?.toLowerCase() || '',
        chain: userAddress.chain || '',
      };
    }

    return {
      address: '',
      chain: '',
    };
  };

  const isRecipientSameAsUser = (recipientAddr: string, walletIdx: number): boolean => {
    const {address, chain} = getUserAddress(walletIdx);
    return recipientAddr.toLowerCase() === address && chain === 'kaspa';
  };

  useEffect(() => {
    const initialAsset =
      route.params.asset || (route.params.assets && route.params.assets[0]);
    if (initialAsset) {
      setAsset(initialAsset as AuthAsset);
    }

    setCoins([]);
    setStableCoin(route.params.stableCoin || '');
    setTokenPrices(route.params.tokenPrices);
    setAssets(route.params.assets);
    route.params.recipientAddr
      ? setRecipientAddress(route.params.recipientAddr)
      : setRecipientAddress('');
    setValideInput(false);
    route.params.amount ? setAmount(route.params.amount) : setAmount('0.000');
    setBalance(route.params.balance);
    setApproximateValue('0');
    setDisabled(false);
    setErrorMessage('');

    if (assets) {
      setCoins(
        assets.flatMap((asset) => {
          if (StableChains.includes(asset.title)) {
            const tokens = ChainToTokens[asset.title] || [];
            return [
              asset.tokenSymbol,
              ...tokens.map((token) => `${asset.tokenSymbol}-${token.tokenSymbol}`),
            ];
          } else {
            return asset.tokenSymbol;
          }
        }),
      );
    }

    if (route.params.stableCoin) {
      setStableCoin(route.params.stableCoin);
      setStableCoinIndex(route.params.stableCoinIndex);
    }
  }, [viewFocused]);

  useEffect(() => {
    if (asset) {
      setWalletIndex(findWalletIndex(asset.tokenSymbol));
      const isStableCoinValid = stableCoin
        ? ChainToTokens[asset.blockchain]?.some(
            (token) => token.tokenSymbol === stableCoin,
          )
        : false;

      if (!isStableCoinValid) {
        setStableCoin('');
        setStableCoinIndex(0);
      }
    }

    setErrorMessage('');
    route.params.recipientAddr
      ? setRecipientAddress(route.params.recipientAddr)
      : setRecipientAddress('');
    setValideInput(false);
    route.params.amount ? setAmount(route.params.amount) : setAmount('0.000');
  }, [asset]);

  useEffect(() => {
    handleAmountChange(route.params.amount);
  }, [route.params.recipientAddr, route.params.amount]);

  return (
    <>
      {!qrVisible ? (
        <>
          <View style={GlobalStyles.THEME.mainContainer}>
            <View style={styles.bannerContainer}>
              <Banner type="warning" message={t('wallet.txsAreIrreversible')} />
            </View>
            <View style={styles.container}>
              <View style={logoContainer}>
                {stableCoin
                  ? getAssetLogo(asset?.tokenSymbol, stableCoin)
                  : getAssetLogo(asset?.tokenSymbol)}
              </View>
              <View style={styles.assetContainer}>
                <FixedText style={styles.title}>
                  {t('currencySpecific.address')}
                </FixedText>
                <View style={logoInputContainer}>
                  <TextInput
                    style={styles.input}
                    value={recipientAddress}
                    onChangeText={setRecipientAddress}
                    returnKeyType="done"
                    placeholderTextColor={GlobalStyles.gray.gray900}
                    onEndEditing={() => {
                      if (errorMessage !== '' && errorMessage !== 'Invalid address') {
                        return;
                      } else if (isRecipientSameAsUser(recipientAddress, walletIndex)) {
                        setErrorMessage('Invalid address');
                      } else {
                        validateAddressForAsset(
                          recipientAddress,
                          getUserAddress(walletIndex).chain,
                        ).then((isValid: boolean) => {
                          setErrorMessage(isValid ? '' : 'Invalid address');
                        });
                      }
                    }}
                  />
                  <TouchableOpacity
                    style={styles.qrHeader}
                    onPress={() => {
                      setQrVisible(true);
                    }}
                  >
                    <ScanSvg />
                  </TouchableOpacity>
                </View>
              </View>
              <View style={styles.assetContainer}>
                <FixedText style={styles.title}>{t('calculator.amount')}</FixedText>
                {!assets ? (
                  <>
                    <View style={assetInputContainer}>
                      <TextInput
                        style={styles.amountInput}
                        value={amount}
                        keyboardType="numeric"
                        onChangeText={(value) => {
                          handleAmountChange(value);
                          handleCalculatePrice(value);
                        }}
                        onEndEditing={() => {
                          if (
                            errorMessage !== '' &&
                            errorMessage !== 'Insufficient funds' &&
                            errorMessage !== 'Invalid input' &&
                            errorMessage !== 'Minimum amount is 10' &&
                            errorMessage !== 'Minimum amount is 1'
                          ) {
                            return;
                          } else if (Number(amount) === 0) {
                            setErrorMessage('Invalid input');
                          } else if (Number(amount) >= Number(balance) - 0.1 * +balance) {
                            setErrorMessage('Insufficient funds');
                          } else if (
                            asset?.tokenSymbol === 'KAS' &&
                            Number(amount) < 10
                          ) {
                            setErrorMessage('Minimum amount is 10');
                          } else if (
                            asset?.tokenSymbol === 'DOGE' &&
                            Number(amount) < 1
                          ) {
                            setErrorMessage('Minimum amount is 1');
                          } else {
                            setErrorMessage('');
                          }
                        }}
                        returnKeyType="done"
                        placeholderTextColor={GlobalStyles.gray.gray900}
                        editable={true}
                      />
                      <TouchableOpacity
                        style={assetDropdownHeader}
                        onPress={handleMaxPress}
                        disabled={recipientAddress === ''}
                      >
                        <FixedText style={styles.maxButton}>Max</FixedText>
                      </TouchableOpacity>
                    </View>
                    <View style={[styles.approximateValueContainer, {marginTop: 24}]}>
                      <Text
                        style={styles.approximateValue}
                      >{`≈ ${approximateValue} USD`}</Text>
                    </View>
                  </>
                ) : (
                  <>
                    <View style={assetsInputContainer}>
                      <TextInput
                        style={styles.assetsInput}
                        keyboardType="numeric"
                        placeholder={'0.00'}
                        value={amount}
                        onChangeText={(value) => {
                          handleAmountChange(value);
                          handleCalculatePrice(value);
                        }}
                        onEndEditing={() => {
                          if (
                            errorMessage !== '' &&
                            errorMessage !== 'Insufficient funds' &&
                            errorMessage !== 'Invalid input' &&
                            errorMessage !== 'Minimum amount is 10' &&
                            errorMessage !== 'Minimum amount is 1'
                          ) {
                            return;
                          } else if (Number(amount) === 0) {
                            setErrorMessage('Invalid input');
                          } else if (Number(amount) >= Number(balance)) {
                            setErrorMessage('Insufficient funds');
                          } else if (
                            asset?.tokenSymbol === 'KAS' &&
                            Number(amount) < 10
                          ) {
                            setErrorMessage('Minimum amount is 10');
                          } else if (
                            asset?.tokenSymbol === 'DOGE' &&
                            Number(amount) < 1
                          ) {
                            setErrorMessage('Minimum amount is 1');
                          } else {
                            setErrorMessage('');
                          }
                        }}
                        returnKeyType="done"
                        placeholderTextColor={GlobalStyles.gray.gray900}
                      />
                      <TouchableOpacity
                        style={styles.assetsDropdownHeader}
                        onPress={handleOpenBottomSheet}
                      >
                        <View style={styles.assetDropdownHeaderTitleContainer}>
                          {renderAssetComponent(
                            stableCoin
                              ? `${asset?.tokenSymbol}-${stableCoin}`
                              : asset?.tokenSymbol,
                            false,
                          )}
                        </View>
                        <ArrowDownSvg />
                      </TouchableOpacity>
                    </View>
                    <View style={styles.approximateValueContainer}>
                      <Text
                        style={styles.approximateValue}
                      >{`≈ ${approximateValue} USD`}</Text>
                    </View>
                  </>
                )}
              </View>
              {errorMessage && (
                <FixedText style={styles.errorText}>{errorMessage}</FixedText>
              )}
            </View>
          </View>
          <View
            style={{
              position: 'absolute',
              bottom: 15,
              width: '90%',
              alignSelf: 'center',
            }}
          >
            <MButton
              text="Next"
              onPress={handleNextPress}
              disabled={disabledNext()}
              isLoading={disabled}
            />
          </View>
        </>
      ) : (
        <>
          <View style={StyleSheet.absoluteFill}>
            <View
              style={{
                flex: 1,
                justifyContent: 'flex-start',
                paddingBottom: 72,
                paddingHorizontal: 22,
              }}
            >
              <Camera
                onBarcodeRead={(e) => {
                  setRecipientAddress(e);
                  setQrVisible(false);
                }}
              />
            </View>
          </View>
          <View
            style={{position: 'absolute', bottom: 15, width: '90%', alignSelf: 'center'}}
          >
            <MButton
              text="Close"
              onPress={() => {
                setQrVisible(false);
              }}
            />
          </View>
        </>
      )}
    </>
  );
};

const mapStateToProps = (state: any) => ({
  user: state.auth.user,
  userAddresses: state.auth.userAddress ?? state.auth.userAddresses,
});

export default connect(mapStateToProps)(SendAssetInput);
