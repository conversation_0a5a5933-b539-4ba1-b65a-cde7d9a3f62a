import {Dimensions, StyleSheet} from 'react-native';
import GlobalStyles from '../../../../../constants/GlobalStyles';
import {getFontSize} from '../../../../../utils/parsing';

export const styles = StyleSheet.create({
  bannerContainer: {
    width: '100%',
    marginBottom: 26,
    marginTop: 4,
  },
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    padding: 6,
    display: 'flex',
    width: '100%',
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginTop: 32,
  },
  logoContainer: {
    width: 84,
    height: 84,
    borderRadius: 42,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -40,
    marginBottom: 16,
    backgroundColor: GlobalStyles.base.white,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  logo: {
    width: 52,
    height: 52,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    width: '90%',
  },
  leftText: {
    flex: 1,
    textAlign: 'left',
    fontSize: 17,
    fontWeight: '500',
    lineHeight: 22,
    color: GlobalStyles.gray.gray900,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  rightText: {
    textAlign: 'right',
    fontSize: 14,
    lineHeight: 19,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
  tokenPriceText: {
    fontSize: 14,
    lineHeight: 19,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontStyle: 'normal',
    textAlign: 'right',
    color: GlobalStyles.gray.gray900,
  },

  line: {
    width: '90%',
    height: 1,
    backgroundColor: GlobalStyles.gray.gray600,
  },
  assetContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    marginVertical: 15,
    paddingHorizontal: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    marginBottom: 5,
  },
  approximateValueContainer: {
    marginTop: 10,
  },
  approximateValue: {
    fontSize: 16,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    marginLeft: 13,
    marginBottom: -12,
  },
  approximateFee: {
    fontSize: 14,
    fontWeight: '500',
    color: GlobalStyles.base.black,
    flex: 1,
    textAlign: 'right',
  },
  input: {
    width: '85%',
    height: 50,
    borderRadius: 8,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 5,
    paddingVertical: 10,
    fontSize: 18,
    color: GlobalStyles.base.black,
  },
  amountInput: {
    width: '80%',
    height: 50,
    borderRadius: 8,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 15,
    paddingVertical: 10,
    fontSize: 18,
    color: GlobalStyles.base.black,
  },
  inputContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // add border
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: -15,
  },
  assetDropdownHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 6,
    backgroundColor: '#fff',
    marginRight: 10,
    marginLeft: 'auto',
    borderRadius: 200,
    // width: 31,
  },
  assetDropdownHeaderIcon: {
    width: 24,
    height: 24,
  },
  maxButton: {
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.primary.primary500,
    fontSize: getFontSize(12),
    fontWeight: '500',
  },
  cameraContainer: {
    marginTop: -80,
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  camera: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingBottom: 80,
  },
  bottomContainer: {
    position: 'absolute',
    bottom: 15,
    alignItems: 'center',
    width: '100%',
  },
  receivingContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  rightContainer: {
    width: '80%',
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  assetsInput: {
    width: '55%',
    height: 50,
    borderRadius: 8,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 15,
    paddingVertical: 10,
    fontSize: 18,
    color: GlobalStyles.base.black,
  },
  assetsInputContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    // add border
    borderColor: GlobalStyles.gray.gray600,
    borderRadius: 8,
    borderWidth: 1,
  },
  assetsDropdownHeader: {
    position: 'relative',
    width: '45%',
    marginRight: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 10,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 5,
  },
  qrHeader: {
    position: 'relative',
    width: '10%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    paddingVertical: 10,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 5,
  },
  assetsDropdownHeaderIcon: {
    width: 24,
    height: 24,
  },
  assetDropdownHeaderTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  assetDropdownContent: {
    position: 'absolute',
    top: '100%',
    right: 0,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    backgroundColor: GlobalStyles.base.white,
    width: '100%',
    zIndex: 2,
    // flex: 1,
    maxHeight: 3 * 53,
  },
  assetDropdownItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray900shadow,
    alignSelf: 'flex-start',
    width: '100%',
  },
  optionContainer: {
    flexDirection: 'row',
    height: 30,
    paddingVertical: 4,
    zIndex: 2,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginLeft: 10,
  },
  selectTitle: {
    fontSize: 18,
    fontWeight: '500',
    color: GlobalStyles.gray.gray900,
    marginVertical: 5,
  },
  errorText: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.error.error500,
    marginTop: 5,
    marginBottom: 10,
  },
});
