import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useState} from 'react';
import {Platform, StyleSheet} from 'react-native';

import DetectSwipe from '@/components/DetectSwipe';
import SafeAreaInset from '@/components/SafeAreaInset';
import Slider from '@/components/Slider';
import CalculatorScreen from '@/screens/CalculatorScreen/CalculatorScreen';
import NewsScreen from '@/screens/NewsScreen/NewsScreen';

const More = () => {
  const [activeTab, setActiveTab] = useState<string>('news');

  const handleTabChange = (tabName: string) => {
    setActiveTab(tabName);
  };

  const handleSwipeLeft = () => {
    if (activeTab === 'news') {
      setActiveTab('calculator');
    }
  };

  const handleSwipeRight = () => {
    if (activeTab === 'calculator') {
      setActiveTab('news');
    }
  };

  useFocusEffect(
    useCallback(() => {
      setActiveTab('news');
    }, []),
  );

  return (
    <>
      <SafeAreaInset type="top" />

      <Slider
        tabs={['news', 'calculator']}
        activeTab={activeTab}
        onTabClick={handleTabChange}
      />

      {/* Temporary solution */}
      {Platform.select({
        android: activeTab === 'news' ? <NewsScreen /> : <CalculatorScreen />,
        ios: (
          <DetectSwipe onSwipeLeft={handleSwipeLeft} onSwipeRight={handleSwipeRight}>
            {activeTab === 'news' ? <NewsScreen /> : <CalculatorScreen />}
          </DetectSwipe>
        ),
      })}
    </>
  );
};

export default More;

export const styles = StyleSheet.create({
  content: {
    flex: 1,
  },
});
