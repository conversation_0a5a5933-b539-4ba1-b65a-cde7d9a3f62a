import {Payment} from '@breeztech/react-native-breez-sdk';
import Clipboard from '@react-native-clipboard/clipboard';
import {useRoute} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {SafeAreaView, TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';
import FixedText from '@/components/FixedText/FixedText';
import TopNavigation from '@/components/TopNavigation';
import GlobalStyles from '@/constants/GlobalStyles';
import {parsePrice} from '@/utils/parsing';
import {styles} from './styles';

type PaymentDetailsProps = {
  payment: Payment;
};

const PaymentDetailsScreen: React.FC = (props: any) => {
  const {t} = useTranslation();

  const route = useRoute() as {params: PaymentDetailsProps};
  const payment = route.params.payment;

  return (
    <>
      <SafeAreaView>
        <TopNavigation
          screenTitle={t('txDetails.title')}
          leftIcon={true}
          leftIconAction={() => props.navigation.goBack()}
        />
      </SafeAreaView>
      <View style={GlobalStyles.THEME.mainContainer}>
        <View style={[styles.container, {alignItems: 'flex-end'}]}>
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.type')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {payment.paymentType === 'received' ? 'Received' : 'Sent'}
            </FixedText>
          </View>

          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.amount')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {parsePrice((payment.amountMsat / 1000).toString())}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.status')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {payment.status === 'complete' ? 'Complete' : 'Canceled'}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.date')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {new Date(payment.paymentTime * 1000).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              })}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.fee')}
            </FixedText>
            <FixedText style={styles.valueText} numberOfLines={1}>
              {parsePrice((payment.feeMsat / 1000).toString())}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={2}>
              {t('txDetails.invoice')}
            </FixedText>
          </View>
          <TouchableOpacity
            style={{...styles.row, marginTop: -10}}
            onPress={() => {
              // @ts-ignore
              Clipboard.setString(payment.details.data.bolt11);
            }}
          >
            <FixedText style={{...styles.typeText, color: GlobalStyles.gray.gray900}}>
              {
                // @ts-ignore
                payment.details.data.bolt11
              }
            </FixedText>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
};

const mapStateToProps = (state: any) => ({
  isConnected: state.common.isConnected,
  user: state.user,
});

export default connect(mapStateToProps, null)(PaymentDetailsScreen);
