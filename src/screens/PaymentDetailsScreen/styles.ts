import {StyleSheet} from 'react-native';
import GlobalStyles from '../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    display: 'flex',
    width: '100%',
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  row: {
    flexDirection: 'row',
    alignSelf: 'center',
    width: '90%',
    marginTop: 20,
    marginBottom: 20,
  },
  typeText: {
    flex: 1,
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.base.black,
  },
  valueText: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 19,
    color: GlobalStyles.gray.gray900,
    width: '43%',
    textAlign: 'right',
    alignSelf: 'center',
  },
  line: {
    width: '90%',
    height: 2,
    backgroundColor: GlobalStyles.gray.gray500,
  },
  icon: {
    marginLeft: 2,
    marginTop: -2,
    alignSelf: 'flex-end',
  },
  addresses: {
    flexDirection: 'row',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    marginVertical: -4,
    width: '57%',
  },
  invoice: {
    flexDirection: 'row',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    marginVertical: -4,
    width: '70%',
  },
  txId: {
    fontSize: 16,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontWeight: '500',
    lineHeight: 28,
    color: GlobalStyles.base.black,
    width: '80%',
    textAlign: 'right',
    alignSelf: 'center',
  },
});
