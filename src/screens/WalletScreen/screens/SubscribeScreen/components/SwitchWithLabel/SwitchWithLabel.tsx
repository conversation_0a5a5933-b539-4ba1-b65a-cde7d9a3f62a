import React from 'react';
import {Switch, TouchableOpacity, View} from 'react-native';
import FixedText from '../../../../../../components/FixedText/FixedText';
import GlobalStyles from '../../../../../../constants/GlobalStyles';
import {navigate} from '../../../../../../navigation/utils/navigation';
import {TSwitchWithLabelProps} from '../_types_';
import {styles} from './styles';
import {getFontSize} from '../../../../../../utils/parsing';

/// DEV: only used in the Subscribe screen, should be refactored
const SwitchWithLabel: React.FC<TSwitchWithLabelProps> = ({
  disabled,
  label,
  isEnabled,
  handleToggleSwitch,
}) => {
  const renderLabel = () => {
    const privacyPolicyRegex = /(Privacy policy|Политиката за поверителност)/i;
    const parts = label.split(privacyPolicyRegex);

    if (parts.length > 1) {
      return (
        <View style={{flexDirection: 'row', flexWrap: 'wrap', alignItems: 'center'}}>
          <FixedText style={{color: GlobalStyles.base.white, fontSize: getFontSize(17)}}>
            {parts[0]}
          </FixedText>
          <TouchableOpacity
            onPress={() => navigate('Settings', {screen: 'TermsOfService'})}
          >
            <FixedText
              style={{
                color: GlobalStyles.base.white,
                textDecorationLine: 'underline',
                fontSize: getFontSize(17),
              }}
            >
              {parts[1]}
            </FixedText>
          </TouchableOpacity>
        </View>
      );
    } else {
      return (
        <FixedText style={{color: GlobalStyles.base.white, fontSize: getFontSize(17)}}>
          {label}
        </FixedText>
      );
    }
  };

  return (
    <View style={styles.switchWithLabelContainer}>
      <TouchableOpacity
        onPress={handleToggleSwitch}
        style={{flexDirection: 'row', alignItems: 'center'}}
      >
        <Switch
          disabled={disabled}
          trackColor={{
            false: GlobalStyles.gray.gray600,
            true: GlobalStyles.success.success500,
          }}
          thumbColor={GlobalStyles.base.white}
          onValueChange={handleToggleSwitch}
          ios_backgroundColor={GlobalStyles.gray.gray600}
          value={isEnabled}
        />
        <View
          style={{marginLeft: 10, flexShrink: 1, flexDirection: 'row', flexWrap: 'wrap'}}
        >
          {renderLabel()}
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default SwitchWithLabel;
