import React from 'react';
import {TouchableOpacity, View} from 'react-native';
import ArrowLeft from '../../../../../../assets/icons/arrow left white.svg';
import X from '../../../../../../assets/icons/x.svg';
import FixedText from '../../../../../../components/FixedText/FixedText';
import GlobalStyles from '../../../../../../constants/GlobalStyles';
import {styles} from './styles';

const ArrowLeftSvg = () => <ArrowLeft width={34} height={34} style={{marginLeft: 5}} />;

const XSvg = () => <X width={34} height={34} />;

type TopNavigationProps = {
  screenTitle: string;
  leftIcon?: boolean;
  leftIconAction?: () => void;
  rightIcon?: boolean;
  rightIconAction?: () => void;
};

const TopNavigation: React.FC<TopNavigationProps> = ({
  screenTitle,
  leftIcon,
  rightIcon,
  leftIconAction,
  rightIconAction,
}) => {
  return (
    <View
      style={{
        display: 'flex',
        padding: 16,
        flexDirection: 'column',
        position: 'relative',
        backgroundColor: GlobalStyles.primary.primary500,
      }}
    >
      <View style={styles.container}>
        {leftIcon ? (
          <TouchableOpacity style={styles.iconContainerLeft} onPress={leftIconAction}>
            <ArrowLeftSvg />
          </TouchableOpacity>
        ) : null}
        <View style={screenTitle ? styles.show : styles.hide}>
          <FixedText style={styles.title}>{screenTitle}</FixedText>
        </View>
        {rightIcon ? (
          <TouchableOpacity style={styles.iconContainerRight} onPress={rightIconAction}>
            <XSvg />
          </TouchableOpacity>
        ) : (
          <></>
        )}
      </View>
    </View>
  );
};

export default TopNavigation;
