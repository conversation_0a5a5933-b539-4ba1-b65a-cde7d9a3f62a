import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    height: 40,
    display: 'flex',
    marginBottom: -8,
  },
  iconContainerLeft: {
    display: 'flex',
    width: 100,
    height: 30,
    alignItems: 'flex-start',
    gap: 10,
    flexShrink: 0,
    position: 'absolute',
    left: 0,
  },
  hide: {
    display: 'none',
  },
  show: {
    display: 'flex',
  },
  title: {
    color: GlobalStyles.base.white,
    textAlign: 'center',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 20,
    fontStyle: 'normal',
    fontWeight: '600',
    lineHeight: 34,
  },
  iconContainerRight: {
    display: 'flex',
    width: 100,
    height: 34,
    alignItems: 'flex-end',
    position: 'absolute',
    right: 0,
    gap: 10,
  },
});
