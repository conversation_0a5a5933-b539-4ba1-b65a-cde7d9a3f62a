import React from 'react';
import {useTranslation} from 'react-i18next';
import {TextInput} from 'react-native';
import {TEmailInputProps} from '../_types_.ts';
import {styles} from './styles.ts';

const EmailInput: React.FC<TEmailInputProps> = ({email, setEmail}) => {
  const {t} = useTranslation();

  return (
    <TextInput
      placeholder={t('subscribe.email')}
      placeholderTextColor={'#b0b0b0'}
      style={styles.emailTextInputContainer}
      value={email}
      onChangeText={(text) => setEmail(text)}
      returnKeyType="done"
    />
  );
};

export default EmailInput;
