import React, {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Dimensions, View, Modal} from 'react-native';
import FixedText from '../../../../../../components/FixedText/FixedText';
import styles from './styles';
import {TAgreePopUpDialogProps} from '../_types_';
import MButton from '@/components/MButton';

const window = Dimensions.get('window');
const deviceWidth = window.width;
const deviceHeight = window.height;

// @ts-ignore
const AgreePopUpDialog: React.FC<TAgreePopUpDialogProps> = ({visible, onAction}) => {
  const {t} = useTranslation();
  const [isActionButtonEnabled, setIsActionButtonEnabled] = useState(true);
  const [checkBoxDisabled, setCheckBoxDisabled] = useState(false);

  const onContinue = () => {
    console.log('Agree pop up continue clicked');
    setCheckBoxDisabled(true);
    onAction();
  };

  return (
    <Modal visible={visible}>
      <View style={styles.container}>
        <View style={styles.containerContent}>
          <View style={styles.textContainer}>
            <FixedText style={styles.text}>
              {t('subscribe.successful_subscribe')}
            </FixedText>
          </View>
          <View>
            <MButton
              text={t('continue')}
              onPress={onContinue}
              disabled={!isActionButtonEnabled}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default AgreePopUpDialog;
