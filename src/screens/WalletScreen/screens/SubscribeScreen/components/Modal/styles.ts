import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../../../constants/GlobalStyles';

const styles = StyleSheet.create({
  container: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    display: 'flex',
    justifyContent: 'flex-end',
    backgroundColor: GlobalStyles.gray.gray300,
  },
  containerContent: {
    display: 'flex',
    paddingRight: 16,
    paddingBottom: 12.5,
    paddingLeft: 16,
    flexDirection: 'column',
    alignSelf: 'stretch',
    textAlign: 'center',
    marginVertical: 8,
  },
  textContainer: {
    display: 'flex',
    paddingTop: 0,
    paddingRight: 16,
    paddingBottom: 12.5,
    paddingLeft: 16,
    flexDirection: 'column',
    alignSelf: 'stretch',
    textAlign: 'center',
    marginVertical: 8,
  },
  text: {
    textAlign: 'center',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 13,
    color: GlobalStyles.gray.gray900,
    marginVertical: 8,
    lineHeight: 19,
  },
  modalStyle: {
    justifyContent: 'flex-end',
    margin: 0,
  },
});

export default styles;
