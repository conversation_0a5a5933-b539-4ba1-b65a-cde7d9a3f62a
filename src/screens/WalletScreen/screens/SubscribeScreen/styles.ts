import {Dimensions, StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';
import {getFontSize, getScreenAlignment} from '../../../../utils/parsing';

const {height, width} = Dimensions.get('window');
const screenWidth = width * 0.98;

export const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: GlobalStyles.primary.primary500,
    height: '100%',
    width: '100%',
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    width: screenWidth,
  },
  hooksContainer: {
    marginTop: +getScreenAlignment(height, '40', '23'),
    width: '90%',
  },
  hookText: {
    color: GlobalStyles.base.white,
    fontSize: getFontSize(20),
  },
  emailContainer: {
    marginTop: height * 0.03,
    width: '90%',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'white',
  },
  switchContainer: {
    marginTop: +getScreenAlignment(height, '25', '17'),
    width: '90%',
  },
  buttonContainer: {
    width: '90%',
    position: 'absolute',
    bottom: 20,
    paddingHorizontal: 10,
  },
  button: {
    backgroundColor: GlobalStyles.primary.primary400,
    borderRadius: 8,
    borderColor: GlobalStyles.base.white,
    borderWidth: 1,
    height: 50,
    justifyContent: 'center',
  },
  buttonTitleText: {
    color: GlobalStyles.base.white,
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 18,
  },
  disabledButton: {
    backgroundColor: 'transparent',
    borderColor: GlobalStyles.base.white,
  },
  disabledButtonTitleText: {
    color: GlobalStyles.base.white,
    fontFamily: GlobalStyles.fonts.sfPro,
  },
});
