import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  notificationsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    display: 'flex',
    alignSelf: 'center',
    width: '100%',
    marginBottom: 90,
  },
  titleText: {
    fontSize: 16,
    lineHeight: 25,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  titleContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  listContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
  },
  container: {
    marginBottom: -10,
    display: 'flex',
    width: '100%',
    height: '100%',
  },
  noAssetContainer: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    height: 120,
  },
  topContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 'auto',
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 8,
    paddingHorizontal: 10,
    width: '100%',
    paddingVertical: 10,
  },
  tokenLogoContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: GlobalStyles.primary.primary100,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tokenLogo: {
    width: 40,
    height: 40,
  },
  tokenInfoContainer: {
    display: 'flex',
    marginRight: 'auto',
  },
  tokenName: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
    marginLeft: 20,
    paddingVertical: 5,
  },
});
