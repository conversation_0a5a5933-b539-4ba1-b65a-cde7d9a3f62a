import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    marginVertical: 4,
    borderRadius: 8,
    paddingHorizontal: 12,
    width: '100%',
    paddingVertical: 10,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  notificationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  leftContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '500',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
  body: {
    fontSize: 14,
    lineHeight: 21,
    fontWeight: '400',
    fontFamily: GlobalStyles.fonts.sfPro,
    color: GlobalStyles.base.black,
    fontStyle: 'normal',
  },
});
