import {useIsFocused} from '@react-navigation/native';
import {NotificationInstance} from '../../../../../services/BackendServices';
import {useEffect} from 'react';
import {View} from 'react-native';
import {styles} from './styles';
import FixedText from '../../../../../components/FixedText/FixedText';
import {Notification} from '../notificationsUtils';

type NotificationComponentProps = {
  index: number;
  notification: Notification;
};
const NotificationComponent = ({index, notification}: NotificationComponentProps) => {
  const viewFocused = useIsFocused();

  useEffect(() => {
    const update = async () => {
      await NotificationInstance.post(
        `/tx/update-notification-status/${notification._id}`,
        {
          newStatus: 'read',
        },
      );
    };
    if (notification.status !== 'read') {
      update();
    }
  }, [viewFocused]);

  return (
    <View
      style={{
        ...styles.container,
        ...{backgroundColor: notification.status == 'read' ? '#f5f5f5' : 'white'},
      }}
      key={index}
    >
      <View key={notification.title} style={styles.notificationContainer}>
        <View style={styles.leftContainer}>
          <View style={styles.infoContainer}>
            <FixedText style={styles.title}>{notification.title}</FixedText>
            <FixedText style={styles.body}>{notification.body}</FixedText>
          </View>
        </View>
      </View>
    </View>
  );
};

export default NotificationComponent;
