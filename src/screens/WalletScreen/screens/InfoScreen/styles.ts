import { StyleSheet } from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  safeArea: { backgroundColor: GlobalStyles.gray.gray300 },
  container: {
    borderRadius: 8,
    flexDirection: 'column',
    alignSelf: 'center',
    display: 'flex',
    width: '100%',
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    paddingTop: 20,
    marginTop: 10,
  },
  logoContainer: {
    width: '100%',
    height: 84,
    borderRadius: 42,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    marginTop: -20,
  },
  textContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignSelf: 'center',
    width: '90%',
  },
  text: {
    flex: 1,
    textAlign: 'left',
    fontSize: 17,
    fontWeight: '400',
    lineHeight: 22,
    color: GlobalStyles.gray.gray900,
    marginBottom: 20,
  },
  boldText: {
    flex: 1,
    textAlign: 'left',
    fontSize: 17,
    fontWeight: '500',
    lineHeight: 22,
    color: GlobalStyles.gray.gray900,
    marginBottom: 20,
  },
});
