import {compact} from 'lodash';
import {NativeModules} from 'react-native';
import {OneSignal} from 'react-native-onesignal';

import {AuthAddresses, AuthUserWallet} from '@/types/authTypes';
import {ChainsOrder} from '../../constants/Chains';
import {NotificationInstance} from '../../services/BackendServices';
import WalletService from '../../services/WalletService';

const {Enumerations} = require('@AssetifyNet/cryptoapis-kms');
const {WalletManagerBridge} = NativeModules;

export const checkNotifSubscription = async (address: string) => {
  try {
    const id = await OneSignal.User.pushSubscription.getIdAsync();
    if (id === null) {
      return;
    }

    const res = await NotificationInstance.get(`/device-data/${address}`);

    const tokens = res.data;

    // if token is not in the list, add it
    if (!tokens.includes(id) && id != null) {
      await NotificationInstance.post('/device-data', {
        btcAddress: address,
        deviceToken: id,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export const checkSolanaWallet = async (
  wallets: AuthUserWallet[],
  addresses: AuthAddresses,
) => {
  try {
    const solanaWallet = wallets.find((wallet: any) => {
      return wallet !== null && wallet !== undefined && wallet.blockchain === 'solana';
    });

    if (solanaWallet) {
      return null;
    }

    const createdSolanaWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
      wallets[0]?.mnemonic,
      'solana',
    );

    if (!createdSolanaWallet || !createdSolanaWallet.address) {
      console.error('Failed to create Solana wallet');
      return null;
    }

    await new WalletService().walletLogger([
      {
        btcAddress: addresses[0] && 'address' in addresses[0] ? addresses[0].address : '',
        chain: createdSolanaWallet.blockchain,
        xpub: createdSolanaWallet.zPub
          ? createdSolanaWallet.zPub.accountXpub
          : createdSolanaWallet.xPubsList[0].accountXpub,
        address: createdSolanaWallet.address,
        network: createdSolanaWallet.network,
      },
    ]);

    let walletsWithSolana = [createdSolanaWallet, ...wallets];
    let sortedWallets = sortWallets(walletsWithSolana);
    let solanaIndex = sortedWallets.findIndex(
      (wallet: any) => wallet?.blockchain === 'solana',
    );

    addresses.splice(solanaIndex, 0, {
      address: createdSolanaWallet.address,
      chain: createdSolanaWallet.blockchain,
      privateKey: createdSolanaWallet.privateKey,
      publicKey: createdSolanaWallet.publicKey,
      qrCode: '',
    });

    for (let i = 0; i < sortedWallets.length; i++) {
      if (walletsWithSolana[i] !== null && walletsWithSolana[i] !== undefined) {
        if (sortedWallets[i]?.blockchain !== walletsWithSolana[i]?.blockchain) {
          return {wallets: sortedWallets, addresses};
        }
      }
    }

    return {wallets: sortedWallets, addresses};
  } catch (error) {
    console.error('Error creating Solana wallet:', error);
    return null;
  }
};

export const checkWallets = async (
  wallets: AuthUserWallet[],
  addresses: AuthAddresses,
) => {
  const avalancheWallets = wallets.find((wallet: any) => {
    if (wallet !== undefined) {
      return wallet.blockchain === 'avalanche';
    }
    return false;
  });
  if (avalancheWallets !== undefined && avalancheWallets) {
    return null;
  }

  const avalancheWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
    wallets[0]?.mnemonic,
    Enumerations.Blockchains.AVALANCHE,
  );
  await new WalletService().walletLogger([
    {
      //@ts-ignore
      btcAddress: addresses[0] && 'address' in addresses[0] ? addresses[0].address : '',
      chain: avalancheWallet.blockchain,
      xpub: avalancheWallet.zPub
        ? avalancheWallet.zPub.accountXpub
        : avalancheWallet.xPubsList[0].accountXpub,
      address: avalancheWallet.address,
      network: avalancheWallet.network,
    },
  ]);

  let walletWithAvalanche = [avalancheWallet, ...wallets];
  let sortedWallets = sortWallets(walletWithAvalanche);
  let avalancheIndex = sortedWallets.findIndex(
    (wallet: any) => wallet?.blockchain === 'avalanche',
  );

  addresses.splice(avalancheIndex, 0, {
    address: avalancheWallet.address,
    chain: avalancheWallet.blockchain,
    privateKey: avalancheWallet.privateKey,
    publicKey: avalancheWallet.publicKey,
    qrCode: '',
  });

  for (let i = 0; i < sortedWallets.length; i++) {
    if (walletWithAvalanche[i] !== null && walletWithAvalanche[i] !== undefined) {
      if (sortedWallets[i]?.blockchain !== walletWithAvalanche[i]?.blockchain) {
        return {wallets: sortedWallets, addresses};
      }
    }
  }

  return null;
};

const sortWallets = (wallets: AuthUserWallet[]) => {
  const validWallets = wallets.filter(
    (wallet) => wallet !== null && wallet !== undefined,
  );
  console.log('wallets', validWallets);

  let mandatoryWallets = validWallets.filter(
    (wallet: any) =>
      wallet !== null && wallet.blockchain && ChainsOrder.includes(wallet.blockchain),
  );
  const optionalWallets = validWallets.filter(
    (wallet: any) =>
      wallet !== null && wallet.blockchain && !ChainsOrder.includes(wallet.blockchain),
  );

  mandatoryWallets.sort((a: any, b: any) => {
    return ChainsOrder.indexOf(a.blockchain) - ChainsOrder.indexOf(b.blockchain);
  });

  mandatoryWallets = compact(mandatoryWallets);

  return [...mandatoryWallets, ...optionalWallets];
};
