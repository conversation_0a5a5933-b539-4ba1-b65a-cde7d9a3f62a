import {Dimensions, StyleSheet} from 'react-native';
import GlobalStyles from '../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  assetsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    display: 'flex',
    alignSelf: 'center',
    width: '90%',
    marginBottom: 72,
  },
  balance: {
    backgroundColor: GlobalStyles.base.white,
    borderRadius: 10,
    overflow: 'hidden',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    display: 'flex',
    width: '90%',
    elevation: 4,
    marginBottom: 4,
    shadowColor: GlobalStyles.gray.gray900shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  bottomContainer: {
    backgroundColor: '#e6e6e6',
    borderColor: GlobalStyles.gray.gray600,
    borderWidth: 0.5,
    position: 'absolute',
    bottom: 0,
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    borderTopRightRadius: 18,
    borderTopLeftRadius: 18,
  },
  titleNewsContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    paddingHorizontal: 8,
    height: Dimensions.get('window').width * 0.41,
    marginVertical: 10,

    width: Dimensions.get('window').width * 0.9,
  },
  titleNewsImage: {
    alignSelf: 'center',
    borderRadius: 8,
  },
  titleNewsTextContainer: {
    display: 'flex',
    flex: 1,
    justifyContent: 'flex-end',
    alignSelf: 'flex-end',
    alignItems: 'flex-start',
    paddingHorizontal: 10,
    paddingTop: 10,
    width: '80%',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 8,
    bottom: 16,
  },
  titleNewsTitle: {
    fontSize: 22,
    lineHeight: 22,
    fontWeight: '600',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontStyle: 'normal',
    top: 24,
    color: GlobalStyles.base.white,
    paddingLeft: 6,
  },
});
