import {useCallback, useRef, useState} from 'react';
import {NativeModules} from 'react-native';

import {ChainToSymbol, SymbolToChain} from '@/constants/Chains';
import {useAppDispatch} from '@/hooks/redux';
import {PriceFetchingInstance} from '@/services/BackendServices';
import WalletService from '@/services/WalletService';
import {setUser, setUserAddresses} from '@/storage/actions/authActions';
import {AuthAddress, AuthAddresses, AuthUser, AuthUserWallet} from '@/types/authTypes';

const {WalletManagerBridge} = NativeModules;

export type SecondaryAsset = {
  tokenSymbol: string;
  tokenPrice: number;
};

export const useSecondaryAssets = (
  user: AuthUser,
  userAddresses: AuthAddresses,
  secondaryAssetsList: string[] = ['BCH', 'LTC', 'DOGE', 'SOL'],
) => {
  const dispatch = useAppDispatch();
  const isExecuted = useRef(false);

  const [loading, setLoading] = useState(false);

  const getAvailableSecondaryAssets = async () => {
    setLoading(true);
    try {
      // Filter out assets that are already in the user's wallet
      let availableAssets = secondaryAssetsList
        .filter((symbol) => {
          return !user.wallet?.some((wallet) => {
            console.log('wallet', wallet);
            return wallet && ChainToSymbol[wallet.blockchain]?.toUpperCase() === symbol;
          });
        })
        .map((symbol) => ({
          tokenSymbol: symbol,
          tokenPrice: 0,
        }));

      console.log('availableAssets', user.wallet);

      // Fetch prices for available assets
      const assetsWithPrices = await Promise.all(
        availableAssets.map(async (asset) => {
          try {
            const res = await PriceFetchingInstance.get(`/price/${asset.tokenSymbol}`);
            return {
              ...asset,
              tokenPrice: parseFloat(res.data.price.price),
            };
          } catch (error) {
            console.error(`Error fetching price for ${asset.tokenSymbol}:`, error);
            return asset;
          }
        }),
      );

      return assetsWithPrices;
    } catch (error) {
      console.error('Error getting available secondary assets:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const createSecondaryWallets = useCallback(
    async (selectedAssets: SecondaryAsset[]) => {
      if (isExecuted.current) {
        return;
      }

      setLoading(true);
      try {
        let updatedUser: AuthUserWallet[] = [...user.wallet];
        let addresses: AuthAddresses = [...userAddresses];
        let loggerRequest: any[] = [];

        await Promise.all(
          selectedAssets.map(async (asset) => {
            const createdWallet = await WalletManagerBridge.createHDWalletFromMnemonic(
              user.wallet[0]?.mnemonic,
              SymbolToChain[asset.tokenSymbol.toLowerCase()],
            );

            const address: AuthAddress = {
              address: createdWallet.address,
              privateKey: createdWallet.privateKey,
              publicKey: createdWallet.publicKey,
              chain: createdWallet.blockchain,
              qrCode: '',
            };

            const wallet: AuthUserWallet = {
              seed: createdWallet.seed,
              zPub: createdWallet.zPub ? createdWallet.zPub : createdWallet.xPubsList[0],
              mnemonic: createdWallet.mnemonic,
              blockchain: createdWallet.blockchain,
              network: createdWallet.network,
            };

            const addressFix = address.address.includes('bitcoincash:')
              ? address.address.split('bitcoincash:')[1]
              : address.address;

            addresses.push(address);
            updatedUser.push(wallet);

            loggerRequest.push({
              btcAddress: userAddresses[0]?.address,
              chain: createdWallet.blockchain,
              xpub: createdWallet.zPub
                ? createdWallet.zPub.accountXpub
                : createdWallet.xPubsList[0].accountXpub,
              address: addressFix,
              network: createdWallet.network,
            });
          }),
        );

        await new WalletService().walletLogger(loggerRequest);
        dispatch(setUser({...user, wallet: updatedUser}));
        dispatch(setUserAddresses(addresses));

        isExecuted.current = true;
        return true;
      } catch (error) {
        console.error('Error creating secondary wallets:', error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [user, userAddresses, dispatch],
  );

  return {
    loading,
    getAvailableSecondaryAssets,
    createSecondaryWallets,
  };
};
