import {StyleSheet} from 'react-native';
import GlobalStyles from '../../../../constants/GlobalStyles';

export const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginVertical: 18,
  },
  title: {
    color: GlobalStyles.base.black,
    textAlign: 'center',
    fontFamily: GlobalStyles.fonts.sfPro,
    fontSize: 18,
    fontStyle: 'normal',
    fontWeight: 'bold',
    lineHeight: 22,
  },
  iconContainerLeft: {
    display: 'flex',
    width: 40,
    height: 40,
    position: 'absolute',
    left: 21,
    gap: 10,
    flexShrink: 0,
  },
  iconContainerRight: {
    position: 'absolute',
    display: 'flex',
    flexDirection: 'row',
    width: 100,
    height: 30,
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 10,
    right: 20,
  },
  icon: {
    textAlign: 'center',
    fontSize: 32,
    fontStyle: 'normal',
    fontWeight: '400',
    lineHeight: 19,
  },
  hide: {
    display: 'none',
  },
  show: {
    display: 'flex',
    justifyContent: 'center',
  },
});
