import {TouchableOpacity, View} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Logo from '../../../../assets/logo/Logo.svg';
import Notifications from '../../../../assets/nav/notifications.svg';
import Settings from '../../../../assets/nav/settings.svg';
import FixedText from '../../../../components/FixedText/FixedText';
import {styles} from './styles';
import SafeAreaInset from '@/components/SafeAreaInset';

const LogoSvg = () => <Logo width={37} height={37} />;
const NotificationsSvg = () => <Notifications width={30} height={30} />;
const SettingsSvg = () => <Settings width={30} height={30} />;

type TopNavigationProps = {
  handleMenu: () => void;
  handleSettings: () => void;
  handleNotifications: () => void;
  title: string;
};

const TopNavigation = ({
  handleMenu,
  handleSettings,
  handleNotifications,
  title,
}: TopNavigationProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.iconContainerLeft}>
        <TouchableOpacity onPress={handleMenu}>
          <LogoSvg />
        </TouchableOpacity>
      </View>
      <View style={title ? styles.show : styles.hide}>
        <FixedText style={styles.title}>{title}</FixedText>
      </View>
      <View style={styles.iconContainerRight}>
        <TouchableOpacity onPress={handleNotifications}>
          <NotificationsSvg />
        </TouchableOpacity>
        <TouchableOpacity onPress={handleSettings}>
          <SettingsSvg />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default TopNavigation;
