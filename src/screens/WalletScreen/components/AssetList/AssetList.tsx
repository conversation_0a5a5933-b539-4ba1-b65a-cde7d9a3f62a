import {useTranslation} from 'react-i18next';
import {TouchableOpacity, View} from 'react-native';
import FixedText from '../../../../components/FixedText/FixedText';
import WalletAsset from './components/WalletAsset/WalletAsset';
import {styles} from './styles';
import {useSelector} from 'react-redux';
import {useMemo} from 'react';
import {AuthAsset, AuthTokenPrices} from '@/types/authTypes';

type AssetListProps = {
  assets: Array<AuthAsset>;
  // calculatedPrices: AuthCalculatedPrices;
  // stableCoins: AuthStableCoins;
  tokenPrices: AuthTokenPrices;
  handleAssetPress: (asset: AuthAsset) => void;
  handleStableCoinPress: (asset: AuthAsset, stableCoin: string) => void;
  blur: boolean;
  setBlur: (value: boolean) => void;
  currency: string;
};

const AssetList = ({
  assets,
  // calculatedPrices,
  tokenPrices,
  // stableCoins,
  handleAssetPress,
  handleStableCoinPress,
  blur,
  setBlur,
  currency,
}: AssetListProps) => {
  const {t} = useTranslation();

  // prettier-ignore
  const calculatedPrices = useSelector((state: any) => state.auth.calculatedPrices)
  // prettier-ignore
  const memoizedCalculatedPrices = useMemo(() => calculatedPrices, [calculatedPrices])

  const stableCoins = useSelector((state: any) => state.auth.stableCoins);
  const memoizedStableCoins = useMemo(() => stableCoins, [stableCoins]);

  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <FixedText style={styles.titleText}>{t('wallet.assets')}</FixedText>
        <TouchableOpacity onPress={() => setBlur(!blur)}>
          <FixedText style={styles.seeAllText}>
            {blur ? t('wallet.showBalance') : t('wallet.hideBalance')}
          </FixedText>
        </TouchableOpacity>
      </View>
      <View style={styles.listContainer}>
        {assets.map((item, index) => {
          if (item.tokenSymbol === 'N/A') return null;
          return (
            <View key={`${item}-${index}`}>
              <WalletAsset
                key={index}
                index={index}
                asset={item}
                calculatedPrices={memoizedCalculatedPrices[index]}
                tokenPrices={tokenPrices[index]}
                stableCoins={memoizedStableCoins[index]}
                handleAssetPress={handleAssetPress}
                handleStableCoinPress={handleStableCoinPress}
                blur={blur}
                currency={currency}
              />
            </View>
          );
        })}
      </View>
    </View>
  );
};

export default AssetList;
