import type {AuthAsset} from '@/types/authTypes';
import FixedText from '@/components/FixedText/FixedText';
import Logo from '@/components/Logo/AssetLogo';
import {ChainToTokens, StableChains} from '@/constants/Chains';
import {useCombinedCurrencyChecker} from '@/screens/CurrencySpecificScreen/hooks/useCombined';
import {NameParsing, parsePrice, symbolToSvg} from '@/utils/parsing';
import {useIsFocused} from '@react-navigation/native';
import {useCallback, useEffect, useMemo, useState} from 'react';
import {TouchableOpacity, View} from 'react-native';
import {styles} from './styles';
import {toggleStableCoins} from './walletAssetUtils';

type WalletAssetProps = {
  asset: AuthAsset;
  calculatedPrices: string[];
  tokenPrices: string[];
  stableCoins: string[];
  handleAssetPress: (asset: AuthAsset) => void;
  handleStableCoinPress: (asset: AuthAsset, stableCoin: string) => void;
  currency: string;
  index: number;
};

type Token = {
  tokenSymbol: string;
  tokenName: string;
};

const convertNetworkTicket = (network: string): string => {
  switch (network.toLowerCase()) {
    case 'trx':
      return 'tron';
    case 'xrp':
      return 'ripple';
    case 'bitcoin-cash':
      return 'bitcoin_cash';
    case 'dogecoin':
      return 'doge';
    case 'binance-smart-chain':
      return 'binance_smart_chain';
    default:
      return network.toLowerCase();
  }
};

const RampAsset = ({
  asset,
  calculatedPrices,
  tokenPrices,
  stableCoins,
  handleAssetPress,
  handleStableCoinPress,
  currency,
  index,
}: WalletAssetProps) => {
  const viewFocused = useIsFocused();
  const [expanded, setExpanded] = useState<boolean>(false);
  const [constCalculatedPrices, setConstCalculatedPrices] = useState<string[]>(
    calculatedPrices || [],
  );
  const [combinedCalcPrice, setCombinedCalcPrice] = useState<string>('0.00');
  const [constTokenPrices, setConstTokenPrices] = useState<string[]>(tokenPrices || []);
  const [constStableCoins, setConstStableCoins] = useState<string[]>(stableCoins || []);

  const {buy} = useCombinedCurrencyChecker('', '');

  useEffect(() => {
    setConstCalculatedPrices(calculatedPrices || []);
    setConstTokenPrices(tokenPrices || []);
    setConstStableCoins(stableCoins || []);

    let calcPrice = 0;
    if (calculatedPrices?.length) {
      for (let i = 0; i < calculatedPrices.length; i++) {
        if (calculatedPrices[i]) {
          calcPrice += parseFloat(calculatedPrices[i]);
        }
      }
    }
    setCombinedCalcPrice(calcPrice.toFixed(2));
  }, [viewFocused, calculatedPrices, tokenPrices, stableCoins]);

  // Check if ChainToTokens contains the blockchain key
  const tokenList = useMemo(
    () => ChainToTokens[asset.blockchain] || [],
    [asset.blockchain],
  );

  // Create a memoized function to check token support
  const isTokenSupported = useCallback(
    (token: Token) => {
      if (!buy?.isSymbolSupported) return false;
      const convertedNetwork = convertNetworkTicket(asset.blockchain);
      return buy.isSymbolSupported(convertedNetwork, token.tokenSymbol);
    },
    [asset.blockchain, buy],
  );

  // Filter supported tokens once
  const supportedTokens = useMemo(
    () => tokenList.filter(isTokenSupported),
    [tokenList, isTokenSupported],
  );

  const renderTokenInfo = useCallback(
    (token: Token, index: number) => {
      const amount = constStableCoins[index] || '0.00';

      return (
        <View key={`${asset.blockchain}-${token.tokenSymbol}-${index}`}>
          <View style={styles.shortLine} />
          <View style={{marginLeft: 50}}>
            <View style={styles.horizontalLine} />

            <TouchableOpacity
              style={styles.assetContainer}
              onPress={() => handleStableCoinPress(asset, token.tokenSymbol)}
            >
              <View style={styles.leftContainer}>
                <View style={styles.stableCoinLogoContainer}>
                  <Logo name={symbolToSvg(token.tokenSymbol + asset.tokenSymbol)} />
                </View>
                <View style={styles.tokenInfoContainer}>
                  <FixedText style={styles.tokenName}>{token.tokenSymbol}</FixedText>
                  <FixedText style={styles.tokenPrice}>{token.tokenName}</FixedText>
                </View>
              </View>
              <View>
                <View style={styles.rightContainer}>
                  <FixedText style={styles.tokenAmount}>
                    {amount} {token.tokenSymbol}
                  </FixedText>
                  <FixedText style={styles.calculatedPrice}>
                    {currency === 'EUR' ? '€' : '$'}
                    {constCalculatedPrices
                      ? constCalculatedPrices[index + 1] != undefined
                        ? parseFloat(constCalculatedPrices[index + 1]).toFixed(2)
                        : '0.00'
                      : '0.00'}
                  </FixedText>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      );
    },
    [asset, constStableCoins, constCalculatedPrices, currency, handleStableCoinPress],
  );

  return (
    <View style={styles.container} key={index}>
      <TouchableOpacity
        key={asset.title}
        style={styles.assetContainer}
        onPress={() => handleAssetPress(asset)}
      >
        <View style={styles.leftContainer}>
          <TouchableOpacity
            style={styles.tokenLogoContainer}
            onPress={() => toggleStableCoins(setExpanded, expanded)}
            disabled={!['ethereum'].includes(asset.title)}
          >
            <Logo name={symbolToSvg(asset.tokenSymbol)} />

            {!expanded &&
              ['ethereum'].includes(asset.title) &&
              ChainToTokens[asset.title]?.map((token, index) => {
                const offset =
                  asset.title === 'solana'
                    ? (index + 1) * 2.5 // Positive offset for Solana (shadows below)
                    : index * 2.5 - 7.5; // Original calculation for other assets
                const opacity = 0.6 - index * 0.2;
                const zIndex = -(index + 1);
                return (
                  <View
                    key={`${asset.title}-${token.tokenSymbol}-${index}`}
                    style={{
                      position: 'absolute',
                      height: '100%',
                      width: '100%',
                      zIndex: zIndex,
                      top: offset,
                      opacity: opacity,
                    }}
                  >
                    <Logo
                      name={symbolToSvg(
                        asset.tokenSymbol === 'BSC' ? 'BNB' : asset.tokenSymbol,
                      )}
                    />
                  </View>
                );
              })}
          </TouchableOpacity>
          <View style={styles.tokenInfoContainer}>
            <FixedText style={styles.tokenName}>{NameParsing(asset.title)}</FixedText>
            <FixedText style={styles.tokenPrice}>
              {currency === 'EUR' ? '€' : '$'}
              {constTokenPrices ? parsePrice(constTokenPrices[0]) : '0.00'}
            </FixedText>
          </View>
        </View>
        <View>
          <View style={styles.rightContainer}>
            <FixedText style={styles.tokenAmount}>
              {parsePrice(asset.amount)} {asset.tokenSymbol}
            </FixedText>
            <FixedText style={styles.calculatedPrice}>
              {currency === 'EUR' ? '€' : '$'}
              {!expanded && ['ethereum'].includes(asset.title) && constCalculatedPrices
                ? parsePrice(parseFloat(combinedCalcPrice).toFixed(2))
                : constCalculatedPrices != undefined
                ? parsePrice(parseFloat(constCalculatedPrices[0]).toFixed(2))
                : '0.00'}
            </FixedText>
          </View>
        </View>
      </TouchableOpacity>
      {expanded && StableChains.includes(asset.title) && (
        <View style={{overflow: 'hidden'}}>
          <View
            style={{
              ...styles.verticalLine,
              ...{height: 51 + (supportedTokens.length - 1) * 69},
            }}
          />

          {supportedTokens.map((token, index) => renderTokenInfo(token, index))}
        </View>
      )}
    </View>
  );
};

export default RampAsset;
