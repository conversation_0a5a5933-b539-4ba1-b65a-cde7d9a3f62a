import Clipboard from '@react-native-clipboard/clipboard';
import {useRoute} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';

import {AuthAsset, AuthTransaction} from '@/types/authTypes';
import {showInfoToast} from '@/utils/toast';
import Copy from '../../assets/icons/copy.svg';
import FixedText from '../../components/FixedText/FixedText';
import {ChainToSymbol, coinToExplorer} from '../../constants/Chains';
import GlobalStyles from '../../constants/GlobalStyles';
import {openLink} from '../../utils/in-app-browser';
import {TrailingZeroRegex} from '../../utils/parsing';
import {styles} from './styles';

type TxDetailsProps = {
  asset: AuthAsset;
  stableCoin: string;
  stableCoinIndex: number;
  stableCoinAmount: string;
  tx: AuthTransaction;
  address: any;
  tokenPrices: string;
  calculatedPrices: string;
};

const CopySvg = () => (
  <Copy width={30} height={30} style={styles.icon} fill={GlobalStyles.gray.gray900} />
);

const handleBCHPrefix = (address: string | undefined) => {
  if (address && address.includes('bitcoincash:')) {
    return address.split('bitcoincash:')[1];
  } else if (address) {
    return address;
  } else {
    return '';
  }
};

const TxDetailsScreen: React.FC = (props: any) => {
  const {t} = useTranslation();
  const route = useRoute() as {params: TxDetailsProps};
  const tx = route.params.tx;
  const asset = route.params.asset;
  const userAddress =
    route.params.address.chain === 'kaspa'
      ? route.params.address.addresses[0].address
      : route.params.address.address;

  const senderAddress =
    tx.tokensTransfered.length > 0
      ? handleBCHPrefix(tx.tokensTransfered[0]?.senderAddress)
      : handleBCHPrefix(tx.senders[0].address);
  const recipientAddress =
    tx.tokensTransfered.length > 0
      ? handleBCHPrefix(tx.tokensTransfered[0]?.recipientAddress)
      : handleBCHPrefix(tx.recipients[0].address);

  const squeezeAddress = (address: string) => {
    return handleBCHPrefix(address).substring(0, 4) + '...' + address.substring(38, 42);
  };

  const copyAddress = (address: string) => {
    Clipboard.setString(handleBCHPrefix(address));
    showInfoToast(t('successCopy'));
  };

  const amount = (tx: AuthTransaction) => {
    if (tx.tokensTransfered.length > 0)
      return tx.tokensTransfered[0].confirmedBalance
        .replace(TrailingZeroRegex, '')
        .slice(0, tx.tokensTransfered[0].confirmedBalance.indexOf('.') + 11);
    if (tx.recipients.length > 0) {
      if (tx.recipients[0].amount == tx.fee.amount) return '0.00';
      else {
        let amount = tx.recipients[0].amount.slice(
          0,
          tx.recipients[0].amount.indexOf('.') + 11,
        );
        let amountBefore = amount.split('.')[0];
        let amountAfter = amount.split('.')[1];
        return amountAfter
          ? amountBefore + '.' + amountAfter.replace(TrailingZeroRegex, '')
          : amountBefore;
      }
    } else {
      return '0.00';
    }
  };

  return (
    <>
      <View style={GlobalStyles.THEME.mainContainer}>
        <View style={[styles.container, {alignItems: 'flex-end'}]}>
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.type')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {route.params.address.chain === 'kaspa'
                ? route.params.address.addresses
                    .map((address: any) => address.address)
                    .includes(senderAddress)
                  ? 'Sent'
                  : 'Received'
                : userAddress === senderAddress
                ? 'Sent'
                : 'Received'}
            </FixedText>
          </View>

          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.amount')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {amount(tx) +
                ' ' +
                (tx.tokensTransfered.length > 0
                  ? tx.tokensTransfered[0]?.symbol
                  : tx.fee.unit
                  ? tx.fee.unit
                  : ChainToSymbol[tx.chain].toUpperCase())}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.status')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {tx.status === 'confirmed' ? 'Done' : 'Canceled'}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.senderAddr')}
            </FixedText>
            <TouchableOpacity
              onPress={() => copyAddress(senderAddress)}
              style={styles.addresses}
            >
              <FixedText style={styles.valueText} numberOfLines={1}>
                {squeezeAddress(senderAddress)}
              </FixedText>
              <CopySvg />
            </TouchableOpacity>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.receiverAddr')}
            </FixedText>
            <TouchableOpacity
              onPress={() => copyAddress(recipientAddress)}
              style={styles.addresses}
            >
              <FixedText style={styles.valueText} numberOfLines={1}>
                {squeezeAddress(recipientAddress)}
              </FixedText>
              <CopySvg />
            </TouchableOpacity>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.date')}
            </FixedText>
            <FixedText style={styles.valueText}>
              {new Date(tx.timestamp).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              })}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={1}>
              {t('txDetails.fee')}
            </FixedText>
            <FixedText style={styles.valueText} numberOfLines={1}>
              {tx.fee.amount?.replace(TrailingZeroRegex, '') +
                ' ' +
                (tx.fee.unit ? tx.fee.unit : ChainToSymbol[tx.chain].toUpperCase())}
            </FixedText>
          </View>
          <View style={styles.line} />
          <View style={styles.row}>
            <FixedText style={styles.typeText} numberOfLines={2}>
              {t('txDetails.txId')}
            </FixedText>
            <TouchableOpacity
              onPress={() => {
                openLink(
                  coinToExplorer[
                    tx.fee.unit ? tx.fee.unit : ChainToSymbol[tx.chain].toUpperCase()
                  ] + tx.transactionId,
                );
              }}
              style={styles.addresses}
            >
              <FixedText style={styles.txId} numberOfLines={1}>
                {tx.transactionId}
              </FixedText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </>
  );
};

const mapStateToProps = (state: any) => ({
  isConnected: state.common.isConnected,
  user: state.user,
});

export default connect(mapStateToProps, null)(TxDetailsScreen);
