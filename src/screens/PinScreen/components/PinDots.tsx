import GlobalStyles from '@/constants/GlobalStyles';
import React from 'react';
import {StyleSheet, View} from 'react-native';

interface PinDotsProps {
  length: number;
  filledLength: number;
}

const PinDots: React.FC<PinDotsProps> = ({length, filledLength}) => {
  return (
    <View style={styles.container}>
      {Array.from({length}).map((_, index) => (
        <View
          key={index}
          style={[styles.dot, index < filledLength && styles.dotFilled]}
          accessibilityLabel={`PIN digit ${index + 1} ${
            index < filledLength ? 'filled' : 'empty'
          }`}
        />
      ))}
    </View>
  );
};

export default PinDots;

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginHorizontal: 8,
    borderWidth: 1,
    borderColor: GlobalStyles.gray.gray900,
  },
  dotFilled: {
    backgroundColor: GlobalStyles.primary.primary400,
    borderColor: GlobalStyles.primary.primary600,
  },
});
