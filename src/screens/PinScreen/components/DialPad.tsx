import {Dimensions, FlatList, Text, TouchableOpacity, View} from 'react-native';
import Backspace from '../../../assets/icons/backspace.svg';

const BackspaceSVG = () => <Backspace width={40} height={40} />;

const dialPad = [1, 2, 3, 4, 5, 6, 7, 8, 9, '', 0, 'del'];

interface DialPadProps {
  onPress: (value: (typeof dialPad)[number]) => void;
  disabled?: boolean;
}

const DialPad = ({onPress, disabled = false}: DialPadProps) => {
  const {width} = Dimensions.get('window');
  const dialPadSize = width * 0.2;
  const dialPadTextSize = dialPadSize * 0.4;

  return (
    <FlatList
      numColumns={3}
      data={dialPad}
      style={{flexGrow: 0}}
      scrollEnabled={false}
      columnWrapperStyle={{gap: 20}}
      contentContainerStyle={{gap: 20}}
      keyExtractor={(_, index) => index.toString()}
      renderItem={({item}) => {
        return (
          <TouchableOpacity
            onPress={() => onPress(item)}
            disabled={disabled || item === ''}
          >
            <View
              style={{
                width: dialPadSize,
                height: dialPadSize,
                borderRadius: dialPadSize,
                borderWidth: typeof item !== 'number' ? 0 : 1,
                borderColor: disabled ? 'gray' : 'black',
                alignItems: 'center',
                justifyContent: 'center',
                opacity: disabled ? 0.5 : 1,
              }}
            >
              {item == 'del' ? (
                <BackspaceSVG />
              ) : (
                <Text
                  style={{
                    fontSize: dialPadTextSize,
                    fontWeight: 'bold',
                    color: disabled ? 'gray' : 'black',
                  }}
                >
                  {item}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        );
      }}
    />
  );
};

export default DialPad;
