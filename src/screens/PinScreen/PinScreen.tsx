import React, {memo, useCallback, useMemo, useState} from 'react';
import {SafeAreaView, StyleSheet, View} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useAuth} from '@/hooks/redux';
import {
  setBiometricsInProgress,
  setIsAuthenticated,
  setUser,
} from '@/storage/actions/authActions';
import {Assetify} from '@assets/index';
import {BodySSB, Caption} from '@styles/styled-components';
import theme, {colors} from '@/styles/themes';
import DialPad from './components/DialPad';
import PinDots from './components/PinDots';

const MAX_ATTEMPTS = 3;
const PIN_LENGTH = 4;

const PinScreen: React.FC = () => {
  const dispatch = useAppDispatch();

  const {user} = useAuth();
  const correctPin = user.pinCode || '';

  const [repeatPin, setRepeatPin] = useState('');
  const [pin, setPin] = useState('');
  const [attempts, setAttempts] = useState(0);
  const [error, setError] = useState('');

  const validatePin = useCallback((newPin: string, against: string) => {
    if (newPin.length !== PIN_LENGTH) return false;

    return newPin === against;
  }, []);

  const handlePinChange = useCallback(
    (pinInput: string | number | 'del') => {
      if (attempts >= MAX_ATTEMPTS) {
        setError('Too many attempts. Please try again later');
        return;
      }

      if (pinInput === 'del') {
        setPin((prev) => prev.slice(0, -1));
        setError('');
        return;
      }

      setPin((prev) => {
        const newPin = prev + pinInput.toString();

        if (newPin.length === PIN_LENGTH) {
          if (correctPin === '') {
            if (repeatPin.length === PIN_LENGTH) {
              if (validatePin(newPin, repeatPin)) {
                dispatch(setUser({...user, pinCode: newPin}));
                dispatch(setIsAuthenticated(true));
                dispatch(setBiometricsInProgress(false));
              } else {
                setError('PINs do not match. Please try again');
                setRepeatPin('');
                setTimeout(() => setPin(''), 150);
              }
            } else {
              setRepeatPin(newPin);
              setTimeout(() => setPin(''), 150);
            }
          } else {
            if (validatePin(newPin, correctPin)) {
              dispatch(setIsAuthenticated(true));
              dispatch(setBiometricsInProgress(false));
            } else {
              setAttempts((prev) => prev + 1);
              setError('Incorrect PIN. Please try again');
              setTimeout(() => setPin(''), 150);
            }
          }
        }

        return newPin;
      });
    },
    [correctPin, repeatPin, attempts, dispatch, user, validatePin],
  );

  const getTitle = useMemo(() => {
    if (!correctPin) {
      return repeatPin.length !== PIN_LENGTH ? 'Enter new PIN' : 'Repeat PIN';
    }

    return 'Enter your PIN';
  }, [correctPin, repeatPin.length]);

  return (
    <View style={styles.overlay}>
      <SafeAreaView style={styles.root}>
        <View style={styles.content}>
          <View style={styles.headerContainer}>
            <Assetify width={theme.layout.images.xs} height={theme.layout.images.xs} />

            <Caption style={styles.headerDescription}>
              App was locked and needs to be unlocked
            </Caption>
          </View>

          <Caption style={styles.title}>{getTitle}</Caption>

          <View style={styles.pinDotsContainer}>
            <PinDots length={PIN_LENGTH} filledLength={pin.length} />
            <DialPad onPress={handlePinChange} disabled={attempts >= MAX_ATTEMPTS} />
          </View>

          <View
            style={[
              styles.errorContainer,
              {
                paddingVertical: error ? theme.spacing.md : 0,
                backgroundColor: error ? theme.colors.brand.coral : 'transparent',
              },
            ]}
          >
            {error ? <BodySSB style={styles.errorText}>{error}</BodySSB> : null}
          </View>
        </View>
      </SafeAreaView>
    </View>
  );
};

export default memo(PinScreen);

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.light.background,
    zIndex: 999,
  },
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.xl,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.lg,
  },
  headerDescription: {
    textAlign: 'center',
    paddingHorizontal: theme.spacing.md,
    color: GlobalStyles.gray.gray900,
    fontSize: 20,
  },
  title: {
    textAlign: 'center',
  },
  errorContainer: {
    minHeight: 50,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.layout.borderRadius.md,
  },
  errorText: {
    color: GlobalStyles.base.white,
    textAlign: 'center',
  },
  pinDotsContainer: {
    gap: 0,
  },
});
