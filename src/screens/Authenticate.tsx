import {useFocusEffect, useIsFocused} from '@react-navigation/native';
import React, {memo, useCallback, useRef, useState} from 'react';
import {
  AppState,
  AppStateStatus,
  NativeEventSubscription,
  Platform,
  StyleSheet,
  View,
} from 'react-native';
import Animated, {FadeIn, FadeInDown} from 'react-native-reanimated';
import {SafeAreaView} from 'react-native-safe-area-context';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {
  setBiometricsInProgress,
  setIsAuthenticated,
  setShowAuthScreen,
} from '@/storage/actions/authActions';
import {AssetifyWithText} from '@assets/index';
import MButton from '@components/MButton';
import {BodySSB, Footer} from '@styles/styled-components';
import theme from '@/styles/themes';
import {areBiometricsAvailable, triggerBiometrics} from '@utils/biometrics';
// import { getAddressFromPublicKey } from '@solana/kit';

const AnimatedSafeAreaView = Animated.createAnimatedComponent(SafeAreaView);

let initialMount = true;
/**
 * This screen is used as main authentication source of the app.
 */
const Authenticate: React.FC = () => {
  const isFocused = useIsFocused();
  const dispatch = useAppDispatch();

  const appState = useRef<AppStateStatus | null>(null);

  const [attempts, setAttempts] = useState(0);

  const handleBiometrics = useCallback(async () => {
    const result = await triggerBiometrics();

    if (result.success) {
      setAttempts(0);
      dispatch(setIsAuthenticated(true));
      dispatch(setShowAuthScreen(true));
      dispatch(setBiometricsInProgress(false));
    } else {
      setAttempts((prevAttempts) => prevAttempts + 1);
    }
  }, [attempts, dispatch]);

  const handleAppStateChanges = useCallback(
    (nextAppState: AppStateStatus) => {
      if (appState.current === null && nextAppState === 'active') {
        if (attempts < 3) {
          handleBiometrics();
        }
      }
      appState.current = nextAppState;
    },
    [handleBiometrics, isFocused],
  );

  const handleAppStateChangesAndroid = useCallback(
    (nextAppState: AppStateStatus) => {
      if (nextAppState.match(/inactive|background/)) {
        console.log('INACTIVE OR BACKGROUND');
        dispatch(setIsAuthenticated(false));
      }
    },
    [handleBiometrics],
  );

  const handleTryAgain = useCallback(async () => {
    if (__DEV__) {
      const biometricsAvailable = await areBiometricsAvailable();
      if (!biometricsAvailable) {
        dispatch(setIsAuthenticated(true));
      }
    }

    handleBiometrics();
  }, [handleBiometrics]);

  useFocusEffect(
    useCallback(() => {
      let androidFocusSubscription: NativeEventSubscription | null = null;

      const appStateSubscription = AppState.addEventListener(
        'change',
        Platform.OS === 'ios' ? handleAppStateChanges : handleAppStateChangesAndroid,
      );

      if (Platform.OS === 'android') {
        androidFocusSubscription = AppState.addEventListener('focus', () => {
          console.log('FOCUSED');
          handleBiometrics();
        });
      }

      if (initialMount) {
        handleBiometrics();
        initialMount = false;
      }

      return () => {
        appStateSubscription.remove();
        if (Platform.OS === 'android') {
          androidFocusSubscription?.remove();
        }
      };
    }, []),
  );

  return (
    <View style={styles.overlay}>
      <AnimatedSafeAreaView style={styles.root} entering={FadeIn.duration(800)}>
        <Animated.View
          style={styles.content}
          entering={FadeIn.delay(200).duration(1000).springify().damping(20)}
        >
          <AssetifyWithText
            width={theme.layout.images.md}
            height={theme.layout.images.md}
          />
        </Animated.View>

        <Animated.View
          entering={FadeInDown.delay(600).duration(600).springify().damping(20).mass(1.2)}
        >
          <Footer style={styles.footer}>
            {Platform.OS === 'ios' ? (
              <BodySSB style={styles.footerText}>
                App was locked and needs to be unlocked
              </BodySSB>
            ) : (
              <BodySSB style={styles.footerText}>
                App was locked and needs to be unlocked, please authenticate yourself
              </BodySSB>
            )}

            <MButton variant="secondary" text="Authenticate" onPress={handleTryAgain} />
          </Footer>
        </Animated.View>
      </AnimatedSafeAreaView>
    </View>
  );
};

export default memo(Authenticate);

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: GlobalStyles.base.white,
    zIndex: 999,
  },
  root: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.layout.ph.screen,
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.md,
  },
  footerText: {
    textAlign: 'center',
    color: GlobalStyles.gray.gray800,
  },
});
