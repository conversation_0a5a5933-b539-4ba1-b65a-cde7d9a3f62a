import React, {useRef} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import WebView from 'react-native-webview';

import ReceiveArrow from '@/assets/icons/receiveArrow.svg';
import SendArrow from '@/assets/icons/sendArrow.svg';
import HoldToConfirmButton from '@/components/HoldToConfirmButton';
import SafeAreaInset from '@/components/SafeAreaInset';
import GlobalStyles from '@/constants/GlobalStyles';
import {resetToHome} from '@/navigation/utils/navigation';
import {debugging} from '@/screens/CurrencySpecificScreen/SendAsset/sendAssetUtils';
import Success from '@/screens/Success';
import {formatNumber} from '@/utils';
import ExchangeRate from './components/ExchangeRate';
import {TIMER_DURATION_IN_SECONDS} from './helpers/exchangeConstants';
import {useExchangeRate} from './hooks/useExchangeRate';
import {useExchangeTransaction} from './hooks/useExchangeTransaction';

export const ExchangeConfirm: React.FC<any> = ({route, navigation}) => {
  const webViewRef = useRef<WebView>(null);
  const {payinLabel, payoutLabel} = route.params;

  const {payinAmount, payoutAmount, rate, rateID, manuallyFetchRate} = useExchangeRate(
    payinLabel,
    payoutLabel,
  );

  const {
    handleConfirmPress,
    isLoading,
    isSuccess,
    webViewPayload,
    handleWebViewMessage,
    isDisabled,
  } = useExchangeTransaction({
    payinLabel,
    payoutLabel,
    payinAmount,
    rateID,
  });

  if (isSuccess) {
    navigation.setOptions({
      headerShown: false,
    });

    return (
      <Success
        title="Swap Completed"
        description="Soon you will receive your funds"
        onFinish={resetToHome}
        isSuccess={isSuccess}
      />
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View>
          <Text style={styles.title}>Swap Details</Text>
        </View>
        <View style={styles.row}>
          <View style={styles.iconContainer}>
            <SendArrow width={38} height={38} />
          </View>
          <View style={styles.textGroup}>
            <Text style={styles.label}>You send</Text>
            <View style={styles.amountRow}>
              <Text style={styles.amount}>{formatNumber(payinAmount)}</Text>
              <View style={styles.currencyBadge}>
                <Text style={styles.currencyText}>{payinLabel.label}</Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.rowRate}>
          <ExchangeRate
            selectedPayin={payinLabel.label}
            selectedPayout={payoutLabel.label}
            fixedRate={rate}
            timeDuration={TIMER_DURATION_IN_SECONDS}
            onCompletion={manuallyFetchRate}
            loading={isLoading}
          />
        </View>

        <View style={styles.divider} />

        <View style={styles.row}>
          <View style={styles.iconContainer}>
            <ReceiveArrow width={38} height={38} />
          </View>
          <View style={styles.textGroup}>
            <Text style={styles.label}>You get</Text>
            <View style={styles.amountRow}>
              <Text style={styles.amount}>{formatNumber(payoutAmount)}</Text>
              <View style={[styles.currencyBadge, styles.secondCurrencyBadge]}>
                <Text style={styles.currencyText}>{payoutLabel.label}</Text>
              </View>
            </View>
          </View>
        </View>
        <View style={styles.estimatedTime}>
          <View style={styles.estimatedTimeRow}>
            <View style={styles.estimatedTimeLeft}>
              <Text style={styles.estimatedTimeTitle}>Estimated arrival time</Text>
              <Text style={styles.estimatedTimeValue}>5-20 mins</Text>
            </View>
            <View style={styles.walletInfoContainer}>
              <Text style={styles.viaText}>Via</Text>
              <Text style={styles.walletText}>Assetify wallet</Text>
            </View>
          </View>
        </View>
      </View>

      <View style={styles.footer}>
        <HoldToConfirmButton onPress={handleConfirmPress} disabled={isDisabled} />
      </View>

      <SafeAreaInset type="bottom" maxPadding={4} />

      <View style={styles.hiddenWebView}>
        {webViewPayload && webViewPayload !== debugging && (
          <WebView
            ref={webViewRef}
            source={{
              uri: 'https://dev.assetify.net/kaspa-worker/index.html',
            }}
            javaScriptEnabled={true}
            onLoad={() => {
              webViewRef.current?.injectJavaScript(webViewPayload);
            }}
            onMessage={handleWebViewMessage}
          />
        )}
      </View>
    </View>
  );
};

export default ExchangeConfirm;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    marginTop: 32,
    gap: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: GlobalStyles.base.black,
    marginBottom: 14,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
  },
  rowRate: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
    marginTop: 18,
    marginLeft: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textGroup: {
    flex: 1,
  },
  label: {
    fontSize: 12,
    color: GlobalStyles.gray.gray800,
    marginBottom: 4,
  },
  amountRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  amount: {
    fontSize: 16,
    fontWeight: '500',
    color: GlobalStyles.base.black,
  },
  currencyBadge: {
    backgroundColor: GlobalStyles.error.error100,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
  },
  secondCurrencyBadge: {
    backgroundColor: GlobalStyles.success.success100,
  },
  currencyText: {
    fontSize: 12,
    fontWeight: '500',
    color: GlobalStyles.base.black,
  },
  divider: {
    height: 1,
    backgroundColor: GlobalStyles.gray.gray600,
    marginVertical: 8,
  },
  estimatedTime: {
    marginTop: 52,
  },
  estimatedTimeRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  estimatedTimeLeft: {
    flex: 1,
  },
  estimatedTimeTitle: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
    marginBottom: 4,
  },
  estimatedTimeValue: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  walletInfoContainer: {
    alignItems: 'flex-end',
  },
  viaText: {
    fontSize: 14,
    color: GlobalStyles.gray.gray800,
  },
  walletText: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  footer: {
    alignItems: 'center',
    paddingBottom: 16,
  },
  hiddenWebView: {
    height: 0,
    width: 0,
  },
});
