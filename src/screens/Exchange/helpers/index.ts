import {formatNumber} from '@/utils';
import {fetchLimits} from './exchangeChangellyApi';
import {
  INSUFFICIENT_BALANCE_MESSAGE,
  mapSymbolToCurrencyOption,
} from './exchangeConstants';

type LimitsResult = {
  updatedPayinAmount: string | undefined;
  updatedPayoutAmount: string | undefined;
  payinUXUpdate: string;
  payoutUXUpdate: string;
};

type BoundaryCheck = {
  value: number;
  min: number;
  max: number;
};

/* ============================================================================================== */
/*                                             EXPOSE                                             */
/* ============================================================================================== */

export const transformLabel = (label: string) => {
  switch (label.toUpperCase()) {
    case 'USDT20':
      return 'USDT';
    case 'USDTBSC':
      return 'USDT';
    case 'USDCBSC':
      return 'USDC';
    case 'TUSDBSC':
      return 'TUSD';
    case 'USDTRX':
      return 'USDT';
    case 'USDTAVAC':
      return 'USDT';
    case 'TUSDTRX':
      return 'USDT';
    case 'USDTAVAC':
      return 'USDT';
    case 'USDCAVAC':
      return 'USDC';
    default:
      return label;
  }
};

export const fetchAndApplyLimitsIfNeeded = async (
  payinLabel: string,
  payoutLabel: string,
  payinAmount?: string,
  payoutAmount?: string,
): Promise<LimitsResult> => {
  const result: LimitsResult = {
    updatedPayinAmount: payinAmount,
    updatedPayoutAmount: payoutAmount,
    payinUXUpdate: '',
    payoutUXUpdate: '',
  };

  // Handle payin amount check
  if (payinAmount && !payoutAmount) {
    const bounds = await fetchLimits(payinLabel, payoutLabel);
    const {minAmountFixed: min, maxAmountFixed: max} = bounds.result[0];

    const check = checkBoundaries({
      value: parseFloat(payinAmount),
      min: parseFloat(min),
      max: parseFloat(max),
    });

    result.updatedPayinAmount = getUpdatedAmount(
      check,
      parseFloat(payinAmount),
      parseFloat(min),
      parseFloat(max),
    );
    result.payinUXUpdate = getUXMessage(check, parseFloat(result.updatedPayinAmount));
  }

  // Handle payout amount check
  if (payoutAmount && !payinAmount) {
    const bounds = await fetchLimits(payoutLabel, payinLabel);
    const {minAmountFixed: min, maxAmountFixed: max} = bounds.result[0];

    const check = checkBoundaries({
      value: parseFloat(payoutAmount),
      min: parseFloat(min),
      max: parseFloat(max),
    });

    result.updatedPayoutAmount = getUpdatedAmount(
      check,
      parseFloat(payoutAmount),
      parseFloat(min),
      parseFloat(max),
    );
    result.payoutUXUpdate = getUXMessage(check, parseFloat(result.updatedPayoutAmount));
  }

  return result;
};

export const balanceChecker = ({
  payinLabel,
  payinAmount,
  user,
  stableCoins,
  ChainToTokens,
  assets,
  transformLabel,
}: any) => {
  if (!payinLabel || !payinAmount) return '';

  // If it's a stablecoin, we need to pass the chain
  const stablecoinChain = mapSymbolToCurrencyOption(payinLabel)?.chain;

  // Check for stablecoins
  if (stablecoinChain) {
    const walletIndex = user.wallet.findIndex(
      (w: any) => w.blockchain === stablecoinChain,
    );

    const stablecoinLabel = transformLabel(payinLabel.toUpperCase());
    const stablecoinIndex =
      ChainToTokens[stablecoinChain].findIndex(
        (t: any) => t.tokenSymbol.toUpperCase() === stablecoinLabel,
      ) + 1;

    if (stablecoinIndex === 0) return INSUFFICIENT_BALANCE_MESSAGE;

    const availableStableAmount = stableCoins[walletIndex][stablecoinIndex - 1] ?? '0';

    return parseFloat(availableStableAmount) <= parseFloat(payinAmount)
      ? INSUFFICIENT_BALANCE_MESSAGE
      : '';
  }

  const assetLabel =
    payinLabel.toUpperCase() === 'BNBBSC' ? 'bsc' : payinLabel.toUpperCase();
  const asset = assets.find((item: any) => item.tokenSymbol === assetLabel);

  return parseFloat(asset?.amount === '0.' ? '0' : asset?.amount) <=
    parseFloat(payinAmount)
    ? INSUFFICIENT_BALANCE_MESSAGE
    : '';
};

/* ============================================================================================== */
/*                                            INTERNALS                                           */
/* ============================================================================================== */

const checkBoundaries = ({value, min, max}: BoundaryCheck) => {
  if (value < min) return 'min';
  if (value > max) return 'max';
  return 'within';
};

const getUpdatedAmount = (status: string, value: number, min: number, max: number) => {
  switch (status) {
    case 'min':
      // Add 1% buffer to minimum amount

      // NOTE: Soon a replacement for `getPairsParams` will come out:
      //       https://docs.changelly.com/limits/get-pairs-params
      //       So, this won't be needed.
      return (min * 1.01).toString();
    case 'max':
      return max.toString();
    default:
      return value.toString();
  }
};

const getUXMessage = (status: string, amount: number) => {
  switch (status) {
    case 'min':
      return `Minimum amount is ${formatNumber(amount)}`;
    case 'max':
      return `Maximum amount is ${formatNumber(amount)}`;
    default:
      return '';
  }
};
