import type {ExchangeCurrencyOption} from '@/screens/Exchange/_types';

export const TIMER_DURATION_IN_SECONDS = 75;
export const INSUFFICIENT_BALANCE_MESSAGE =
  'Insufficient balance in your Assetify wallet';

const currencyMap: Record<string, ExchangeCurrencyOption> = {
  btc: {fullName: 'Bitcoin', label: 'BTC', type: 'crypto'},
  eth: {fullName: 'Ethereum', label: 'ETH', type: 'crypto'},
  sol: {fullName: 'Solana', label: 'SOL', type: 'crypto'},
  usdtsol: {
    fullName: 'Tether USD (Solana)',
    label: 'USDTSOL',
    type: 'crypto',
    chain: 'solana',
  },
  usdcsol: {
    fullName: 'USD Coin (Solana)',
    label: 'USDCSOL',
    type: 'crypto',
    chain: 'solana',
  },
  usdt20: {
    fullName: 'USD Tether (Ethereum)',
    label: 'USDT20',
    type: 'crypto',
    chain: 'ethereum',
  },
  usdc: {
    fullName: 'Usd Coin (Ethereum)',
    label: 'USDCETH',
    type: 'crypto',
    chain: 'ethereum',
  },
  tusd: {
    fullName: 'True USD (Ethereum)',
    label: 'TUSDETH',
    type: 'crypto',
    chain: 'ethereum',
  },
  dai: {
    fullName: 'Dai (Ethereum)',
    label: 'DAIETH',
    type: 'crypto',
    chain: 'ethereum',
  },
  trx: {fullName: 'Tron', label: 'TRX', type: 'crypto'},
  usdtrx: {
    fullName: 'USD Tether (Tron)',
    label: 'USDTTRX',
    type: 'crypto',
    chain: 'trx',
  },
  tusdtrx: {
    fullName: 'True USD (Tron)',
    label: 'TUSDTRX',
    type: 'crypto',
    chain: 'trx',
  },
  bnbbsc: {fullName: 'Binance Coin', label: 'BNBBSC', type: 'crypto'},
  usdtbsc: {
    fullName: 'Tether USD (Binance Smart Chain)',
    label: 'USDTBSC',
    type: 'crypto',
    chain: 'binance-smart-chain',
  },
  usdcbsc: {
    fullName: 'USD Coin (Binance Smart Chain)',
    label: 'USDCBSC',
    type: 'crypto',
    chain: 'binance-smart-chain',
  },
  tusdbsc: {
    fullName: 'True USD (Binance Smart Chain)',
    label: 'TUSDBSC',
    type: 'crypto',
    chain: 'binance-smart-chain',
  },
  xrp: {fullName: 'Ripple', label: 'XRP', type: 'crypto'},
  avax: {fullName: 'Avalanche', label: 'AVAX', type: 'crypto'},
  usdtavax: {
    fullName: 'Tether Avalanche Chain',
    label: 'USDTAVAX',
    type: 'crypto',
    chain: 'avalanche',
  },
  usdcavax: {
    fullName: 'Usd Coin Avalanche Chain',
    label: 'USDCAVAX',
    type: 'crypto',
    chain: 'avalanche',
  },
  bch: {fullName: 'Bitcoin Cash', label: 'BCH', type: 'crypto'},
  ltc: {fullName: 'Litecoin', label: 'LTC', type: 'crypto'},
  doge: {fullName: 'Dogecoin', label: 'DOGE', type: 'crypto'},
  kas: {fullName: 'Kaspa', label: 'KAS', type: 'crypto'},
};

export const mapSymbolToCurrencyOption = (
  symbol: string,
): ExchangeCurrencyOption | null => {
  return currencyMap[symbol.toLowerCase()] || null;
};

export const payinOptionsSample: ExchangeCurrencyOption[] = Object.values(currencyMap);

export const isValidCurrencySymbol = (symbol: string): boolean => {
  return symbol.toLowerCase() in currencyMap;
};
