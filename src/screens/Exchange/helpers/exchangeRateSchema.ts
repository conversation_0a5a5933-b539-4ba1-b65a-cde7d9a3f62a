import {ChainToTokens} from '@/constants/Chains';
import {balanceChecker, fetchAndApplyLimitsIfNeeded, transformLabel} from '.';
import {fetchFixRateForAmount} from './exchangeChangellyApi';

type ExchangeRateResult = {
  rateResponse: {
    amountFrom: string;
    amountTo: string;
    result: string;
    id: string;
    error?: string;
  };
  uiUpdates: {
    payin?: string;
    payout?: string;
  };
  balanceMessage: string;
};

/**
 * Orchestrates the exchange rate process by:
 *
 * - Validates and normalizes input amounts
 * - Fetches and applies exchange limits
 * - Calculates final exchange rate
 * - Checks user balance
 */
export const exchangeRateSchema = async (
  payinLabel: string,
  payoutLabel: string,
  assets: any,
  user: any,
  stableCoins: any,
  payinAmount?: string,
  payoutAmount?: string,
): Promise<ExchangeRateResult> => {
  if (payoutAmount) payinAmount = '';
  else if (!payinAmount && !payoutAmount) payinAmount = '0';

  // console.log('1️⃣  Initial payinAmount & payoutAmount', payinAmount, payoutAmount);

  const limitsResponse = await fetchAndApplyLimitsIfNeeded(
    payinLabel.toLowerCase(),
    payoutLabel.toLowerCase(),
    payinAmount,
    payoutAmount,
  );

  // console.log('2️⃣  limitsResponse', limitsResponse);

  const {updatedPayinAmount, updatedPayoutAmount, payinUXUpdate, payoutUXUpdate} =
    limitsResponse;

  const rateResponse = await fetchFixRateForAmount(
    payinLabel.toLowerCase(),
    payoutLabel.toLowerCase(),
    updatedPayinAmount,
    updatedPayoutAmount,
  );

  // console.log('3️⃣  rateResponse', rateResponse);

  const balanceMessage = balanceChecker({
    payinLabel,
    payinAmount: rateResponse.result[0].amountFrom,
    user,
    stableCoins,
    ChainToTokens,
    assets,
    transformLabel,
  });

  // console.log('4️⃣  balanceMessage', balanceMessage);

  return {
    rateResponse: rateResponse.result[0],
    uiUpdates: {
      payin: payinUXUpdate || undefined,
      payout: payoutUXUpdate || undefined,
    },
    balanceMessage,
  };
};
