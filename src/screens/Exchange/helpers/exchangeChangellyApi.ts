import {ExchangeInstance} from '../../../services/BackendServices';

export const fetchPairs = async (
  payinLabel: string | null = null,
  payoutLabel: string | null = null,
) => {
  try {
    const res = await ExchangeInstance.post('#getPairs', {
      jsonrpc: '2.0',
      id: 'prodAss',
      method: 'getPairs',
      params: {
        from: payinLabel ? payinLabel.toLowerCase() : undefined,
        to: payoutLabel ? payoutLabel.toLowerCase() : undefined,
        txType: 'fixed',
      },
    });

    return res.data;
  } catch (e) {
    console.error('error fetching pairs', e);
  }
};

export const fetchLimits = async (payinLabel: string, payoutLabel: string) => {
  try {
    const res = await ExchangeInstance.post('#getPairsParams', {
      jsonrpc: '2.0',
      id: 'prodAss',
      method: 'getPairsParams',
      params: {
        from: payinLabel,
        to: payoutLabel,
      },
    });

    return res.data;
  } catch (e) {
    console.error('error fetching limits', e);
    return null;
  }
};

export const fetchFixRateForAmount = async (
  payinLabel: string,
  payoutLabel: string,
  payinAmount?: string,
  payoutAmount?: string,
) => {
  try {
    const params: any = {
      from: payinLabel,
      to: payoutLabel,
    };

    if (payinAmount) {
      params.amountFrom = payinAmount;
    } else if (payoutAmount) {
      params.amountTo = payoutAmount;
    }

    const res = await ExchangeInstance.post('#getFixRateForAmount', {
      jsonrpc: '2.0',
      id: 'prodAss',
      method: 'getFixRateForAmount',
      params,
    });

    return res.data;
  } catch (e) {
    console.error('error fetching rate', e);
    return null;
  }
};

export const createFixedRateTx = async (
  payinLabel: string,
  payoutLabel: string,
  rateId: string,
  recipientAddr: string,
  refundAddr: string,
  payinAmount: string,
) => {
  try {
    const res = await ExchangeInstance.post('#createFixTransaction', {
      jsonrpc: '2.0',
      id: 'prodAss',
      method: 'createFixTransaction',
      params: {
        from: payinLabel,
        to: payoutLabel,
        rateId: rateId,
        address: recipientAddr,
        refundAddress: refundAddr,
        amountFrom: payinAmount,
      },
    });

    return res.data;
  } catch (e) {
    console.error('error creating fixed rate tx', e);
    return null;
  }
};
