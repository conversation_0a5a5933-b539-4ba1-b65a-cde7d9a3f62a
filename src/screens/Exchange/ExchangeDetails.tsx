import {useFocusEffect} from '@react-navigation/native';
import React, {useCallback, useEffect} from 'react';
import {ScrollView, StyleSheet, Text, View} from 'react-native';

import {KeyboardAvoidingView} from '@/components/KeyboardAvoidingView';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useBottomSheet} from '@/hooks/bottomSheet';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {
  setExpiryTimestamp,
  setPaused,
  setTimeLeft,
} from '@/storage/actions/exchangeActions';
import {useDispatch, useSelector} from 'react-redux';
import type {ExchangeCurrencyOption} from './_types';
import ExchangeFeatures from './components/ExchangeFeatures';
import {ExchangeForm} from './components/ExchangeForm';
import {TIMER_DURATION_IN_SECONDS} from './helpers/exchangeConstants';
import {useCurrencyChange} from './hooks/useCurrencyChange';
import {useExchangeRate} from './hooks/useExchangeRate';

const ExchangeDetails: React.FC = ({route}: any) => {
  const {open: openSheet, close: closeSheet} = useBottomSheet('list');
  const dispatch = useDispatch();
  const expiryTimestamp = useSelector((state: any) => state.exchange.expiryTimestamp);

  const {
    selectedPayin,
    selectedPayout,
    onPayinChange: onPayinCurrencyChange,
    onPayoutChange: onPayoutCurrencyChange,
    payinOptions,
    payoutOptions,
  } = useCurrencyChange();

  const {rate, payinAmount, payoutAmount, error, uiUpdates, loading, manuallyFetchRate} =
    useExchangeRate(selectedPayin, selectedPayout);

  const handleCurrencySelect = (type: 'payin' | 'payout') => {
    const options = type === 'payin' ? payinOptions : payoutOptions;
    openSheet(
      {
        type: 'currency',
        data: options.map((option) => ({
          ...option,
          value: option.label,
        })),
        onSelect: (selected) => {
          const selectedOption = selected as ExchangeCurrencyOption;
          if (type === 'payin') {
            onPayinCurrencyChange(selectedOption);
          } else {
            onPayoutCurrencyChange(selectedOption);
          }
          closeSheet();
        },
      },
      80,
      true,
    );
  };

  const handleCompletePress = () =>
    navigateViaBottomTabs('Exchange', 'ExchangeConfirm', {
      payinLabel: selectedPayin,
      payoutLabel: selectedPayout,
    });

  const initializeTimer = () => {
    dispatch(setPaused(false));
    dispatch(setTimeLeft(TIMER_DURATION_IN_SECONDS));
    dispatch(setExpiryTimestamp(Date.now() + TIMER_DURATION_IN_SECONDS * 1000));
  };

  // Check timer state when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (expiryTimestamp) {
        const now = Date.now();
        const timeLeft = Math.max(0, Math.floor((expiryTimestamp - now) / 1000));

        if (timeLeft <= 0) {
          // Timer expired, reset everything
          initializeTimer();
          manuallyFetchRate('0', undefined);
        } else {
          // Resume timer with remaining time
          dispatch(setTimeLeft(timeLeft));
          dispatch(setPaused(false));
        }
      }
    }, [expiryTimestamp]),
  );

  useEffect(() => {
    // If the route params are not set, we are on the first render
    if (!route.params) {
      manuallyFetchRate('0', undefined);
      initializeTimer();
    }
  }, [route.params]);

  return (
    <>
      <KeyboardAvoidingView style={styles.keyboardAvoidingView}>
        <ScrollView style={styles.root}>
          <View style={styles.screen}>
            <ExchangeFeatures />

            {error && <Text style={styles.error}>{error}</Text>}

            <ExchangeForm
              selectedPayin={selectedPayin}
              selectedPayout={selectedPayout}
              payinAmount={payinAmount}
              payoutAmount={payoutAmount}
              rate={rate}
              isLoading={loading}
              uiUpdates={uiUpdates}
              onPayinCurrencyChange={() => handleCurrencySelect('payin')}
              onPayoutCurrencyChange={() => handleCurrencySelect('payout')}
              fetchRate={manuallyFetchRate}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
      <View style={styles.footer}>
        <MButton
          text="Confirm Swap"
          onPress={handleCompletePress}
          disabled={!!error || loading || !payinAmount || !payoutAmount}
        />
      </View>
    </>
  );
};

export default ExchangeDetails;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  root: {
    flex: 1,
  },
  screen: {
    paddingHorizontal: 22,
    marginTop: 12,
  },
  error: {
    color: GlobalStyles.error.error400,
    marginBottom: 4,
    paddingHorizontal: 6,
  },
  footer: {
    width: '90%',
    alignSelf: 'center',
    paddingVertical: 16,
  },
});
