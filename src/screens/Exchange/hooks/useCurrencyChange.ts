import {useQuery} from '@tanstack/react-query';
import {useEffect, useState} from 'react';

import {useAppDispatch} from '@/hooks/redux';
import type {ExchangeCurrencyOption} from '@/screens/Exchange/_types';
import {setTimeLeft} from '../../../storage/actions/exchangeActions';
import {fetchPairs} from '../helpers/exchangeChangellyApi';
import {
  TIMER_DURATION_IN_SECONDS,
  mapSymbolToCurrencyOption,
  payinOptionsSample,
} from '../helpers/exchangeConstants';

export const useCurrencyChange = () => {
  const dispatch = useAppDispatch();

  const [selectedPayin, setSelectedPayin] = useState<any>(payinOptionsSample[0]);
  const [selectedPayout, setSelectedPayout] = useState<any>(payinOptionsSample[1]);
  const [payinOptions, setPayinOptions] =
    useState<ExchangeCurrencyOption[]>(payinOptionsSample);
  const [payoutOptions, setPayoutOptions] = useState<ExchangeCurrencyOption[]>([]);

  const {
    data: payinData,
    isSuccess: isPayinSuccess,
    isLoading: isPayinLoading,
    refetch: fetchPayinPairs,
  } = useQuery({
    queryKey: ['fetchPairs', selectedPayin.label],
    queryFn: () => fetchPairsForChange(selectedPayin.label.toLowerCase(), null),
  });

  const {
    data: payoutData,
    isSuccess: isPayoutSuccess,
    isLoading: isPayoutLoading,
    refetch: fetchPayoutPairs,
  } = useQuery({
    queryKey: ['fetchPairs', selectedPayout.label],
    queryFn: () => fetchPairsForChange(null, selectedPayout.label.toLowerCase()),
  });

  const fetchPairsForChange = (payinLabel: string | null, payoutLabel: string | null) => {
    return fetchPairs(payinLabel, payoutLabel);
  };

  const updateOptions = (
    response: any,
    payinLabel: string | null,
    payoutLabel: string | null,
  ) => {
    let pairIsCompatible = false;

    const filteredSymbols = response.result
      .filter((pair: any) => {
        if (payinLabel) {
          const isCurrentPair =
            pair.from.toLowerCase() === payinLabel.toLowerCase() &&
            pair.to.toLowerCase() === selectedPayout.label.toLowerCase();
          if (isCurrentPair) pairIsCompatible = true;

          return pair.from.toLowerCase() === payinLabel.toLowerCase();
        }
        if (payoutLabel) {
          const isCurrentPair =
            pair.from.toLowerCase() === selectedPayin.label.toLowerCase() &&
            pair.to.toLowerCase() === payoutLabel.toLowerCase();
          if (isCurrentPair) pairIsCompatible = true;

          return pair.to.toLowerCase() === payoutLabel.toLowerCase();
        }

        return false;
      })
      .map((pair: any) => (payinLabel ? pair.to.toLowerCase() : pair.from.toLowerCase()));

    const newOptions = filteredSymbols
      .map((symbol: string) => mapSymbolToCurrencyOption(symbol))
      .filter((option): option is any => option !== null);

    if (newOptions.length < 1) return;

    if (payinLabel) {
      setPayoutOptions(newOptions);
      if (!pairIsCompatible) setSelectedPayout(newOptions[0]);
    } else if (payoutLabel) {
      setPayinOptions(newOptions);
      if (!pairIsCompatible) setSelectedPayin(newOptions[0]);
    }
  };

  const onPayinChange = (newPayin: any) => {
    setSelectedPayin(newPayin);
    dispatch(setTimeLeft(TIMER_DURATION_IN_SECONDS));
    fetchPayinPairs();
  };

  const onPayoutChange = (newPayout: any) => {
    setSelectedPayout(newPayout);
    dispatch(setTimeLeft(TIMER_DURATION_IN_SECONDS));
    fetchPayoutPairs();
  };

  useEffect(() => {
    if (isPayinLoading) return;

    if (isPayinSuccess && payinData) {
      updateOptions(payinData, selectedPayin.label, null);
      // console.log(`0️⃣ Fetched Pairs for ${selectedPayin.label} & null >>>>>>`);
    }
  }, [isPayinLoading, isPayinSuccess, payinData, selectedPayin]);

  useEffect(() => {
    if (isPayoutLoading) return;

    if (isPayoutSuccess && payoutData) {
      updateOptions(payoutData, null, selectedPayout?.label);
      // console.log(`0️⃣ Fetched Pairs for null & ${selectedPayout.label} >>>>>>`);
    }
  }, [isPayoutLoading, isPayoutSuccess, payoutData, selectedPayout]);

  return {
    selectedPayin,
    setSelectedPayin,
    selectedPayout,
    setSelectedPayout,
    payinOptions,
    payoutOptions,
    onPayinChange,
    onPayoutChange,
  };
};
