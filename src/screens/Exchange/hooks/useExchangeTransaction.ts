import {useEffect, useMemo, useState} from 'react';

import {useAppDispatch, useAuth} from '@/hooks/redux';
import {
  findChainIndex,
  findWalletIndex,
} from '@/screens/CurrencySpecificScreen/currencySpecificUtils';
import {
  getPreparedTx,
  prepareSolanaTransaction,
  sendAndBroadcastSolanaTransaction,
} from '@/screens/CurrencySpecificScreen/SendAsset/screens/SendAssetInput/SendAssetInputUtils';
import {debugging} from '@/screens/CurrencySpecificScreen/SendAsset/sendAssetUtils';
import {WalletBalanceInstance} from '@/services/BackendServices';
import WalletService from '@/services/WalletService';
import {setPaused} from '@/storage/actions/exchangeActions';
import {triggerBiometrics} from '@/utils/biometrics';
import {ExchangeCurrencyOption} from '../_types';
import {transformLabel} from '../helpers';
import {createFixedRateTx} from '../helpers/exchangeChangellyApi';
import {mapSymbolToCurrencyOption} from '../helpers/exchangeConstants';

export const useExchangeTransaction = ({
  payoutLabel,
  payinLabel,
  payinAmount,
  rateID,
}: {
  payoutLabel: ExchangeCurrencyOption;
  payinLabel: ExchangeCurrencyOption;
  payinAmount: string;
  rateID: string;
}) => {
  const dispatch = useAppDispatch();

  const {user, userAddresses, assets} = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isDisabled, setDisabled] = useState(false);
  const [webViewPayload, setWebViewPayload] = useState<string | null>(null);
  const [signedKaspaTransaction, setSignedKaspaTransaction] = useState<any>(null);

  const payinStablecoinChain = useMemo(
    () => mapSymbolToCurrencyOption(payinLabel.label.toLowerCase())?.chain,
    [payinLabel.label],
  );

  const payoutStablecoinChain = useMemo(
    () => mapSymbolToCurrencyOption(payoutLabel.label.toLowerCase())?.chain,
    [payoutLabel.label],
  );

  const getRecipientAddress = () => {
    if (!userAddresses || !payoutLabel) return '';

    if (payoutStablecoinChain) {
      const index = findChainIndex(payoutStablecoinChain);
      return userAddresses[index].address;
    }

    const index = findWalletIndex(
      payoutLabel.label === 'BNBBSC' ? 'bsc' : payoutLabel.label,
    );

    return payoutLabel.label === 'KAS'
      ? userAddresses[index].addresses[0].address
      : userAddresses[index].address;
  };

  const getRefundAddress = () => {
    if (!userAddresses || !payinLabel) return '';

    if (payinStablecoinChain) {
      const index = findChainIndex(payinStablecoinChain);
      return userAddresses[index].address;
    }

    const index = findWalletIndex(
      payinLabel.label === 'BNBBSC' ? 'bsc' : payinLabel.label,
    );

    return payinLabel.label === 'KAS'
      ? userAddresses[index].addresses[0].address
      : userAddresses[index].address;
  };

  const handleWebViewMessage = (payload: any) => {
    let dataPayload;

    try {
      dataPayload = JSON.parse(payload.nativeEvent.data);
    } catch (e) {
      console.log(e);
    }

    console.log(dataPayload);

    if (dataPayload.type === 'Console') {
      if (dataPayload.data.type === 'info') {
        const log = JSON.parse(dataPayload.data.log);
        setSignedKaspaTransaction(log);
      }
    }
  };

  const handleConfirmPress = async (): Promise<boolean> => {
    setIsLoading(true);
    dispatch(setPaused(true));
    setDisabled(true);

    if (payinLabel.label.toLowerCase() === 'sol') {
      const result = await triggerBiometrics();

      if (result.success) {
        try {
          console.log('recipientAddress', getRecipientAddress());
          const data = await createFixedRateTx(
            payinLabel.label.toLowerCase(),
            payoutLabel.label.toLowerCase(),
            rateID,
            getRecipientAddress(),
            getRefundAddress(),
            payinAmount,
          );

          console.log('paying in address', data.result.payinAddress);

          const {tx, feeData} = await prepareSolanaTransaction(
            data.result.payinAddress,
            parseFloat(payinAmount),
            userAddresses,
          );

          console.log('tx', tx);

          await sendAndBroadcastSolanaTransaction(
            userAddresses[findWalletIndex('SOL')].address,
            tx,
            '', // just for now
            assets.find((item: any) => item.blockchain === 'solana'),
            userAddresses,
            data.result.payinAddress,
            parseFloat(payinAmount),
            feeData.solana.PDA.wallets,
          );

          setIsSuccess(true);
          return true;
        } catch (error) {
          console.log('error', error);
        }
      }

      return false;
    }

    const result = await triggerBiometrics();

    if (result.success) {
      const data = await createFixedRateTx(
        payinLabel.label.toLowerCase(),
        payoutLabel.label.toLowerCase(),
        rateID,
        getRecipientAddress(),
        getRefundAddress(),
        payinAmount,
      );

      let asset;
      if (payinStablecoinChain) {
        asset = assets.find(
          (item: any) => item.blockchain === payinStablecoinChain?.toLowerCase(),
        );
      } else {
        asset = assets.find(
          (item: any) => item.tokenSymbol === payinLabel.label.toUpperCase(),
        );
      }

      const payinLabelToUse = transformLabel(payinLabel.label.toUpperCase());

      const {tx, wallet, privKey} = await getPreparedTx(
        payinStablecoinChain ? payinLabelToUse : '',
        asset,
        user.wallet,
        data.result.payinAddress,
        parseFloat(payinAmount),
        userAddresses,
      );

      if (tx && asset.blockchain !== 'kaspa') {
        await new WalletService().sendPreparedTx(tx, privKey, wallet!);
      } else if (tx && asset.blockchain === 'kaspa') {
        const privKeys = userAddresses[
          findWalletIndex(asset.tokenSymbol) //@ts-ignore
        ].addresses.map((address: AuthAddress) => address.privateKey);
        setWebViewPayload(
          debugging +
            `\nmultipleHashTx(${JSON.stringify(tx)}, ${JSON.stringify(
              privKeys,
            )}).then((res) => {console.info(res)});`,
        );
      }

      setIsSuccess(true);
      return true;
    } else {
      setDisabled(false);
      return false;
    }
  };

  useEffect(() => {
    if (signedKaspaTransaction !== null) {
      WalletBalanceInstance.post(`/broadcast-kaspa`, {
        //@ts-ignore
        transaction: signedKaspaTransaction.tx,
      })
        .then((res) => {
          console.log('res', res);
          setDisabled(false);
        })
        .catch((error) => {
          console.log('error', error.response);
          setDisabled(false);
        });
    }
  }, [signedKaspaTransaction]);

  return {
    handleConfirmPress,
    isLoading,
    isSuccess,
    webViewPayload,
    handleWebViewMessage,
    isDisabled,
  };
};
