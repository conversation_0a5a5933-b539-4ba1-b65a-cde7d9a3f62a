import {useQuery} from '@tanstack/react-query';
import {useCallback, useEffect, useMemo} from 'react';

import {useAppDispatch, useAppSelector} from '@/hooks/redux';
import {
  setPayinAmount,
  setPayoutAmount,
  setRate,
  setRateID,
  setTimeLeft,
} from '@/storage/actions/exchangeActions';
import {ExchangeCurrencyOption} from '../_types';
import {TIMER_DURATION_IN_SECONDS} from '../helpers/exchangeConstants';
import {exchangeRateSchema} from '../helpers/exchangeRateSchema';
import {useRoute} from '@react-navigation/native';

let lastManualPayin: string = '';
let lastManualPayout: string = '';

export const useExchangeRate = (
  selectedPayin: ExchangeCurrencyOption,
  selectedPayout: ExchangeCurrencyOption,
) => {
  const exchangeState = useAppSelector((state) => state.exchange);
  const authState = useAppSelector((state) => state.auth);
  const {assets, user, stableCoins} = useMemo(() => authState || {}, [authState]);
  const dispatch = useAppDispatch();
  const route = useRoute();

  const {
    data: rateData,
    isSuccess,
    isLoading,
    isRefetching,
    isError,
    refetch,
  } = useQuery({
    queryKey: ['exchangeRate', selectedPayin?.label, selectedPayout?.label],
    queryFn: () => {
      return exchangeRateSchema(
        selectedPayin?.label,
        selectedPayout?.label,
        assets,
        user,
        stableCoins,
        lastManualPayin,
        lastManualPayout,
      );
    },
    staleTime: TIMER_DURATION_IN_SECONDS * 1000,
  });

  const manuallyFetchRate = useCallback(
    (manualPayinAmount?: string, manualPayoutAmount?: string) => {
      if (manualPayinAmount === '0' || manualPayoutAmount === '0') {
        lastManualPayin = '';
        lastManualPayout = '';
        manualPayinAmount = undefined;
        manualPayoutAmount = undefined;
      }

      if (parseFloat(manualPayinAmount!)) {
        lastManualPayin = manualPayinAmount!;
        lastManualPayout = '';
      } else if (parseFloat(manualPayoutAmount!)) {
        lastManualPayout = manualPayoutAmount!;
        lastManualPayin = '';
      }

      refetch();
    },
    [refetch],
  );

  useEffect(() => {
    if (isSuccess && rateData?.rateResponse && !route.params) {
      const {
        amountFrom: payinAmount,
        amountTo: payoutAmount,
        result: rate,
        id: rateID,
      } = rateData?.rateResponse;

      dispatch(setPayinAmount(parseFloat(payinAmount).toFixed(8).toString()));
      dispatch(setPayoutAmount(parseFloat(payoutAmount).toFixed(8).toString()));
      dispatch(setRate(rate));
      dispatch(setRateID(rateID));
      dispatch(setTimeLeft(TIMER_DURATION_IN_SECONDS));
    }
  }, [isSuccess, rateData?.rateResponse]);

  return {
    rate: exchangeState.rate || '0',
    rateID: exchangeState.rateID,
    payinAmount: exchangeState.payinAmount,
    payoutAmount: exchangeState.payoutAmount,
    error: isError ? isError : rateData?.balanceMessage,
    uiUpdates: rateData?.uiUpdates || {},
    loading: isLoading || isRefetching,
    manuallyFetchRate,
  };
};
