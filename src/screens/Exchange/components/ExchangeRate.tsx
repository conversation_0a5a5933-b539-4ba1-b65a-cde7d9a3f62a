import {ActivityIndicator, StyleSheet, Text, View} from 'react-native';

import CountdownTimer from '@/components/CountdownTimer/components/CountdownTimer';
import GlobalStyles from '@/constants/GlobalStyles';
import {formatNumber} from '@/utils';
import {getFontSize} from '@/utils/parsing';

const ExchangeRate: React.FC<any> = ({
  selectedPayin,
  fixedRate,
  selectedPayout,
  timeDuration,
  onCompletion,
  loading,
}) => {
  return (
    <View style={styles.root}>
      <Text style={styles.rate}>
        1 {selectedPayin} ={' '}
        <Text>
          {loading ? (
            <View style={{paddingHorizontal: 5}}>
              <ActivityIndicator size="small" color={GlobalStyles.primary.primary800} />
            </View>
          ) : (
            formatNumber(fixedRate)
          )}
        </Text>{' '}
        {selectedPayout}
      </Text>
      {!loading && (
        <CountdownTimer
          timeDuration={timeDuration}
          onCompletion={onCompletion}
          payinLabel={selectedPayin}
          payoutLabel={selectedPayout}
        />
      )}
    </View>
  );
};

export default ExchangeRate;

const styles = StyleSheet.create({
  root: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rate: {
    color: GlobalStyles.base.black,
    fontWeight: '600',
    fontSize: getFontSize(16),
  },
});
