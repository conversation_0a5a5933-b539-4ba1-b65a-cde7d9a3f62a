import {useCallback, useEffect, useState} from 'react';
import {StyleSheet, Text} from 'react-native';

import GlobalStyles from '@/constants/GlobalStyles';
import {getFontSize} from '@/utils/parsing';

type UXUpdateProps = {
  update: string | undefined;
  loading: boolean;
};

const UXUpdate = ({update, loading}: UXUpdateProps) => {
  const [isVisible, setIsVisible] = useState(false);

  const hideUpdate = useCallback(() => {
    setIsVisible(false);
  }, []);

  useEffect(() => {
    if (!update) return;

    setIsVisible(true);
    const timer = setTimeout(hideUpdate, 2000);

    return () => clearTimeout(timer);
  }, [update, hideUpdate]);

  if (!isVisible || loading) return null;

  return <Text style={styles.update}>{update}</Text>;
};

export default UXUpdate;

const styles = StyleSheet.create({
  update: {
    color: GlobalStyles.success.success900,
    fontWeight: 'bold',
    fontSize: getFontSize(16),
    marginLeft: 8,
    marginTop: 4,
  },
});
