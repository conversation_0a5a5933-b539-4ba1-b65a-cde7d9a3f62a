import GlobalStyles from '@/constants/GlobalStyles';
import {StyleSheet, Text, View} from 'react-native';

const ExchangeFeatures: React.FC = () => {
  return (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.emoji}>🪶</Text>
        <View style={styles.statText}>
          <Text style={styles.statTitle}>Effortless Swapping</Text>
          <Text style={styles.statSubtitle}>Just a few clicks to complete.</Text>
        </View>
      </View>

      <View style={styles.statItem}>
        <Text style={styles.emoji}>⚡</Text>
        <View style={styles.statText}>
          <Text style={styles.statTitle}>Blazing Speed</Text>
          <Text style={styles.statSubtitle}>Done in 5-10 minutes on average.</Text>
        </View>
      </View>

      <View style={[styles.statItem, {marginBottom: 0}]}>
        <Text style={styles.emoji}>🛡️</Text>
        <View style={styles.statText}>
          <Text style={styles.statTitle}>Transparent Pricing</Text>
          <Text style={styles.statSubtitle}>No hidden fees—what you see is</Text>
          <Text style={styles.statSubtitle}>what you pay.</Text>
        </View>
      </View>
    </View>
  );
};

export default ExchangeFeatures;

const styles = StyleSheet.create({
  statsContainer: {
    backgroundColor: GlobalStyles.primary.primary50,
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statText: {
    marginLeft: 12,
  },
  statTitle: {
    color: GlobalStyles.primary.primary400,
    fontSize: 16,
    fontWeight: '600',
  },
  statSubtitle: {
    color: GlobalStyles.primary.primary400,
    fontSize: 14,
    marginTop: 2,
  },
  emoji: {
    fontSize: 28,
    width: 28,
    textAlign: 'center',
  },
});
