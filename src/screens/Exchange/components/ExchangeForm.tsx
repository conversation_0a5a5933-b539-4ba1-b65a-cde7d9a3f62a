import React from 'react';
import {useTranslation} from 'react-i18next';
import {
  NativeSyntheticEvent,
  StyleSheet,
  TextInputFocusEventData,
  View,
} from 'react-native';

import CurrencyInput from '@/components/CurrencyInput';
import UXUpdate from '@/screens/Exchange/components/UXUpdate';
import {useAppDispatch} from '@/hooks/redux';
import {setPayinAmount, setPayoutAmount} from '@/storage/actions/exchangeActions';
import {ExchangeCurrencyOption} from '../_types';
import {TIMER_DURATION_IN_SECONDS} from '../helpers/exchangeConstants';
import ExchangeRate from './ExchangeRate';

type ExchangeFormProps = {
  selectedPayin: ExchangeCurrencyOption;
  selectedPayout: ExchangeCurrencyOption;
  payinAmount: string;
  payoutAmount: string;
  rate?: number;
  isLoading: boolean;
  uiUpdates: {payin?: string; payout?: string};
  onPayinCurrencyChange: () => void;
  onPayoutCurrencyChange: () => void;
  fetchRate: (manualPayinAmount?: string, manualPayoutAmount?: string) => void;
};

export const ExchangeForm = ({
  selectedPayin,
  selectedPayout,
  payinAmount,
  payoutAmount,
  rate,
  isLoading,
  uiUpdates,
  onPayinCurrencyChange,
  onPayoutCurrencyChange,
  fetchRate,
}: ExchangeFormProps) => {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const handlePayinChange = (value: string) => {
    dispatch(setPayinAmount(value.replace(/,/g, '')));
  };

  const handlePayinBlur = (e: NativeSyntheticEvent<TextInputFocusEventData> | string) => {
    const value = typeof e === 'string' ? e : e.nativeEvent.text;
    fetchRate(value, undefined);
  };

  const handlePayoutChange = (value: string) => {
    dispatch(setPayoutAmount(value.replace(/,/g, '')));
  };

  const handlePayoutBlur = (
    e: NativeSyntheticEvent<TextInputFocusEventData> | string,
  ) => {
    const value = typeof e === 'string' ? e : e.nativeEvent.text;
    fetchRate(undefined, value);
  };

  return (
    <View style={styles.container}>
      <View>
        <CurrencyInput
          selectedOption={selectedPayin}
          value={isLoading ? '.' : payinAmount}
          onChangeText={handlePayinChange}
          onOptionPress={onPayinCurrencyChange}
          onBlur={handlePayinBlur}
          label={t('exchange.payinInputLabel')}
        />
        <UXUpdate update={uiUpdates.payin} loading={isLoading} />
      </View>

      <View style={styles.rateContainer}>
        <ExchangeRate
          selectedPayin={selectedPayin.label}
          selectedPayout={selectedPayout.label}
          fixedRate={rate}
          timeDuration={TIMER_DURATION_IN_SECONDS}
          onCompletion={fetchRate}
          loading={isLoading}
        />
      </View>

      <View style={styles.payoutInput}>
        <CurrencyInput
          selectedOption={selectedPayout}
          onOptionPress={onPayoutCurrencyChange}
          value={isLoading ? '.' : payoutAmount}
          onChangeText={handlePayoutChange}
          onBlur={handlePayoutBlur}
          label={t('exchange.payoutInputLabel')}
        />
        <UXUpdate update={uiUpdates.payout} loading={isLoading} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 2,
  },
  rateContainer: {
    alignItems: 'flex-start',
    marginVertical: 24,
    paddingHorizontal: 10,
  },
  payoutInput: {
    marginBottom: 22,
  },
});
