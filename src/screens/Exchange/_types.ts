export type ExchangeCurrencyOption = {
  /** Display name of the currency (e.g., "Bitcoin", "Tether ETH Chain") */
  fullName: string;
  /** Currency symbol/ticker (e.g., "BTC", "USDT20") */
  label: string;
  /** Currency type identifier */
  type: 'crypto' | 'fiat';
  /** Optional chain identifier for tokens (e.g., "ethereum", "bsc") */
  chain?: string;
  /** Optional parent currency for tokens (e.g., "ETH" for USDT20) */
  parentCurrency?: string;
  /** Optional decimal places for formatting */
  decimals?: number;
};

export type ExchangeBottomSheetData = {
  type: 'payin' | 'payout';
  data: ExchangeCurrencyOption[];
};

export type ExchangeRate = {
  rate: string;
  payinAmount: string;
  payoutAmount: string;
  error?: string;
};

export type UIInfo = {
  message: string;
  type: 'info' | 'warning' | 'error';
};

export type ExchangeFormState = {
  payinAmount: string;
  payoutAmount: string;
  selectedPayin: ExchangeCurrencyOption;
  selectedPayout: ExchangeCurrencyOption;
  rate: string | null;
  timeLeft: number;
  isPaused: boolean;
  error?: string;
  firstUiInfo?: UIInfo;
  secondUiInfo?: UIInfo;
};
