export const ChainsOrder = [
  'bitcoin',
  'ethereum',
  'solana',
  'binance-smart-chain',
  'trx',
  'xrp',
  'avalanche',
  'kaspa',
  'bitcoin-cash',
  'litecoin',
  'dogecoin',
];

export const StableChains = [
  'ethereum',
  'solana',
  'binance-smart-chain',
  'trx',
  'avalanche',
];


export const ChainToSymbol: {[key: string]: string} = {
  bitcoin: 'btc',
  'bitcoin-cash': 'bch',
  litecoin: 'ltc',
  dogecoin: 'doge',
  dash: 'dash',
  ethereum: 'eth',
  solana: 'sol',
  'binance-smart-chain': 'bsc',
  kaspa: 'kas',
  xrp: 'xrp',
  trx: 'trx',
  avalanche: 'avax',
};

export const UTXOChains = ['bitcoin', 'bitcoin-cash', 'litecoin', 'dogecoin', 'dash'];

export const EVMChains = ['ethereum', 'binance-smart-chain', 'xrp', 'trx', 'avalanche'];

export const ChainToTokens: {
  [key: string]: {tokenSymbol: string; tokenName: string}[];
} = {
  ethereum: [
    {
      tokenSymbol: 'USDT',
      tokenName: 'Tether USD',
    },
    {
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
    },
    {
      tokenSymbol: 'TUSD',
      tokenName: 'TrueUSD',
    },
    {
      tokenSymbol: 'DAI',
      tokenName: 'Dai Stablecoin',
    },
  ],
  solana: [
    {
      tokenSymbol: 'USDT',
      tokenName: 'Tether USD',
    },
    {
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
    },
    {
      tokenSymbol: 'USDS',
      tokenName: 'USDS',
    },
    {
      tokenSymbol: 'JUP',
      tokenName: 'Jupiter',
    },
    {
      tokenSymbol: 'RAY',
      tokenName: 'Raydium',
    },
  ],
  'binance-smart-chain': [
    {
      tokenSymbol: 'USDT',
      tokenName: 'Tether USD',
    },
    {
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
    },
    {
      tokenSymbol: 'TUSD',
      tokenName: 'TrueUSD',
    },
    {
      tokenSymbol: 'DAI',
      tokenName: 'Dai Stablecoin',
    },
  ],
  trx: [
    {
      tokenSymbol: 'USDT',
      tokenName: 'Tether USD',
    },
    {
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
    },
    {
      tokenSymbol: 'TUSD',
      tokenName: 'TrueUSD',
    },
  ],
  avalanche: [
    {
      tokenSymbol: 'USDt',
      tokenName: 'Tether USD',
    },
    {
      tokenSymbol: 'USDT.e',
      tokenName: 'Tether USD',
    },
    {
      tokenSymbol: 'USDC',
      tokenName: 'USD Coin',
    },
    {
      tokenSymbol: 'USDC.e',
      tokenName: 'USD Coin',
    },
    {
      tokenSymbol: 'DAI.e',
      tokenName: 'Dai Stablecoin',
    },
    {
      tokenSymbol: 'TUSD',
      tokenName: 'TrueUSD',
    },
  ],
};

export const SymbolToChain: {[key: string]: string} = {
  btc: 'bitcoin',
  bch: 'bitcoin-cash',
  btc_cash: 'bitcoin-cash',
  ltc: 'litecoin',
  doge: 'dogecoin',
  dash: 'dash',
  eth: 'ethereum',
  sol: 'solana',
  bsc: 'binance-smart-chain',
  bnb: 'binance-smart-chain',
  xrp: 'xrp',
  trx: 'trx',
  tron: 'trx',
  kaspa: 'kaspa',
  kas: 'kaspa',
  avax: 'avalanche',
  avalanche: 'avalanche',
};

export const EVMChainsIds: {[key: string]: number} = {
  ethereum: 1,
  'binance-smart-chain': 56,
  xrp: 144,
  trx: 195,
  avalanche: 43114,
};

export const coinToExplorer: {[key: string]: string} = {
  BTC: 'https://blockchair.com/bitcoin/transaction/',
  ETH: 'https://blockchair.com/ethereum/transaction/',
  SOL: 'https://blockchair.com/solana/transaction/',
  BNB: 'https://bscscan.com/tx/',
  TRX: 'https://blockchair.com/tron/transaction/',
  XRP: 'https://blockchair.com/xrp-ledger/transaction/',
  KAS: 'https://explorer.kaspa.org/txs/',
  BCH: 'https://blockchair.com/bitcoin-cash/transaction/',
  LTC: 'https://blockchair.com/litecoin/transaction/',
  DOGE: 'https://blockchair.com/dogecoin/transaction/',
  AVAX: 'https://blockchair.com/avalanche/transaction/',
};
