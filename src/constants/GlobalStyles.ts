import {Dimensions, StyleSheet} from 'react-native';

const {width} = Dimensions.get('window');
const isSmallScreen = width < 375;
const baseFontSize = isSmallScreen ? 14 : 16;

const COLORS = {
  gray: {
    gray50: '#fefefe',
    gray100: '#fafafb',
    gray200: '#f8f8f9',
    gray300: '#f5f5f6',
    gray400: '#f3f3f5',
    gray500: '#f0f0f2',
    gray600: '#dadadc',
    gray700: '#aaaaac',
    gray750: '#98989a',
    gray800: '#848485',
    gray900: '#656566',
    gray900shadow: '#65656666',
  },
  orange: {
    orange50: '#fef1e8',
    orange100: '#fcd3b8',
    orange200: '#fabe96',
    orange300: '#f8a066',
    orange400: '#f68d48',
    orange500: '#f4711a',
    orange600: '#de6718',
    orange700: '#ad5012',
    orange800: '#863e0e',
    orange900: '#662f0b',
  },
  base: {
    white: '#FFFFFF',
    black: '#000000',
    base300: '#fafafb',
    transparent: 'rgba(52, 52, 52, 0.8)',
  },
  primary: {
    primary50: '#ece7eb',
    primary100: '#c3b3c2',
    primary200: '#a58fa4',
    primary300: '#7c5c7b',
    primary400: '#633c61',
    primary500: '#3C0B3A',
    primary600: '#370a35',
    primary700: '#2b0829',
    primary800: '#210620',
    primary900: '#190518',
  },
  success: {
    success50: '#ebfcf6',
    success100: '#c1f4e3',
    success200: '#a3efd6',
    success300: '#78e8c3',
    success400: '#5ee4b8',
    success500: '#36dda6',
    success600: '#31c997',
    success700: '#269d76',
    success800: '#1e7a5b',
    success900: '#175d46',
  },
  error: {
    error50: '#ffe6ec',
    error100: '#ffb0c4',
    error200: '#ff8aa7',
    error300: '#ff547f',
    error400: '#ff3366',
    error500: '#ff0040',
    error600: '#e8003a',
    error700: '#b5002d',
    error800: '#8c0023',
    error900: '#6b001b',
  },
};

// Use without destruction
const THEME = StyleSheet.create({
  mainContainer: {
    display: 'flex',
    paddingHorizontal: 16,
    paddingVertical: 10,
    flexDirection: 'column',
    position: 'relative',
  },
  container: {
    flex: 1,
    fontSize: baseFontSize,
    padding: '5%',
  },
  flex: {
    flex: 1,
  },
  grid: {},
});

const FONTS = {
  fonts: {
    poppins: 'Poppins-Light',
    sfPro: 'SF-Pro',
  },
};

export default {
  ...COLORS,
  ...FONTS,
  THEME,
};
