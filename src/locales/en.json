{"welcome": "Welcome", "welcome_to_assetify": "Welcome to  ASSETIFY", "assetify": "ASSETIFY", "please_wait": "Please wait...", "check_your_connection": "Check your connection...", "balance": "Wallet Balance", "twelve_word_seed_phrase": "Your 12 words seed phrase", "continue": "Continue", "continue_to_wallet": "Continue to Wallet", "paste": "Paste", "clear": "Clear", "confirm": "Confirm", "understand": "I understand!", "about": "About", "readMore": "Read More", "readLess": "Read Less", "doNotShowAgain": "Do not show again", "holdToConfirm": "Hold To Confirm", "cancel": "Cancel", "delete": "Delete", "bottomNav": {"wallet": "Wallet", "topAssets": "Top Assets", "calculator": "Calculator", "news": "News", "addWallet": "Add Wallet", "lightning": "Lightning", "exchange": "<PERSON><PERSON><PERSON>"}, "wallet": {"new_wallet": "Create New Wallet", "import_wallet": "Import Existing Wallet", "continue_to_wallet": "Continue to Wallet", "important_notice": "The following seed phrase is the Master Key to your wallet", "tap_checkboxes": "Tap on all checkboxes to confirm you understand the importance of your secret phrase", "please_input_seed_phrase": "Please input seed phrase in the correct order.", "verify_manually": "Verify Manually", "send": "Send", "receive": "Receive", "buy": "Buy", "more_assets": "More Assets", "crypto_loan": "Need a Crypto Loan?", "title": "Home", "assets": "Assets", "hideBalance": "Hide balance", "showBalance": "Show balance", "noAssets": "You don't have any assets yet", "creatingWallet": "Creating wallet...", "importingWallet": "Importing wallet...", "doNotShare": "1. Do not share your {{opt}} with anyone.\n\n2. The {{opt}} is the key to your wallet, keep it safe.\n\n3. Assetify does not store your {{opt}} and we will never ask for it.", "txsAreIrreversible": "Once confirmed, crypto transactions can't be canceled or reversed.", "hooksTitle": "No Wallet? No Problem.", "hook1Title": "Get started straight away", "hook1Text": "Your wallet's available everywhere, and works like any other wallet.", "hook2Title": "Secure by design", "hook2Text": "Your private key is never shared with us. Your keys, your crypto.", "hook3Title": "You're always in control", "hook3Text": "Our wallets are non-custodial. You can always export your seed phrase if you need to.", "suggestionsAccessory": "Type to see suggestions", "howToImport": "Choose how you want to add your wallet", "importWalletSubtitle": "Enter your recovery phrase", "restoreWallet": "Restore a wallet", "restoreWalletSubtitle": "Add wallets you've backed up to your {{opt}} account", "restoreWalletCheck": "Searching for backups...", "authenticate": "Please authenticate yourself", "noRecoveryPhrases": "No recovery phrases found", "noRecoveryPhrasesDescription": "You don't have any recovery phrases backed up to your device keychain", "selectBackupToRestore": "Select a backup to restore", "multipleRecoveryPhrases": "There are multiple recovery phrases backed up to your device", "oneRecoveryPhrase": "There is one recovery phrase backed up to your device", "restoreWalletAndImport": "Enter your password to retrieve your seed phrase and restore your wallet."}, "confirmations": {"seedPhrase1": "If i lose my secret phrase, my funds will be lost forever", "seedPhrase2": "If i expose or share my secret phrase to anybody, my funds can get stolen", "seedPhrase3": "Assetify Support will NEVER reach out to ask for it"}, "messages": {"alert": {"never_share_your_secret_phrase": "Never share your seed phrase with anyone!"}}, "login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "login": "<PERSON><PERSON>", "dontHaveAccount": "Don't have an account yet?", "signUp": "Sign up", "language": "Language"}, "registration": {"title": "Registration", "email": "Email", "password": "Password", "signUp": "Sign up", "alreadyHaveAccount": "Already have an account?", "login": "<PERSON><PERSON>", "registrationTitle": "User Registration", "registrationSubTitle": "Please input the required information to the fields below and complete the registration.", "givenName": "Given Name", "familyName": "Family Name", "telephone": "Phone Number", "companyInfo": "Company Information", "companyName": "Company Name", "city": "City", "country": "Country", "countryCode": "Country Code", "postalCode": "Postal Code", "addressLine": "Address line", "bankInfo": "Bank Information", "bankName": "Bank Name", "accountType": "Account Type", "send": "Send", "completedTitle": "Registration Completed", "completedMessage": "We will send you an email with login URL once your user information is verified. It usually takes about xx business days to verify your account.", "backToLogin": "Back to Login"}, "navNews": "News", "navQuestions": "Questions", "boostAccount": {}, "calculator": {"title": "Calculator", "howMuchYouEarn": "Check how much you can earn!", "properEarnings": "Propel your earnings into space and leave inflation earthbound", "after": "In ", "years": " years", "calcInfo": "This calculator is for informational purposes only. Prizes are subject to change and not guaranteed for the above periods.", "amount": "Amount", "currency": "Cryptocurrency", "plan": "Plan", "profit": "Expected Profit"}, "appDownload": {"download": "Download the app", "worldInYourHands": "The world of cryptocurrencies is in your hands"}, "footer": {"usefulLinks": "Useful links", "services": "Services", "terms": "Terms", "aboutUs": "About Us", "news": "News", "faqs": "Frequently asked questions", "usefulTerms": "Useful cryptocurrency terms", "trade": "Trade cryptocurrencies", "supportedCrypto": "Supported cryptocurrencies", "paymentMethods": "Supported payment methods", "TermsAndCo": "Terms and Conditions", "privacyPolicy": "Privacy Policy", "bonusTerms": "Bonus Program Terms and Conditions", "policy": "AML Policy", "cookies": "Cookies Preferences", "mobileApp": "Downloasd our mobile app", "followUs": "Follow us", "tel": "Telephone", "email": "Email"}, "topAssets": {"title": "Top staking assets", "topAssets": "Top assets", "topProviders": "Top providers", "topProviders_stakedValue": "Staked Value", "topProviders_users": "Users", "favorites": "Favorites", "all": "All", "noAssets": "No assets found", "noProviders": "No providers found", "noFavorites": "No favorites found", "search": "Search"}, "stakingCalculator": {"title": "Staking calculator", "asset": "<PERSON><PERSON>", "selectAsset": "Select", "chooseProvider": "Choose provider", "selectProvider": "Select provider", "period": "Period", "potentialProfit": "Potential profit", "month": "month", "months": "months", "year": "year", "years": "years"}, "news": {"title": "News", "search": "Search", "trending": "Trending", "hot": "Hot", "important": "Important"}, "currencySpecific": {"youHave": "You have", "noTransactions": "Your transactions will appear here", "buyMore": "Buy more", "transactions": "Transactions", "address": "Address", "receivingAddr": "Recipient", "txSent": "Transaction sent!", "txSentDescription": "Your transaction has been sent successfully!", "sent": "<PERSON><PERSON>", "received": "Received", "done": "Done", "canceled": "Canceled", "bitcoinDisc": "Bitcoin is a decentralized digital currency created in 2009 by <PERSON><PERSON>. It enables peer-to-peer transactions without intermediaries, using cryptography to secure and verify transactions on a public ledger called the blockchain.", "ethereumDisc": "Ethereum (ETH) is a decentralized blockchain platform known for its smart contract functionality. Launched in 2015 by Vitalik Buterin, it allows developers to build decentralized applications (dApps). Its native cryptocurrency, Ether (ETH), is used for transaction fees and services. Ethereum aims to enable a more decentralized internet.", "trxDisc": "Tron (TRX) is a decentralized blockchain platform launched in 2017 by Justin Sun. It focuses on content sharing and entertainment, supporting smart contracts and dApps. Its native cryptocurrency, TRX, is used for transactions and network incentives.", "avalancheDisc": "Avalanche (AVAX) is a decentralized blockchain platform launched in 2020. It is designed for high throughput and low latency, supporting smart contracts and decentralized applications (dApps). Avalanche's native cryptocurrency, AVAX, is used for transaction fees, staking, and governance within the network.", "dogecoinDisc": "Dogecoin (DOGE) is a decentralized cryptocurrency that started as a meme in 2013. Created by <PERSON> and <PERSON>, it features the Shiba Inu dog from the \"Doge\" meme as its logo. Dogecoin is known for its active community and use in online tipping and charitable donations.", "xrpDisc": "XRP is a cryptocurrency created by Ripple Labs in 2012. It is designed to facilitate fast, low-cost international money transfers. Unlike many cryptocurrencies, XRP does not rely on a blockchain but uses a consensus ledger to validate transactions. Its primary use is within the Ripple payment protocol to enhance the efficiency of cross-border payments.", "kaspaDisc": "Kaspa (KAS) is a decentralized cryptocurrency focused on high-speed transactions and scalability. It utilizes the BlockDAG (Directed Acyclic Graph) architecture, allowing for parallel blocks and faster confirmation times. Kaspa aims to provide a secure and efficient blockchain network.", "binance-smart-chainDisc": "Binance Smart Chain (BSC) is a decentralized blockchain launched by Binance in 2020. It supports smart contracts and dApps, offering fast transactions and low fees. Its native cryptocurrency, BNB, is used for transactions and governance.", "litecoinDisc": "Litecoin (LTC) is a decentralized cryptocurrency created in 2011 by <PERSON>. It offers faster transactions and lower fees than Bitcoin, with a capped supply of 84 million coins.", "bitcoin-cashDisc": "Bitcoin Cash (BCH) is a decentralized cryptocurrency created in 2017 as a fork of Bitcoin. It was developed to allow for faster transactions and lower fees by increasing the block size limit. BCH aims to be more efficient for everyday transactions.", "solanaDisc": "Solana (SOL) is a high-performance blockchain platform that supports smart contracts and decentralized applications (dApps). It is designed for fast transactions and low fees, with a focus on scalability and energy efficiency."}, "settings": {"title": "Settings", "preferences": "Preferences", "appearance": "Appearance", "privateKey": {"title": "Private Key", "warning": "Private key is crucial for accessing your funds. Handle it with care and avoid sharing it with anyone. Keep your financial assets secure by keeping your private key confidential."}, "publicKey": "Public Key", "seedPhrase": "Seed Phrase", "termsAndServices": "Terms and Services", "version": "Build Version", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "logout": "Logout", "logoutMessage": "Are you sure you want to logout?", "yes": "Yes", "no": "No", "deleteAccount": "Delete Account", "deleteAccountMessage": "Are you sure you want to delete your account?", "deleteAccountWarning": "Warning: This action cannot be undone!", "showPrivateKey": " Show Private Key", "showSeedPhrase": " Show Seed Phrase", "seedPhraseConfirmed": "Seed Phrase Confirmed!", "incorrectSeedPhrase": "Seed Phrase must be 12 words long", "correctSeedPhrase": "Seed Phrase matches 12 words", "privacyAndSecurity": "Privacy and Security", "about": "About"}, "keychain": {"keychainBackup": "Save to {{opt}}", "keychainHomeDescription": "By having your recovery phrase backed up to {{opt}}, you can recover your wallet just by being logged into your {{opt}} account on any device", "keychainLabelDescription": "Setting a label will help you identify your wallet easily in import", "setPasswordDescription": "Setting a password will encrypt your seed phrase backup, adding an extra level of protection if your {{opt}} account is ever compromised", "confirmPassword": "Confirm your password", "confirmPasswordDescription": "You'll need to enter this to recover your funds. If you ever forget it, we can't retrieve it for you", "deleteBackup": "Delete Backup", "deleteBackupDescription": "Are you sure you want to delete your {{opt}} backup? This action cannot be undone!"}, "secondaryAssets": {"title": "Secondary assets", "createWallet": " Create wallet", "createWalletDialog": "Are you sure you want to create these wallets?", "i_agree": "I agree"}, "info": {"title": "About Assetify"}, "copy": "Copy", "successCopy": "Copy successful!", "success": "Success!", "oops": "Oops!", "share": "Share", "questions": "Questions", "on": "On", "off": "Off", "subscribe": {"title": "Subscribe", "intro": "Want to be among the first to know when the platform becomes available?\n", "stay_tuned": "Subscribe to our newsletter to stay updated with our news and updates!", "email": "Enter your email...", "receive_updates": "I would like to receive emails updates and marketing materials from Assetify", "agree_terms": "I have read and agree to the terms of the Privacy policy", "successful_subscribe": "You are subscribed! We will keep you posted. Assetify Team."}, "notifications": {"title": "Notifications", "noNotifications": "No notifications", "markAllAsRead": "Mark all as read"}, "txDetails": {"title": "Transaction Details", "type": "Type", "amount": "Amount", "status": "Status", "senderAddr": "Sender", "receiverAddr": "Receiver", "date": "Date", "fee": "Fee", "txId": "Transaction ID", "invoice": "Invoice"}, "errorHandling": {"errorOccurred": "Unexpected Error", "weAreSorry": "Sorry, Assetify just crashed unexpectedly. Our team has been notified"}, "exchange": {"topNavigationTitle": "<PERSON><PERSON><PERSON>", "payinInputLabel": "You send", "payoutInputLabel": "You get"}}