// dkg.service.ts

import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, { DKGPhase1Output, DKGPhase2Input, DKGPhase2Output, DKGPhase3Input, DKGPhase3Output, DKGPhase4Input, KeepInitMulPhase3to4, KeepInitZeroSharePhase2to3, KeepInitZeroSharePhase3to4, UniqueKeepDerivationPhase2to3 } from "../../../specs/NativeSampleModule";
import { MPCClientService } from "../mpc-client";
import { ProofCommitment, DKGSession } from "../../../specs/common-payloads";
import { Party, BroadcastDerivationPhase2to4, TransmitInitZeroSharePhase2to4, BroadcastDerivationPhase3to4, TransmitInitZeroSharePhase3to4, TransmitInitMulPhase3to4, BroadcastProofCommitment } from "../../../specs/keygen-payloads";
import { WebSocketBroadcastMessage, WebSocketTransmitMessage } from "../../../specs/network-payloads";
import { registerDkgEvents } from "../events/base-events";
import crypto from 'crypto'
import { NativeModules } from "react-native";


export class KeyGenerationService extends MPCClientService {


  private sessionData?: DKGSession;
  // DKG-Specific Fields
  public party?: Party;

  private phase1Output?: DKGPhase1Output;
  private phase2Output?: DKGPhase2Output;
  private phase3Output?: DKGPhase3Output;

  private receivedFragments: string[] = [];
  private allCommitments: ProofCommitment[] = [];

  private zeroShareKeep2to3: KeepInitZeroSharePhase2to3[] = [];
  private bipKeep2to3: UniqueKeepDerivationPhase2to3[] = [];

  public receivedBroadcastDerivationPhase2to4: BroadcastDerivationPhase2to4[] = [];
  public receivedZeroSharePhase2to4: TransmitInitZeroSharePhase2to4[] = [];

  private zeroShareKeep3to4: KeepInitZeroSharePhase3to4[] = [];
  private mulShareKeep3to4: KeepInitMulPhase3to4[] = [];
  public receivedBroadcastDerivationPhase3to4: BroadcastDerivationPhase3to4[] = [];
  public receivedZeroSharePhase3to4: TransmitInitZeroSharePhase3to4[] = [];
  public receivedMulSharePhase3to4: TransmitInitMulPhase3to4[] = [];

  private bipBroadcast2to3: Record<number, BroadcastDerivationPhase2to4> = {};
  private bipBroadcast3to4: Record<number, BroadcastDerivationPhase3to4> = {};

  // A promise that will be resolved when DKG is complete.
  private dkgCompletePromise?: Promise<Party>;
  // The resolver function for the above promise.
  private dkgCompleteResolver?: (party: Party) => void;

  /**
   * Start the DKG session
   */
  async startDkgSession(session: DKGSession, networkParams: { socketURL: string, namespace: string, sessionId: string}) {
    await this.initializeBaseSocket(networkParams);
    this.sessionData = session;
    this.sessionIDWS = networkParams.sessionId;
    const partyIndex = this.getPartyIndex();
    console.log('STARTING!')
    console.log(this.sessionIDWS)
    this.emitWithSession('register', { sessionId: this.sessionIDWS, partyIndex, dsg: false });
    console.log(`DKG session started with ID: ${networkParams.sessionId}`);

    this.dkgCompletePromise = new Promise<Party>((resolve) => {
        this.dkgCompleteResolver = resolve;
      });

      return this.dkgCompletePromise;

  }

  /**
   * Our child-specific event registration is now delegated
   * to the `registerDkgEvents` function.
   */
  public registerChildEvents(): void {
    registerDkgEvents(this);
  }

  public resetState(): void {
    console.log('Resetting DKG state...');
    this.phase1Output = undefined;
    this.phase2Output = undefined;
    this.phase3Output = undefined;
    this.party = undefined;

    this.receivedFragments = [];
    this.allCommitments = [];
    this.zeroShareKeep2to3 = [];
    this.bipKeep2to3 = [];
    this.receivedBroadcastDerivationPhase2to4 = [];
    this.receivedZeroSharePhase2to4 = [];
    this.zeroShareKeep3to4 = [];
    this.mulShareKeep3to4 = [];
    this.receivedBroadcastDerivationPhase3to4 = [];
    this.receivedZeroSharePhase3to4 = [];
    this.receivedMulSharePhase3to4 = [];
    this.bipBroadcast2to3 = {};
    this.bipBroadcast3to4 = {};
  }

  // --------------------------------------
  // PHASE FLOW METHODS (called by events)
  // --------------------------------------

  public async generatePhase1(): Promise<void> {
    this.resetState();

    const session: DKGSession = {
      session: {
        parameters: {
          threshold: this.getThreshold(),
          share_count: this.getShareCount(),
        },
        party_index: this.getPartyIndex(),
        session_id: new Array(32).fill(0),
      },
    };

    this.phase1Output = DKGHandler.dkgPhase1(session);
    if (!this.phase1Output) {
      throw new Error('phase1Dkg returned null/undefined');
    }

    // Keep our own fragment
    const ownFragment = this.phase1Output.fragments[this.getPartyIndex() - 1];
    this.receivedFragments.push(ownFragment!);

    // Send each fragment to the correct party
    this.phase1Output.fragments.forEach((fragment, idx) => {
      if (idx !== (this.getPartyIndex() - 1)) {
        this.emitWithSession('sendFragment', {
          senderIndex: this.getPartyIndex(),
          receiverIndex: idx + 1,
          fragment,
        });
      }
    });

    // Wait for "allFragmentsReceived"
    await this.waitForEvent<{ partyIndex: number }>(
      'fragmentsComplete',
      (data) => {
        console.log(data)
        return data.partyIndex === this.getPartyIndex();
      }
    );
  }

  public async processPhase2(): Promise<void> {
    const phase2Input: DKGPhase2Input = {
      session: {
        parameters: {
          threshold: this.getThreshold(),
          share_count: this.getShareCount(),
        },
        party_index: this.getPartyIndex(),
        session_id: new Array(32).fill(0),
      },
      poly_fragments: this.receivedFragments,
    };

    this.phase2Output = DKGHandler.dkgPhase2(phase2Input);
    if (!this.phase2Output) {
      throw new Error('phase2Dkg returned null/undefined');
    }

    // 1) Broadcast derivation
    this.bipBroadcast2to3[this.getPartyIndex()] = this.phase2Output.bip_broadcast;
    this.receivedBroadcastDerivationPhase2to4.push(this.phase2Output.bip_broadcast);
    this.emitWithSession('broadcast', {
      type: 'BroadcastDerivationPhase2to4',
      payload: this.phase2Output.bip_broadcast,
    });

    await this.waitForEvent<{ partyIndex: number }>(
      'broadcastsComplete',
      (data) => data.partyIndex === this.getPartyIndex(),
    );

    // 2) Zero shares
    for (const zeroTx of this.phase2Output.zero_transmit) {
      if (zeroTx.parties.receiver !== this.getPartyIndex()) {
        this.emitWithSession('sendZeroSharePhase2to4', {
          type: 'TransmitInitZeroSharePhase2to4',
          payload: zeroTx,
        });
      }
    }

    await this.waitForEvent<{ partyIndex: number }>(
      'allZeroSharePhase2to4Received',
      (data) => data.partyIndex === this.getPartyIndex(),
    );
    console.log('All Zero Share Phase 2->4 received successfully.');
  }

  public async processPhase3(): Promise<void> {
    const phase3Input: DKGPhase3Input = {
      session: {
        parameters: {
          threshold: this.getThreshold(),
          share_count: this.getShareCount(),
        },
        party_index: this.getPartyIndex(),
        session_id: new Array(32).fill(0),
      },
      zero_kept: this.phase2Output!.zero_keep,
      bip_kept: this.phase2Output!.bip_keep,
    };

    this.phase3Output = DKGHandler.dkgPhase3(phase3Input);
    if (!this.phase3Output) {
      throw new Error('phase3Dkg returned null/undefined');
    }

    // 1) Broadcast derivation (Phase 3->4)
    const broadcast3to4 = this.phase3Output.bip_broadcast;
    this.bipBroadcast3to4[this.getPartyIndex()] = broadcast3to4;
    this.receivedBroadcastDerivationPhase3to4.push(broadcast3to4);

    this.emitWithSession('broadcastPhase3', {
      type: 'BroadcastDerivationPhase3to4',
      payload: broadcast3to4,
    });

    await this.waitForEvent<{ partyIndex: number }>(
      'broadcastsCompletePhase3',
      (data) => data.partyIndex === this.getPartyIndex(),
    );

    // 2) Zero shares Phase 3->4
    for (const zeroTx of this.phase3Output.zero_transmit) {
      if (zeroTx.parties.receiver !== this.getPartyIndex()) {
        this.emitWithSession('sendZeroSharePhase3to4', {
          type: 'TransmitInitZeroSharePhase3to4',
          payload: zeroTx,
        });
      }
    }
    await this.waitForEvent<{ partyIndex: number }>(
      'allZeroSharePhase3to4Received',
      (data) => data.partyIndex === this.getPartyIndex(),
    );

    // 3) Mul shares
    for (const mulTx of this.phase3Output.mul_transmit) {
      if (mulTx.parties.receiver !== this.getPartyIndex()) {
        this.emitWithSession('sendMulSharePhase3to4', {
          type: 'TransmitInitMulSharePhase3to4',
          payload: mulTx,
        });
      }
    }
    await this.waitForEvent<{ partyIndex: number }>(
      'allMulSharePhase3to4Received',
      (data) => data.partyIndex === this.getPartyIndex(),
    );
    console.log('All Mul Share Phase 3->4 received successfully.');
  }

  public async processPhase4(): Promise<Party> {
    // 1) Broadcast proof commitment

    
    this.allCommitments.push(this.phase2Output!.proof_commitment)
    
    this.emitWithSession('broadcastProofCommitment', {
      type: 'BroadcastProofCommitment',
      payload: {
        sender_index: this.getPartyIndex(),
        proof_commitment: this.phase2Output!.proof_commitment,
      },
    });

    console.log('HERRE')

    // Wait for all proof commitments
    await this.waitForEvent<{ partyIndex: number }>(
      'allBroadcastsProofCommitmentsCompleted',
      (data) => data.partyIndex === this.getPartyIndex(),
    );

    // 2) Finalize
    const phase4Input: DKGPhase4Input = {
      session: {
        parameters: {
          threshold: this.getThreshold(),
          share_count: this.getShareCount(),
        },
        party_index: this.getPartyIndex(),
        session_id: new Array(32).fill(0),
      },
      poly_point: this.phase2Output!.poly_point,
      proofs_commitments: this.allCommitments,
      zero_kept: this.phase3Output!.zero_keep,
      zero_received_phase2: this.receivedZeroSharePhase2to4,
      zero_received_phase3: this.receivedZeroSharePhase3to4,
      mul_kept: this.phase3Output!.mul_keep,
      mul_received: this.receivedMulSharePhase3to4,
      bip_broadcast_2to4: this.bipBroadcast2to3,
      bip_broadcast_3to4: this.bipBroadcast3to4,
    };

    const phase4Output = DKGHandler.dkgPhase4(phase4Input);
    if (!phase4Output) {
      throw new Error('phase4Dkg returned null/undefined');
    }

    this.emitWithSession('done', {
      partyIndex: this.getPartyIndex(),
      ETHDerivedAddress: phase4Output.party.eth_address,
      BTCDerivedAddress: phase4Output.party.btc_address,
    });

    this.party = phase4Output.party;
    return phase4Output.party;
  }

  // -----------------------------
  // HANDLER METHODS (for events)
  // -----------------------------
  public handleReceivedFragment(data: { senderIndex: number; fragment: string }) {
    console.log(
      `Received fragment from Player ${data.senderIndex}: ${data.fragment}`,
    );
    this.receivedFragments.push(data.fragment);
    console.log(this.receivedFragments.length)

    console.log(this.getShareCount())
    console.log(this.receivedFragments.length === this.getShareCount())
    if (this.receivedFragments.length === this.getShareCount()) {
      this.emitWithSession('allFragmentsReceived', { partyIndex: this.getPartyIndex() });
    }
  }

  public handleBroadcast(data: WebSocketBroadcastMessage) {
    switch (data.type) {
      case 'BroadcastDerivationPhase2to4':
        const bcast2to4 = data.payload as BroadcastDerivationPhase2to4;
        this.receivedBroadcastDerivationPhase2to4.push(bcast2to4);
        this.bipBroadcast2to3[bcast2to4.sender_index] = bcast2to4;

        if (this.receivedBroadcastDerivationPhase2to4.length === this.getShareCount()) {
          this.emitWithSession('allBroadcastsReceived', { partyIndex: this.getPartyIndex() });
        }
        break;

      default:
        console.warn(`Unknown broadcast type: ${data.type}`);
    }
  }

  public processBroadcastDerivationPhase3to4(data: WebSocketBroadcastMessage): void {
    if (data.type !== 'BroadcastDerivationPhase3to4') {
      return;
    }

    const bcast3to4 = data.payload as BroadcastDerivationPhase3to4;
    console.log(
      `Received BroadcastDerivationPhase3to4 from Player ${bcast3to4.sender_index}`,
    );

    this.receivedBroadcastDerivationPhase3to4.push(bcast3to4);
    this.bipBroadcast3to4[bcast3to4.sender_index] = bcast3to4;

    if (this.receivedBroadcastDerivationPhase3to4.length === this.getShareCount()) {
      this.emitWithSession('allBroadcastsReceivedPhase3', { partyIndex: this.getPartyIndex() });
    }
  }

  public handleReceivedProofCommitment(data: WebSocketBroadcastMessage): void {
    if (data.type !== 'BroadcastProofCommitment') {
      return;
    }
    const payload = data.payload as BroadcastProofCommitment;
    this.allCommitments.push(payload.proof_commitment);

    console.log('PROOF COMMITMENT!')

    console.log(this.allCommitments.length)
    console.log(this.getShareCount())

    if (this.allCommitments.length === this.getShareCount()) {
      this.emitWithSession('allBroadcastsProofCommitmentsReceived', {
        partyIndex: this.getPartyIndex(),
      });
    }
  }

  public handleReceivedShare<T>(
    data: WebSocketTransmitMessage,
    storage: T[], 
    eventName: string,
    expectedCount: number,
    shareType: string
  ): void {
    console.log(`Received ${shareType} from Player ${data.payload.parties.sender}`);
    storage.push(data.payload as T);

    if (storage.length === expectedCount) {
      this.emitWithSession(eventName, { partyIndex: this.getPartyIndex() });
    }
  }

  public getPartyIndex(): number {
    if(!this.sessionData?.session.party_index) {
        throw new Error('Party index not set');
    }
    return this.sessionData?.session.party_index
}
public getShareCount(): number {
    if(!this.sessionData?.session.parameters.share_count) {
        throw new Error('Share count not set');
    }
    return this.sessionData?.session.parameters.share_count
}
public getThreshold(): number {
    if(!this.sessionData?.session.parameters.threshold) {
        throw new Error('Threshold not set');
    }
    return this.sessionData?.session.parameters.threshold
}

public emitWithSession(event: string, data: any): void {
    console.log(event, data)
    this.socket.emit(event, { sessionId: this.sessionIDWS, ...data });
}

public async encryptKeyShare(
  mnemonic: string,
  keyShareData: string | Buffer,
  salt = 'random-app-salt-placeholder'
): Promise<{
  shareCipherBundle: { iv: string; ciphertext: string; authTag: string };
  wrappedKeyBundle: { iv: string; wrapped: string; authTag: string };
}> {
  const { WalletManagerBridge } = NativeModules;

  // 1. Derive the master key from the mnemonic using PBKDF2.
  //    WalletManagerBridge.pbkdf2 returns a hex string.
  const derivedKeyHex: string = await WalletManagerBridge.pbkdf2(
    mnemonic,
    salt,
    100000,
    32
  );
  const masterKey = Buffer.from(derivedKeyHex, 'hex');

  // 2. Generate a fresh random ephemeral key (32 bytes for AES-256)
  const ephemeralKey = crypto.randomBytes(32);

  // 3. Encrypt the key share using the ephemeral key (AES-256-GCM)
  const ivShare = crypto.randomBytes(12); // 12-byte IV is standard for GCM
  const cipherShare = crypto.createCipheriv('aes-256-gcm', ephemeralKey, ivShare);
  let shareCiphertext = cipherShare.update(keyShareData);
  shareCiphertext = Buffer.concat([shareCiphertext, cipherShare.final()]);
  const shareAuthTag = cipherShare.getAuthTag();

  const shareCipherBundle = {
    iv: ivShare.toString('hex'),
    ciphertext: shareCiphertext.toString('hex'),
    authTag: shareAuthTag.toString('hex'),
  };

  // 4. Wrap (encrypt) the ephemeral key using the master key (AES-256-GCM)
  const ivWrap = crypto.randomBytes(12); // New IV for wrapping
  const cipherWrap = crypto.createCipheriv('aes-256-gcm', masterKey, ivWrap);
  let wrappedKey = cipherWrap.update(ephemeralKey);
  wrappedKey = Buffer.concat([wrappedKey, cipherWrap.final()]);
  const wrapAuthTag = cipherWrap.getAuthTag();

  const wrappedKeyBundle = {
    iv: ivWrap.toString('hex'),
    wrapped: wrappedKey.toString('hex'),
    authTag: wrapAuthTag.toString('hex'),
  };

  // Return both bundles so they can be stored in your backend.
  return {
    shareCipherBundle,
    wrappedKeyBundle,
  };
}


public async decryptKeyShare(
  mnemonic: string,
  encryptedBundles: {
    shareCipherBundle: { iv: string; ciphertext: string; authTag: string };
    wrappedKeyBundle: { iv: string; wrapped: string; authTag: string };
  },
  salt = 'random-app-salt-placeholder'
): Promise<Buffer> {
  const { WalletManagerBridge } = NativeModules;

  // 1. Re-derive the master key from the mnemonic using PBKDF2.
  const derivedKeyHex: string = await WalletManagerBridge.pbkdf2(
    mnemonic,
    salt,
    100000,
    32
  );
  const masterKey = Buffer.from(derivedKeyHex, 'hex');

  // 2. Unwrap (decrypt) the ephemeral key using the master key.
  const wrappedKeyBundle = encryptedBundles.wrappedKeyBundle;
  const ivWrap = Buffer.from(wrappedKeyBundle.iv, 'hex');
  const wrappedKey = Buffer.from(wrappedKeyBundle.wrapped, 'hex');
  const wrapAuthTag = Buffer.from(wrappedKeyBundle.authTag, 'hex');

  const decipherWrap = crypto.createDecipheriv('aes-256-gcm', masterKey, ivWrap);
  decipherWrap.setAuthTag(wrapAuthTag);
  let ephemeralKey = decipherWrap.update(wrappedKey);
  ephemeralKey = Buffer.concat([ephemeralKey, decipherWrap.final()]);

  // 3. Decrypt the key share using the unwrapped ephemeral key.
  const shareBundle = encryptedBundles.shareCipherBundle;
  const ivShare = Buffer.from(shareBundle.iv, 'hex');
  const ciphertext = Buffer.from(shareBundle.ciphertext, 'hex');
  const shareAuthTag = Buffer.from(shareBundle.authTag, 'hex');

  const decipherShare = crypto.createDecipheriv('aes-256-gcm', ephemeralKey, ivShare);
  decipherShare.setAuthTag(shareAuthTag);
  let decryptedShare = decipherShare.update(ciphertext);
  decryptedShare = Buffer.concat([decryptedShare, decipherShare.final()]);

  return decryptedShare;
}


}
