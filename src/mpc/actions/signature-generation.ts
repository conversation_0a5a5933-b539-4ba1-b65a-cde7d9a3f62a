// dsg.service.ts

import {Party} from '../../../specs/keygen-payloads';
import {MPCClientService} from '../mpc-client';
import {ethers} from 'ethers';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {
  SignPhase1Output,
  SignPhase2Output,
  SignPhase3Output,
  SignPhase2Input,
  SignPhase3Input,
  SignPhase4Input,
  SignPhase4Output,
} from '../../../specs/NativeSampleModule';
import {WebSocketTransmitMessage} from '../../../specs/network-payloads';
import {
  TransmitPhase1to2,
  TransmitPhase2to3,
  Broadcast3to4,
  SignData,
} from '../../../specs/signature-payloads';
import {registerDsgEvents} from '../events/base-events';
import {DKGSession} from 'specs/common-payloads';
import { networks, payments, script, Transaction } from 'bitcoinjs-lib';
import Big from 'big.js'

export interface IUTXO {
  scriptPubKey: {
      addresses: string[],
      asm: string,
      hex: string,
      type: string,
  },

  prevTxId: string,
  index: number,
  value: string,
  isSpent: boolean,
}
export class SignatureGenerationService extends MPCClientService {
  // DSG-specific fields
  private DKGParty?: Party;
  private sessionData?: DKGSession;

  public signData?: SignData;

  private signPhase1Output?: SignPhase1Output;
  private signPhase2Output?: SignPhase2Output;
  private signPhase3Output?: SignPhase3Output;

  private signTransmitPhase1to2: TransmitPhase1to2[] = [];
  private signTransmitPhase2to3: TransmitPhase2to3[] = [];
  private broadcastSignPhase3: Broadcast3to4[] = [];

  // A promise that will resolve when signing is complete.
  public registerPromise?: Promise<void>;
  // A resolver function for the promise.
  public registerCompletionPromise?: () => void;

  /**
   * Start the DSG session
   */
  async startDsgSession(
    sessionData: DKGSession,
    session: {signData: SignData; party: Party},
    networkParams: {socketURL: string; namespace: string; sessionId: string},
    txSerialized: string,
    chain: 'BTC' | 'ETH',

    utxos?: IUTXO[],
  ) {
    await this.initializeBaseSocket(networkParams);

    // Create a promise and capture its resolve function.
    this.registerPromise = new Promise<void>((resolve, reject) => {
      this.registerCompletionPromise = resolve;
    });

    this.sessionData = sessionData;
    this.signData = session.signData;
    this.DKGParty = session.party;

    const partyIndex = this.getPartyIndex();
    this.emitWithSession('register', {partyIndex, dsg: true, chain});
    console.log(`DSG session started with ID: ${networkParams.sessionId}`);

    await this.registerPromise;



    if (session.signData) {
      await this.runSigningPhases(session.signData, txSerialized);
    }
  }

  /**
   * Example convenience method to run all signing phases end-to-end.
   */
  public async runSigningPhases(signData: SignData, txSerialized: string): Promise<{
    phase4: SignPhase4Output,
    phase3: SignPhase3Output
  }> {
    this.resetState();

    const party = this.DKGParty;

    if (!party) {
      throw new Error(
        'Party not found in store. Make sure DKG is complete and storeParty() was called.',
      );
    }

    await this.proceedSignPhase1(signData, party);
    await this.proceedSignPhase2(signData, party);
    const signPhase3Output = await this.proceedSignPhase3(signData, party);

    const phase4Output = await this.proceedSignPhase4(signData, party);

    console.log('Signing completed. Final signature:', phase4Output.signature);
    return {
      phase4: phase4Output,
      phase3: signPhase3Output
    }

    console.log(signData);

    // Reconstruct the full signature
    const rHex = signPhase3Output.x_coord;
    const sHex = phase4Output.signature.startsWith('0x')
      ? phase4Output.signature.slice(2)
      : phase4Output.signature;
    const recId = phase4Output.rec_id;
    const v = recId + 27; // Ethereum uses v = rec_id + 27

    const signatureObject = {
      r: '0x' + rHex,
      s: '0x' + sHex,
      v: v,
    };

    const ecdsaSignature = ethers.Signature.from(signatureObject);
    const tx = ethers.Transaction.from(JSON.parse(txSerialized));

    tx.signature = ecdsaSignature;

    console.log('Serialized signature:', tx.serialized);

    // let fullSignature = '';
    // const r = "0x" + signatureHex.slice(2,66)
    // const s = "0x" + signatureHex.slice(66,130)
    // const possibleV = [11155111 * 2 + 35, 11155111 * 2 + 36]
    // const signerAddress = party.eth_address

    // Try each candidate v and recover the signer's address
    // for (const v of possibleV) {
    //   const candidateSignature = ethers.Signature.from({r, s, v});
    //   try {
    //     const messageHash: ethers.BytesLike = new Uint8Array(signData.message_hash);

    //     const recoveredAddress = ethers.recoverAddress(messageHash, candidateSignature);
    //     console.log(recoveredAddress)
    //     if (recoveredAddress.toLowerCase() === signerAddress) {
    //       fullSignature = candidateSignature.serialized;
    //       console.log(`Found matching v value: ${v}`);
    //       break;
    //     }
    //   } catch (error) {
    //     console.error(`Error recovering address with v=${v}:`, error);
    //   }
    // }

    // console.log(`Signing completed. Final signature: ${fullSignature}`);
  }

  /**
   * Let `dsg.events.ts` set up all socket event handlers
   */
  public registerChildEvents(): void {
    registerDsgEvents(this);
  }

  public resetState(): void {
    console.log('Resetting DSG signing state...');
    this.signPhase1Output = undefined;
    this.signPhase2Output = undefined;
    this.signPhase3Output = undefined;

    this.signTransmitPhase1to2 = [];
    this.signTransmitPhase2to3 = [];
    this.broadcastSignPhase3 = [];
  }

  // -----------------------------
  // PHASE METHODS
  // -----------------------------
  public async proceedSignPhase1(signData: SignData, party: Party): Promise<void> {
    const phase1Input = {party, sign_data: signData};
    this.signPhase1Output = DKGHandler.signPhase1(phase1Input);

    // Emit the transmissions
    for (const tx of this.signPhase1Output!.transmit) {
      if (tx.parties.receiver !== this.getPartyIndex()) {
        this.emitWithSession('sendSignPhase1to2', {
          type: 'TransmitPhase1to2',
          payload: tx,
        });
      }
    }

    // Wait for "allSignTransmitPhase1to2Received"
    await this.waitForEvent<{partyIndex: number}>(
      'allSignTransmitPhase1to2Received',
      (data) => data.partyIndex === this.getPartyIndex(),
    );
    console.log('All sign shares from Phase 1->2 received.');
  }

  public async proceedSignPhase2(signData: SignData, party: Party): Promise<void> {
    const phase2Input: SignPhase2Input = {
      party,
      sign_data: signData,
      unique_kept: this.signPhase1Output!.unique_keep,
      kept: this.signPhase1Output!.keep,
      received: this.signTransmitPhase1to2,
    };
    this.signPhase2Output = DKGHandler.signPhase2(phase2Input);

    // Emit transmissions
    for (const tx of this.signPhase2Output!.transmit) {
      if (tx.parties.receiver !== this.getPartyIndex()) {
        this.emitWithSession('sendSignPhase2to3', {
          type: 'TransmitPhase2to3',
          payload: tx,
        });
      }
    }

    await this.waitForEvent<{partyIndex: number}>(
      'allSignTransmitPhase2to3Received',
      (data) => data.partyIndex === this.getPartyIndex(),
    );
    console.log('All sign shares from Phase 2->3 received.');
  }

  public async proceedSignPhase3(
    signData: SignData,
    party: Party,
  ): Promise<SignPhase3Output> {
    const phase3Input: SignPhase3Input = {
      party,
      sign_data: signData,
      unique_kept: this.signPhase2Output!.unique_keep,
      kept: this.signPhase2Output!.keep,
      received: this.signTransmitPhase2to3,
    };
    this.signPhase3Output = DKGHandler.signPhase3(phase3Input);

    // Broadcast the Phase3->4 piece
    this.emitWithSession('broadcastSignPhase3', {
      payload: this.signPhase3Output!.broadcast,
    });

    await this.waitForEvent<{partyIndex: number}>(
      'broadcastsCompleteSignPhase3',
      (data) => data.partyIndex === this.getPartyIndex(),
    );
    console.log('All broadcast sign shares from Phase 3->4 received.');

    return this.signPhase3Output;
  }

  public async proceedSignPhase4(
    signData: SignData,
    party: Party,
  ): Promise<SignPhase4Output> {
    const phase4Input: SignPhase4Input = {
      party,
      sign_data: signData,
      x_coord: this.signPhase3Output!.x_coord,
      received: [...this.broadcastSignPhase3, this.signPhase3Output!.broadcast],
      normalize: true,
    };
    const phase4Output: SignPhase4Output = DKGHandler.signPhase4(phase4Input);

    return phase4Output;
  }

  // -----------------------------
  // HANDLERS
  // -----------------------------
  public handleReceivedSignSharePhase1to2(msg: WebSocketTransmitMessage) {
    console.log(
      `Received sign share Phase 1->2 from Player ${msg.payload.parties.sender}`,
    );
    this.signTransmitPhase1to2.push(msg.payload as TransmitPhase1to2);
    if (this.signTransmitPhase1to2.length === this.getThreshold() - 1) {
      this.emitWithSession('allSignTransmitPhase1to2Received', {
        partyIndex: this.getPartyIndex(),
      });
    }
  }

  public handleReceivedSignSharePhase2to3(msg: WebSocketTransmitMessage) {
    console.log(
      `Received sign share Phase 2->3 from Player ${msg.payload.parties.sender}`,
    );
    this.signTransmitPhase2to3.push(msg.payload as TransmitPhase2to3);
    if (this.signTransmitPhase2to3.length === this.getThreshold() - 1) {
      this.emitWithSession('allSignTransmitPhase2to3Received', {
        partyIndex: this.getPartyIndex(),
      });
    }
  }

  public processBroadcastSignPhase3to4(payload: Broadcast3to4) {
    console.log(`Received broadcast sign Phase3->4`);
    this.broadcastSignPhase3.push(payload);
    if (this.broadcastSignPhase3.length === this.getThreshold() - 1) {
      this.emitWithSession('allBroadcastsReceivedSignPhase3', {
        partyIndex: this.getPartyIndex(),
      });
    }
  }

  public getPartyIndex(): number {
    if (!this.sessionData?.session.party_index) {
      throw new Error('Party index not set');
    }
    return this.sessionData?.session.party_index;
  }
  public getShareCount(): number {
    if (!this.sessionData?.session.parameters.share_count) {
      throw new Error('Share count not set');
    }
    return this.sessionData?.session.parameters.share_count;
  }
  public getThreshold(): number {
    if (!this.sessionData?.session.parameters.threshold) {
      throw new Error('Threshold not set');
    }
    return this.sessionData?.session.parameters.threshold;
  }

  public emitWithSession(event: string, data: any): void {
    console.log(event, data);
    this.socket.emit(event, {sessionId: this.sessionIDWS, ...data});
  }
}
