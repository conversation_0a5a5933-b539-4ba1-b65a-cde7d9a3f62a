import { MPCClientService } from "../mpc-client";

// Common events
export function registerBaseMpcEvents(service: MPCClientService) {
  const { socket } = service;

  // "resetState" event
  socket.on('resetState', () => {
    console.log('Received resetState. Resetting state.');
    service.resetState();
  });

  // "readyToConfirm" event
  socket.once('readyToConfirm', (dsg: boolean) => {
    console.log(`Received "readyToConfirm" with dsg=${dsg}`);
    const partyIndex = service.getPartyIndex();
    service.emitWithSession('confirmReady', { partyIndex, dsg });
  });
}

// DKG Events

import { KeyGenerationService } from '../actions/key-generation';

export function registerDkgEvents(service: KeyGenerationService) {
  const { socket } = service;

  // "allPlayersReady"
  socket.on('allPlayersReady', async () => {
    console.log('All players ready for DKG. Starting Phase 1...');
    try {
      await service.generatePhase1();
      console.log('Phase 1 done.');

      await service.processPhase2();
      console.log('Phase 2 done.');

      await service.processPhase3();
      console.log('Phase 3 done.');

      service.party = await service.processPhase4();


      if (service['dkgCompleteResolver']) {
        service['dkgCompleteResolver'](service.party);
      }

      console.log(`DKG complete. Final Party: ${JSON.stringify(service.party)}`);
    } catch (error) {
      console.error(`Error in DKG phases: ${error}`);
    }
  });

  // Receiving fragments
  socket.on('receiveFragment', (data: { senderIndex: number; fragment: string }) => {
    service.handleReceivedFragment(data);
  });

  // Receiving broadcast (phase2->4)
  socket.on('broadcast', (data: WebSocketBroadcastMessage) => {
    service.handleBroadcast(data);
  });

  // Receiving broadcast (phase3->4)
  socket.on('broadcastPhase3', (data: WebSocketBroadcastMessage) => {
    service.processBroadcastDerivationPhase3to4(data);
  });

  // Receiving proof commitments
  socket.on('broadcastProofCommitment', (data: WebSocketBroadcastMessage) => {
    service.handleReceivedProofCommitment(data);
  });

  // Receiving zero/mul shares
  socket.on('receiveZeroSharePhase2to4', (data: WebSocketTransmitMessage) =>
    service.handleReceivedShare(
      data,
      service.receivedZeroSharePhase2to4,
      'allZeroSharePhase2to4Received',
      service.getShareCount() - 1,
      'Zero Share Phase 2->4',
    ),
  );

  socket.on('receiveZeroSharePhase3to4', (data: WebSocketTransmitMessage) =>
    service.handleReceivedShare(
      data,
      service.receivedZeroSharePhase3to4,
      'allZeroSharePhase3to4Received',
      service.getShareCount() - 1,
      'Zero Share Phase 3->4',
    ),
  );

  socket.on('receiveMulSharePhase3to4', (data: WebSocketTransmitMessage) =>
    service.handleReceivedShare(
      data,
      service.receivedMulSharePhase3to4,
      'allMulSharePhase3to4Received',
      service.getShareCount() - 1,
      'Mul Share Phase 3->4',
    ),
  );
}

// DSG Events

import { SignatureGenerationService } from '../actions/signature-generation';
import { WebSocketBroadcastMessage, WebSocketTransmitMessage } from "../../../specs/network-payloads";
import { Broadcast3to4, SignData } from "../../../specs/signature-payloads";

export function registerDsgEvents(service: SignatureGenerationService) {
  const { socket } = service;
  socket.on('allPlayersReady', async (dsg: boolean) => {

    if (dsg) {
      try {

        let signData = {
          sign_id: Array(32).fill(0),
          counterparties: [2],
          message_hash: Array.from(Buffer.from('f9c8af661d881567eea7599723091999d4587750c0a86b9c4e7b6b05ee38bf44', 'hex'))
        } as SignData


        service.signData = signData;

        console.log('All players ready for DSG. Starting Phase 1...');

        // COMPLETE THE REGISTER PROMISE HERE!
        if (service.registerCompletionPromise) {
          service.registerCompletionPromise();
          // Optionally, reset the promise and resolver if needed:
          service.registerPromise = undefined;
          service.registerCompletionPromise = undefined;
        }


      } catch (error) {
        console.error(`Error in DSG phases: ${error}`);
      }
    }
    console.log('All players ready for DSG. Waiting for signRequest or external trigger...');
  });

  // Phase 1->2 transmissions
  socket.on('sendSignPhase1to2', (msg: WebSocketTransmitMessage) => {
    service.handleReceivedSignSharePhase1to2(msg);
  });

  // Phase 2->3 transmissions
  socket.on('sendSignPhase2to3', (msg: WebSocketTransmitMessage) => {
    service.handleReceivedSignSharePhase2to3(msg);
  });

  // Broadcast for Phase 3->4
  socket.on('broadcastSignPhase3', (msg: WebSocketBroadcastMessage) => {
    const payload = msg.payload as Broadcast3to4;
    service.processBroadcastSignPhase3to4(payload);
  });
}
