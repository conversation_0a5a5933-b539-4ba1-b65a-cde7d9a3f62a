const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');
const {withSentryConfig} = require('@sentry/react-native/metro');
const path = require('path');

const defaultConfig = getDefaultConfig(__dirname);
const {assetExts, sourceExts} = defaultConfig.resolver;

const config = {
  transformer: {
    babelTransformerPath: require.resolve('react-native-svg-transformer'),
  },
  resolver: {
    assetExts: assetExts.filter((ext) => ext !== 'svg'),
    sourceExts: [...sourceExts, 'svg'],

    extraNodeModules: {
      assert: require.resolve('assert'),
      crypto: require.resolve('react-native-quick-crypto'),
      stream: require.resolve('stream-browserify'),
      fs: require.resolve('react-native-fs'),
      path: require.resolve('react-native-path'),
      querystring: require.resolve('querystring-es3'),
      url: require.resolve('react-native-url-polyfill'),
    },
    watchFolders: [path.resolve(__dirname, 'node_modules')],
  },
};

module.exports = withSentryConfig(mergeConfig(defaultConfig, config));
