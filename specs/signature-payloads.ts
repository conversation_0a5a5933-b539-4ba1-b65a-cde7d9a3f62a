import { AffinePoint, HashOutput, OTEDataToSender, PartiesMessage, Scalar } from "./common-payloads";

export interface TransmitPhase1to2 {
    parties: PartiesMessage;
    commitment: HashOutput;
    mul_transmit: OTEDataToSender;
}

export interface TransmitPhase2to3 {
    parties: PartiesMessage;
    gamma_u: AffinePoint;
    gamma_v: AffinePoint;
    psi: Scalar;
    public_share: AffinePoint;
    instance_point: AffinePoint;
    salt: number[]; // Vec<u8>
    mul_transmit: MulDataToReceiver;
}

export interface Broadcast3to4 {
    u: Scalar;
    w: Scalar;
}

export interface SignData {
    sign_id: number[]; // Vec<u8>
    counterparties: number[]; // Vec<u8>
    message_hash: number[]; // [u8; 32]
}

export interface MulDataToReceiver {
    // Define fields based on Rust's MulDataToReceiver struct
    // For now, we'll represent it as any
    [key: string]: any;
}
