export interface PartiesMessage {
    sender: number;
    receiver: number;
}


export type HashOutput = number[]; // Represents [u8; 32]
export type Scalar = string; // Hexadecimal string representation
export type AffinePoint = string; // Hexadecimal string representation of a compressed point

export interface Parameters {
    threshold: number;
    share_count: number;
}

export interface DKGSession {
    session: {
      parameters: Parameters;
      party_index: number;
      session_id: number[];
    };
}

export interface MulReceiver {
    ot_sender: string;
    nonce: string;
    // ... other fields
}



export interface MulSender {
    correlation: boolean[];
    vec_r: string[];
    ot_receiver: string;
    // ... other fields
}
  
  
  export interface SeedPair {
    my_index: number;
    their_index: number;
    seed1: number[];
    seed2: number[];
}
  

export interface ZeroShare {
    seed_pairs: SeedPair[];
    // ... other fields
}

export interface EncProof {
    // Define fields based on Rust's EncProof struct
    field1: number[]; // Example field
    field2: number[]; // Example field
    // ... add other fields as necessary
}

// Existing interfaces
export interface ProofCommitment {
    index: number;
    proof: DLogProof;
    commitment: number[]; // Assuming HashOutput is a byte array
  }
  
  export interface DLogProof {
    point: string;
    proofs: EncProof[];           // Update 'any' to EncProof[]
    rand_commitments: EncProof[]; // Update 'any' to EncProof[]
}
  

export interface OTSender {
    s: Scalar;
    proof: DLogProof;
}
  
  export interface OTReceiver {
    seed: number[]; // Represents [u8; SECURITY]
}
  
  export interface ErrorOT {
    description: string;
}

export interface OTEDataToSender {
    correlation: boolean[];
    vec_r: Scalar[]; // Scalars as hex strings
}

export interface DerivData {
    depth: number;
    child_number: number;
    parent_fingerprint: number[];
    poly_point: string;
    pk: string;
    chain_code: number[];
}
  