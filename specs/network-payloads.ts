import { BroadcastDerivationPhase2to4, BroadcastDerivationPhase3to4, BroadcastProofCommitment, ProofCommitment, TransmitInitMulPhase3to4, TransmitInitZeroSharePhase2to4, TransmitInitZeroSharePhase3to4, TransmitPolyFragment } from "./keygen-payloads";
import { Broadcast3to4, TransmitPhase1to2, TransmitPhase2to3 } from "./signature-payloads";

export type TransmitPayload =
  | TransmitPolyFragment
  | TransmitInitMulPhase3to4
  | TransmitInitZeroSharePhase2to4
  | TransmitInitZeroSharePhase3to4
  | TransmitPhase1to2
  | TransmitPhase2to3;

export interface WebSocketTransmitMessage {
  type: 'TransmitPolyFragment' | 
        'TransmitInitMulPhase3to4' | 
        'TransmitInitZeroSharePhase2to4' | 
        'TransmitInitZeroSharePhase3to4' |
        'TransmitPhase1to2' |
        'TransmitPhase2to3'
  payload: TransmitPayload;
}

export type BroadcastPayload =
  | BroadcastDerivationPhase2to4
  | BroadcastDerivationPhase3to4
  | Broadcast3to4
  | BroadcastProofCommitment

export interface WebSocketBroadcastMessage {
  type: 'BroadcastDerivationPhase2to4' | 
        'BroadcastDerivationPhase3to4' |
        'BroadcastProofCommitment' |
        'BroadcastSignPhase3to4'
  payload: BroadcastPayload;
}
