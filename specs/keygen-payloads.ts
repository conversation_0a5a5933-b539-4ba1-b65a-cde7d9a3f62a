import {Scalar} from './common-payloads';

export interface PartiesMessage {
  sender: number;
  receiver: number;
}

export interface Parameters {
  threshold: number;
  share_count: number;
}

export interface DKGSession {
  session: {
    parameters: Parameters;
    party_index: number;
    session_id: number[];
  };
}

export interface MulReceiver {
  ot_sender: string;
  nonce: string;
}

export interface MulSender {
  correlation: boolean[];
  vec_r: string[];
  ot_receiver: string;
}

export interface SeedPair {
  my_index: number;
  their_index: number;
  seed1: number[];
  seed2: number[];
}

export interface ZeroShare {
  seed_pairs: SeedPair[];
}

export interface EncProof {
  // Based on Rust's EncProof struct
  field1: number[];
  field2: number[];
}

export interface ProofCommitment {
  index: number;
  proof: DLogProof;
  commitment: number[]; // Assuming HashOutput is a byte array
}

export interface DLogProof {
  point: string;
  proofs: EncProof[];
  rand_commitments: EncProof[];
}

export interface OTSender {
  s: Scalar;
  proof: DLogProof;
}

export interface OTReceiver {
  seed: number[]; // Represents [u8; SECURITY]
}

export interface ErrorOT {
  description: string;
}

export interface OTEDataToSender {
  correlation: boolean[];
  vec_r: Scalar[]; // Scalars as hex strings
}

export interface DerivData {
  depth: number;
  child_number: number;
  parent_fingerprint: number[];
  poly_point: string;
  pk: string;
  chain_code: number[];
}

// Newly defined interfaces

export interface Party {
  parameters: Parameters;
  party_index: number;
  session_id: number[];
  poly_point: string;
  pk: string;
  zero_share: ZeroShare;
  mul_senders: {[key: number]: MulSender};
  mul_receivers: {[key: number]: MulReceiver};
  derivation_data: DerivData;
  eth_address: string;
  btc_address: string;
}

export interface TransmitPolyFragment {
  parties: PartiesMessage; // sender and receiver as numbers
  fragment: string;
}

/**
 * Represents a zero-sharing commitment to be transmitted.
 */
export interface TransmitInitZeroSharePhase2to4 {
  parties: PartiesMessage;
  commitment: number[];
}

/**
 * Represents a zero-sharing data (seeds and salts) to be transmitted.
 */
export interface TransmitInitZeroSharePhase3to4 {
  parties: PartiesMessage;
  seed: number[];
  salt: number[];
}

/**
 * Represents multiplication protocol data to be transmitted.
 */
export interface TransmitInitMulPhase3to4 {
  parties: PartiesMessage;
  dlog_proof: DLogProof;
  nonce: string;
  enc_proofs: EncProof[];
  seed: string;
}

/**
 * Represents BIP derivation commitments to be broadcasted.
 */
export interface BroadcastDerivationPhase2to4 {
  sender_index: number;
  cc_commitment: number[];
}

export interface BroadcastDerivationPhase3to4 {
  sender_index: number;
  aux_chain_code: number[];
  cc_salt: number[];
}

export interface BroadcastProofCommitment {
  sender_index: number;
  proof_commitment: ProofCommitment;
}
