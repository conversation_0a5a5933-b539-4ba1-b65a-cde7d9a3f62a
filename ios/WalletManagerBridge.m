//
//  WalletManagerBridge.m
//  AssetifyApp
//
//  Created by <PERSON> on 3.04.24.
//

#import <React/RCTBridgeModule.h>

@interface RCT_EXTERN_MODULE(WalletManagerBridge, NSObject)

// Expose wallet creation method
RCT_EXTERN_METHOD(createHDWalletFromMnemonic:(NSString *)mnemonic
                  blockchain:(NSString *)blockchain
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose TransactionManager's buildUnsignedSolanaTransaction method
RCT_EXTERN_METHOD(buildUnsignedSolanaTransaction:(NSString *)fromAddress
                  toAddress:(NSString *)toAddress
                  lamports:(nonnull NSNumber *)lamports
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose TransactionManager's signSolanaTransaction method
RCT_EXTERN_METHOD(signSolanaTransaction:(NSString *)fromAddress
                  toAddress:(NSString *)toAddress
                  lamports:(nonnull NSNumber *)lamports
                  blockhash:(NSString *)blockhash
                  privateKeyHex:(NSString *)privateKeyHex
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose TransactionManager's signSolanaTokenTransaction method
RCT_EXTERN_METHOD(signSolanaTokenTransaction:(NSString *)fromAddress
                  toAddress:(NSString *)toAddress
                  senderTokenAddress:(NSString *)senderTokenAddress
                  recipientTokenAddress:(NSString *)recipientTokenAddress
                  amount:(nonnull NSNumber *)amount
                  tokenMint:(NSString *)tokenMint
                  decimals:(nonnull NSNumber *)decimals
                  blockhash:(NSString *)blockhash
                  privateKeyHex:(NSString *)privateKeyHex
                  shouldCreateAssociatedTokenAccount:(BOOL)shouldCreateAssociatedTokenAccount
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose TransactionManager's buildUnsignedSolanaTransaction method
RCT_EXTERN_METHOD(buildUnsignedSolanaTransaction:(NSString *)fromAddress
                  toAddress:(NSString *)toAddress
                  lamports:(nonnull NSNumber *)lamports
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose TransactionManager's signSolanaTransaction method
RCT_EXTERN_METHOD(signSolanaTransaction:(NSString *)fromAddress
                  toAddress:(NSString *)toAddress
                  lamports:(nonnull NSNumber *)lamports
                  blockhash:(NSString *)blockhash
                  privateKeyHex:(NSString *)privateKeyHex
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose TransactionManager's signSolanaTokenTransaction method
RCT_EXTERN_METHOD(signSolanaTokenTransaction:(NSString *)fromAddress
                  toAddress:(NSString *)toAddress
                  senderTokenAddress:(NSString *)senderTokenAddress
                  recipientTokenAddress:(NSString *)recipientTokenAddress
                  amount:(nonnull NSNumber *)amount
                  tokenMint:(NSString *)tokenMint
                  decimals:(nonnull NSNumber *)decimals
                  blockhash:(NSString *)blockhash
                  privateKeyHex:(NSString *)privateKeyHex
                  shouldCreateAssociatedTokenAccount:(BOOL)shouldCreateAssociatedTokenAccount
                  resolver:(RCTPromiseResolveBlock)resolver
                  rejecter:(RCTPromiseRejectBlock)rejecter)

// Expose the PBKDF2 function
RCT_EXTERN_METHOD(pbkdf2:
  (NSString *)password
  withSalt:(NSString *)salt
  iterations:(nonnull NSNumber *)iterations
  dkLen:(nonnull NSNumber *)dkLen
  resolver:(RCTPromiseResolveBlock)resolver
  rejecter:(RCTPromiseRejectBlock)rejecter
)

@end
