#pragma once

#include <AppSpecsJSI.h>

#include <memory>
#include <string>

namespace facebook::react {

class NativeSampleModule : public NativeSampleModuleCxxSpec<NativeSampleModule> {
public:
  NativeSampleModule(std::shared_ptr<CallInvoker> jsInvoker);

  std::string reverseString(jsi::Runtime& rt, std::string input);

  std::string dkg_phase1(jsi::Runtime& rt, std::string data);
  std::string dkg_phase2(jsi::Runtime& rt, std::string phase2_json_in);
  std::string dkg_phase3(jsi::Runtime& rt, std::string phase3_json_in);
  std::string dkg_phase4(jsi::Runtime& rt, std::string phase4_json_in);

  std::string sign_phase1(jsi::Runtime& rt, std::string phase1_json_in);
  std::string sign_phase2(jsi::Runtime& rt, std::string phase2_json_in);
  std::string sign_phase3(jsi::Runtime& rt, std::string phase3_json_in);
  std::string sign_phase4(jsi::Runtime& rt, std::string phase4_json_in);

  std::string party_derive_from_path(jsi::Runtime& rt, std::string json_in);

};

} // namespace facebook::react
