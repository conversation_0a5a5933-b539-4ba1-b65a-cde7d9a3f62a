#include "NativeSampleModule.h"
#include "libmpc_core_bindings.h"

namespace facebook::react {


// Helper function to convert C string to std::string
std::string cstr_to_stdstring(const char* cstr) {
  if (cstr == nullptr) {
    return "";
  }
  return std::string(cstr);
}


NativeSampleModule::NativeSampleModule(std::shared_ptr<CallInvoker> jsInvoker)
    : NativeSampleModuleCxxSpec(std::move(jsInvoker)) {}

std::string NativeSampleModule::reverseString(jsi::Runtime& rt, std::string input) {
  return std::string(input.rbegin(), input.rend());
}

// Implementation of dkgPhase1
std::string NativeSampleModule::dkg_phase1(jsi::Runtime& rt, std::string data) {
  const char* input = data.c_str();
  const char* result = dkls_dkg_phase1(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

// Implementation of dkgPhase1
std::string NativeSampleModule::dkg_phase2(jsi::Runtime& rt, std::string phase2_json_in) {
  const char* input = phase2_json_in.c_str();
  const char* result = dkls_dkg_phase2(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::dkg_phase3(jsi::Runtime& rt, std::string phase3_json_in) {
  const char* input = phase3_json_in.c_str();
  const char* result = dkls_dkg_phase3(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::dkg_phase4(jsi::Runtime& rt, std::string phase4_json_in) {
  const char* input = phase4_json_in.c_str();
  const char* result = dkls_dkg_phase4(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}


// Implementation of dkgPhase1
std::string NativeSampleModule::sign_phase1(jsi::Runtime& rt, std::string phase1_json_in) {
  const char* input = phase1_json_in.c_str();
  const char* result = dkls_sign_phase1(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_sign_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

// Implementation of dkgPhase1
std::string NativeSampleModule::sign_phase2(jsi::Runtime& rt, std::string phase2_json_in) {
  const char* input = phase2_json_in.c_str();
  const char* result = dkls_sign_phase2(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_sign_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::sign_phase3(jsi::Runtime& rt, std::string phase3_json_in) {
  const char* input = phase3_json_in.c_str();
  const char* result = dkls_sign_phase3(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::sign_phase4(jsi::Runtime& rt, std::string phase4_json_in) {
  const char* input = phase4_json_in.c_str();
  const char* result = dkls_sign_phase4(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: dkls_dkg_phase1 returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}

std::string NativeSampleModule::party_derive_from_path(jsi::Runtime& rt, std::string json_in) {
  const char* input = json_in.c_str();
  const char* result = dkls_party_derive_from_path(input);
  
  // Ensure that the result is not null
  if (result == nullptr) {
    return "Error: party_derive_from_path returned null";
  }

  // Convert the C string to std::string
  std::string output = cstr_to_stdstring(result);

  // If the Rust function allocates memory for the string, ensure to free it if necessary
  // For example, if the Rust function uses malloc, you should free it here
  // free((void*)result); // Uncomment if needed

  return output;
}


} // namespace facebook::react