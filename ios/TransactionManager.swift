import Foundation
import WalletCore

@objc(TransactionManager)
class TransactionManager: NSObject {
  
  /**
   Creates a **JSON** representation of a Solana transfer transaction in this format:
   
   {
   "tx_data": {
   "feePayer": "...",
   "instructions": [
   {
   "programId": "********************************",
   "keys": [
   {
   "pubkey": "...",
   "isSigner": true,
   "isWritable": true
   },
   {
   "pubkey": "...",
   "isSigner": false,
   "isWritable": true
   }
   ],
   "data": "AgAAAICWmAAAAAAA"
   }
   ]
   }
   }
   
   - Parameters:
   - fromAddress: Base58-encoded sender (fee payer)
   - toAddress:   Base58-encoded recipient
   - lamports:    The amount to send in lamports
   
   - Returns: A JSON string matching the above structure.
   */
  @objc func buildUnsignedSolanaTransaction(
    fromAddress: String,
    toAddress: String,
    lamports: NSNumber
  ) -> String? {
    
    // 1) We want a single "System Program" transfer instruction (program ID = '********************************')
    //    The data is [4-byte little-endian instruction index=2] + [8-byte little-endian lamports].
    //    Then base64-encode that.
    
    let lamportsValue = lamports.uint64Value
    
    // "instruction index" for SystemProgram.transfer is 2 (little-endian)
    var transferInstructionIndex: UInt32 = 2
    transferInstructionIndex = transferInstructionIndex.littleEndian
    let instructionIndexData = withUnsafeBytes(of: &transferInstructionIndex) { Data($0) }
    
    // lamports in 8-byte little-endian
    var lamportsLE = lamportsValue.littleEndian
    let lamportsData = withUnsafeBytes(of: &lamportsLE) { Data($0) }
    
    // Combined "data" field
    let instructionData = instructionIndexData + lamportsData
    let instructionDataBase64 = instructionData.base64EncodedString()
    
    // 2) Build the JSON structure in Swift Dictionary form
    let dict: [String: Any] = [
      "tx_data": [
        "feePayer": fromAddress,
        "recipient": toAddress,
        "instructions": [
          [
            "programId": "********************************",
            "keys": [
              [
                "pubkey": fromAddress,
                "isSigner": true,
                "isWritable": true
              ],
              [
                "pubkey": toAddress,
                "isSigner": false,
                "isWritable": true
              ]
            ],
            "data": instructionDataBase64
          ]
        ]
      ]
    ]
    
    // 3) Serialize the dictionary to JSON text
    guard let jsonData = try? JSONSerialization.data(withJSONObject: dict, options: []),
          let jsonString = String(data: jsonData, encoding: .utf8)
    else {
      return nil
    }
    
    return jsonString
  }
  
  
  
  /**
   Signs a Solana transfer transaction by building it with the provided parameters and a valid private key.
   
   - Parameters:
   - fromAddress: The base58-encoded sender address
   - toAddress:   The base58-encoded recipient address
   - lamports:    The amount to transfer in lamports (1e9 lamports = 1 SOL)
   - blockhash:   The “recentBlockhash” from the Solana network
   - privateKeyHex: The private key (in hex) used to sign the transaction
   
   - Returns: Hex-encoded **fully signed** transaction (ready to broadcast), or `nil` on error.
   */
  @objc func signSolanaTransaction(
    fromAddress: String,
    toAddress: String,
    lamports: NSNumber,
    blockhash: String,
    privateKeyHex: String
  ) -> String? {
    
    let lamportsValue = lamports.uint64Value
    // 1) Construct the transfer message
    let transfer = TW_Solana_Proto_Transfer.with { tBuilder in
      tBuilder.recipient = toAddress
      tBuilder.value = lamportsValue
    }
    
    // 2) Convert the private key from hex to Data
    guard let privateKeyData = Data(hexString: privateKeyHex) else {
      print("Invalid privateKey hex: \(privateKeyHex)")
      return nil
    }
    
    // 3) Construct the SigningInput with the real private key
    let input = TW_Solana_Proto_SigningInput.with { iBuilder in
      iBuilder.transferTransaction = transfer
      iBuilder.recentBlockhash = blockhash
      iBuilder.sender = fromAddress
      iBuilder.privateKey = privateKeyData
    }
    
    guard let inputData = try? input.serializedData() else {
      print("Failed to serialize SigningInput.")
      return nil
    }
    
    // 4) Sign the transaction
    let resultData = AnySigner.nativeSign(data: inputData, coin: .solana)
    
    // 5) Parse the SigningOutput from the result
    guard let output = try? TW_Solana_Proto_SigningOutput(serializedData: resultData) else {
      print("Failed to parse SigningOutput.")
      return nil
    }
    
    // 6) Return the final signed transaction.
    //    Here, output.encoded is already a String, so we return it directly.
    return output.encoded
  }
  
  /**
   Creates a JSON representation of a Solana token transfer transaction.
   
   This function builds a transaction for transferring an SPL token using the token’s mint address,
   amount, and decimals. Depending on the boolean flag, the transaction either attempts to create the
   recipient’s associated token account (if not already present) before performing the transfer, or
   simply transfers the tokens using pre-computed token addresses.
   
   - Parameters:
   - fromAddress: Base58-encoded sender address (also the fee payer)
   - toAddress: Base58-encoded recipient address (the token account to receive tokens, used when creating an associated token account)
   - senderTokenAddress: Sender's ATA address (already derived)
   - recipientTokenAddress: Recipient's ATA address (already derived)
   - amount: The amount of tokens to transfer in the token’s smallest unit (expressed as NSNumber)
   - tokenMint: The mint address of the SPL token
   - decimals: The number of decimals for the SPL token (expressed as NSNumber)
   - blockhash: The recent blockhash from the Solana network
   - privateKeyHex: The sender’s private key (in hex) used to sign the transaction
   - shouldCreateAssociatedTokenAccount: A boolean flag determining if the recipient’s associated token account should be created if needed.
   
   - Returns: A hex-encoded, fully signed transaction (ready to broadcast), or `nil` on error.
   */
  @objc func signSolanaTokenTransaction(
    fromAddress: String,
    toAddress: String,
    senderTokenAddress: String,
    recipientTokenAddress: String,
    amount: NSNumber,
    tokenMint: String,
    decimals: NSNumber,
    blockhash: String,
    privateKeyHex: String,
    shouldCreateAssociatedTokenAccount: Bool
  ) -> String? {
    
    // Convert the private key from hex into Data.
    guard let privateKeyData = Data(hexString: privateKeyHex) else {
      print("Invalid privateKey hex: \(privateKeyHex)")
      return nil
    }
    
    // Build the SigningInput using the chosen transaction message based on the flag.
    let input = TW_Solana_Proto_SigningInput.with { builder in
      builder.recentBlockhash = blockhash
      builder.sender = fromAddress
      builder.privateKey = privateKeyData
      
      if shouldCreateAssociatedTokenAccount {
        // Build a CreateAndTransferToken message that includes the recipient's main address.
        let createAndTransferMsg = TW_Solana_Proto_CreateAndTransferToken.with { subBuilder in
          subBuilder.amount = amount.uint64Value
          subBuilder.decimals = decimals.uint32Value
          subBuilder.recipientMainAddress = toAddress
          subBuilder.recipientTokenAddress = recipientTokenAddress
          subBuilder.senderTokenAddress = senderTokenAddress
          subBuilder.tokenMintAddress = tokenMint
          // Optionally, add a memo or reference pubkeys here.
        }
        builder.createAndTransferTokenTransaction = createAndTransferMsg
      } else {
        // Build a plain TokenTransfer message that doesn't require the main address.
        let tokenTransferMsg = TW_Solana_Proto_TokenTransfer.with { subBuilder in
          subBuilder.amount = amount.uint64Value
          subBuilder.decimals = decimals.uint32Value
          subBuilder.recipientTokenAddress = recipientTokenAddress
          subBuilder.senderTokenAddress = senderTokenAddress
          subBuilder.tokenMintAddress = tokenMint
          
          // Optionally, add a memo or reference pubkeys here.
        }
        builder.tokenTransferTransaction = tokenTransferMsg
      }
    }
    
    // Serialize the SigningInput.
    guard let inputData = try? input.serializedData() else {
      print("Failed to serialize SigningInput.")
      return nil
    }
    
    // Sign the transaction using AnySigner.
    let resultData = AnySigner.nativeSign(data: inputData, coin: .solana)
    
    // Parse the SigningOutput.
    guard let output = try? TW_Solana_Proto_SigningOutput(serializedData: resultData) else {
      print("Failed to parse SigningOutput.")
      return nil
    }
    
    // Return the final signed transaction (as hex-encoded string or as provided by output.encoded).
    return output.encoded
  }
}



// MARK: - Data+HexString Helper Extension

extension Data {
    /// Initializes Data from a hex string.
    init?(hexString: String) {
        let cleanString = hexString.lowercased().filter { "0123456789abcdef".contains($0) }
        guard cleanString.count % 2 == 0, !cleanString.isEmpty else { return nil }

        var data = Data()
        var index = cleanString.startIndex
        while index < cleanString.endIndex {
            let nextIndex = cleanString.index(index, offsetBy: 2)
            guard nextIndex <= cleanString.endIndex else { return nil }
            let byteString = cleanString[index..<nextIndex]
            if let byte = UInt8(byteString, radix: 16) {
                data.append(byte)
            } else {
                return nil
            }
            index = nextIndex
        }
        self = data
    }
}
