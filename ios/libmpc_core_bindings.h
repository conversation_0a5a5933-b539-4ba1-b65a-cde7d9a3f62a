#include <cstdarg>
#include <cstdint>
#include <cstdlib>
#include <ostream>
#include <new>

extern "C" {

const char *dkls_derive_from_path(const char *derive_json_in);

const char *dkls_party_derive_from_path(const char *derive_json_in);

const char *dkls_derive_child(const char *derive_json_in);

const char *dkls_party_derive_child(const char *derive_json_in);

const char *dkls_dkg_phase1(const char *data);

const char *dkls_dkg_phase2(const char *phase2_json_in);

const char *dkls_dkg_phase3(const char *phase3_json_in);

const char *dkls_dkg_phase4(const char *phase4_json_in);

const char *dkls_re_key(const char *re_key_json_in);

const char *dkls_sign_phase1(const char *phase1_json_in);

const char *dkls_sign_phase2(const char *phase2_json_in);

const char *dkls_sign_phase3(const char *phase3_json_in);

const char *dkls_sign_phase4(const char *phase4_json_in);

const char *dkls_verify_ecdsa_signature(const char *verify_json_in);

}  // extern "C"
