/* eslint-disable no-var */

// NOTE: yarn rn-nodeify --yarn --install crypto,stream,buffer --hack

// Install react-native-quick-crypto
import {install} from 'react-native-quick-crypto';
install();

if (typeof __dirname === 'undefined') global.__dirname = '/';
if (typeof __filename === 'undefined') global.__filename = '';
if (typeof process === 'undefined') {
  global.process = require('process');
} else {
  const bProcess = require('process');
  for (var p in bProcess) {
    if (!(p in process)) {
      process[p] = bProcess[p];
    }
  }
}

process.browser = false;
if (typeof Buffer === 'undefined') global.Buffer = require('buffer').Buffer;

global.location = global.location || {port: 80};
