module.exports = {
  root: true,
  extends: '@react-native',
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: ['./tsconfig.json'],
  },
  plugins: ['@typescript-eslint'],
  globals: {
    localStorage: false,
  },
  rules: {
    indent: [2, 'tab'],
    'no-async-promise-executor': 0,
    'no-buffer-constructor': 0,
    'no-case-declarations': 0,
    'no-console': 0,
    'no-empty': [2, {allowEmptyCatch: true}],
    'no-promise-executor-return': 2,
    'no-shadow': 0,
    'no-undef': 0,
    'no-useless-escape': 0,
    'object-curly-spacing': [
      2,
      'always',
      {
        objectsInObjects: true,
      },
    ],
    semi: 2,

    /* ====================================== TYPESCRIPT PLUGIN ===================================== */
    // Made available via `@typescript-eslint/eslint-plugin`
    '@typescript-eslint/explicit-function-return-type': 2,
    '@typescript-eslint/semi': 2,
    '@typescript-eslint/no-floating-promises': 2,
    '@typescript-eslint/no-misused-promises': [
      2,
      {
        checksVoidReturn: {
          arguments: false,
          attributes: false,
          properties: false,
        },
      },
    ],
    '@typescript-eslint/no-shadow': 2,
    '@typescript-eslint/no-unused-vars': 2,
    '@typescript-eslint/prefer-optional-chain': 2,

    /* ======================================== REACT PLUGIN ======================================== */
    // Made available via `eslint-plugin-react`
    'react/default-props-match-prop-types': 2,
    'react/jsx-equals-spacing': [2, 'never'],
    'react/jsx-curly-spacing': [
      2,
      {
        when: 'never',
        attributes: {allowMultiline: true},
        children: true,
      },
    ],
    'react/jsx-uses-vars': 2,
    'react/jsx-wrap-multilines': 2,
    'react/jsx-tag-spacing': [
      2,
      {
        closingSlash: 'never',
        beforeSelfClosing: 'always',
        afterOpening: 'never',
        beforeClosing: 'never',
      },
    ],
    'react/jsx-indent': [2, 'tab', {indentLogicalExpressions: false}],
    'react/jsx-child-element-spacing': 2,
    'react/no-unsafe': [2, {checkAliases: true}],
    'react/no-unused-prop-types': 2,
    'react/prop-types': 2,

    /* ===================================== REACT NATIVE PLUGIN ==================================== */
    // Made available via `eslint-plugin-react-native`
    'react-native/no-color-literals': 2,
    'react-native/no-single-element-style-arrays': 2,
    'react-native/no-unused-styles': 2,

    /* ========================================== PRETTIER ========================================== */
    'no-mixed-spaces-and-tabs': 0,
  },
};
