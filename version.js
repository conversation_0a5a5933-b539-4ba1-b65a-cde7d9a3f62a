const fs = require("fs")
const path = require("path")

// Your current IOS minimum OS target
const CURRENT_TARGET_IOS = 13

// Your current minimum Android SDK version target
const CURRENT_TARGET_ANDROID = 33

function updateGradleVersion(gradleFilePath, version, versionInt) {
  try {
    let gradleFileContent = fs.readFileSync(gradleFilePath, "utf8")
    gradleFileContent = gradleFileContent.replace(
      /versionCode\s*\d+/g,
      `versionCode ${CURRENT_TARGET_ANDROID}${versionInt}`,
    )
    gradleFileContent = gradleFileContent.replace(
      /versionName\s*['"]\d+\.\d+\.\d+['"]/g,
      `versionName "${version}"`,
    )
    fs.writeFileSync(gradleFilePath, gradleFileContent, "utf8")
    console.log(`Gradle version in ${gradleFilePath} updated to ${version}`)
  } catch (error) {
    console.error(`Error updating Gradle version: ${error.message}`)
  }
}

function updateXcodeVersion(xcodeProjPath, version, versionInt) {
  try {
    const xcodeProjFile = path.join(xcodeProjPath, "project.pbxproj")
    let xcodeProjContent = fs.readFileSync(xcodeProjFile, "utf8")
    xcodeProjContent = xcodeProjContent.replace(
      /MARKETING_VERSION\s*=\s*\d+\.\d+\.\d+/g,
      `MARKETING_VERSION = ${version}`,
    )
    xcodeProjContent = xcodeProjContent.replace(
      /CURRENT_PROJECT_VERSION\s*=\s*\d+/g,
      `CURRENT_PROJECT_VERSION = ${CURRENT_TARGET_IOS}${versionInt}`,
    )
    fs.writeFileSync(xcodeProjFile, xcodeProjContent, "utf8")
    console.log(`Xcode version in ${xcodeProjFile} updated to ${version}`)
  } catch (error) {
    console.error(`Error updating Xcode version: ${error.message}`)
  }
}

function updateInfoPlistVersion(infoPlistPath, version, versionInt) {
  try {
    let infoPlistContent = fs.readFileSync(infoPlistPath, "utf8")
    infoPlistContent = infoPlistContent.replace(
      /<key>CFBundleShortVersionString<\/key>\s*[\n]?<string>\d+\.\d+\.\d+<\/string>/g,
      `<key>CFBundleShortVersionString</key>\n\t<string>${version}</string>`,
    )
    infoPlistContent = infoPlistContent.replace(
      /<key>CFBundleVersion<\/key>\s*<string>\d+<\/string>/g,
      `<key>CFBundleVersion</key>\n\t<string>${CURRENT_TARGET_IOS}${versionInt}</string>`,
    )
    fs.writeFileSync(infoPlistPath, infoPlistContent, "utf8")
    console.log(`iOS Info.plist version in ${infoPlistPath} updated to ${version}`)
  } catch (error) {
    console.error(`Error updating iOS Info.plist version: ${error.message}`)
  }
}

function padNum(num, len = 2) {
  return String(num).padStart(len, "0")
}

function patchIncrementVersion(currentVersion) {
  const [major, minor, patch] = currentVersion.split(".").map(Number)
  const major_ = String(major)
  const minor_ = String(minor)
  const patch_ = String(patch + 1)
  const versionString = `${major_}.${minor_}.${patch_}`
  const versionInteger = padNum(major_) + padNum(minor_, 3) + padNum(patch_)
  return [versionString, versionInteger]
}

function updateVersions(packageJsonPath) {
  try {
    const packageJson = require(packageJsonPath)
    const currentVersion = packageJson.version

    // Patch-increment the version
    const [versionString, versionInteger] = patchIncrementVersion(currentVersion)

    // Update Gradle version
    updateGradleVersion("./android/app/build.gradle", versionString, versionInteger)

    // Update Xcode version
    updateXcodeVersion("./ios/AssetifyApp.xcodeproj/", versionString, versionInteger)

    // Update iOS Info.plist version
    updateInfoPlistVersion("./ios/AssetifyApp/Info.plist", versionString, versionInteger)

    // Update package.json with the new version
    packageJson.version = versionString
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), "utf8")
    console.log(`Package.json version updated to ${versionString}`)
  } catch (error) {
    console.error(`Error reading package.json: ${error.message}`)
  }
}

// Replace 'path/to/your/package.json' with the actual path to your package.json file
// $ node patch.js
updateVersions("./package.json")