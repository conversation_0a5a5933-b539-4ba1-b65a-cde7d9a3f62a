package com.assetifyapp;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WalletDTO {
    public final String blockchain;
    public final String network;
    public final String mnemonic;
    public final String seed;
    public final List<Map<String, String>> xPubsList;
    public final String address;
    public final String privateKey;
    public final String publicKey;

    public WalletDTO(String blockchain, String network, String mnemonic, String seed,
                     List<Map<String, String>> xPubsList, String address, String privateKey, String publicKey) {
        this.blockchain = blockchain;
        this.network = network;
        this.mnemonic = mnemonic;
        this.seed = seed;
        this.xPubsList = xPubsList;
        this.address = address;
        this.privateKey = privateKey;
        this.publicKey = publicKey;
    }
}
