package com.assetifyapp;

import android.util.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

import com.google.protobuf.ByteString;

import wallet.core.java.AnySigner;
import wallet.core.jni.CoinType;
import wallet.core.jni.proto.Solana;

public class TransactionManager {

    /**
     * Creates a JSON representation of a Solana transfer transaction.
     *
     * @param fromAddress Base58-encoded sender (fee payer)
     * @param toAddress Base58-encoded recipient
     * @param lamports The amount to send in lamports
     * @return A JSON string matching the expected structure, or null on error
     */
    public String buildUnsignedSolanaTransaction(
            String fromAddress,
            String toAddress,
            long lamports
    ) {
        try {
            // 1) Build the instruction data: 4-byte little-endian instruction index + 8-byte little-endian lamports
            ByteBuffer buffer = ByteBuffer.allocate(12);
            buffer.order(ByteOrder.LITTLE_ENDIAN);
            buffer.putInt(2); // instruction index for transfer
            buffer.putLong(lamports);

            // Convert to Base64
            String instructionDataBase64 = Base64.encodeToString(buffer.array(), Base64.NO_WRAP);

            // 2) Build the JSON structure
            JSONObject txData = new JSONObject();
            JSONObject jsonObj = new JSONObject();

            // Build the keys array
            JSONArray keysArray = new JSONArray();

            // Sender key
            JSONObject senderKeyObj = new JSONObject();
            senderKeyObj.put("pubkey", fromAddress);
            senderKeyObj.put("isSigner", true);
            senderKeyObj.put("isWritable", true);
            keysArray.put(senderKeyObj);

            // Recipient key
            JSONObject recipientKeyObj = new JSONObject();
            recipientKeyObj.put("pubkey", toAddress);
            recipientKeyObj.put("isSigner", false);
            recipientKeyObj.put("isWritable", true);
            keysArray.put(recipientKeyObj);

            // Build the instruction
            JSONArray instructionsArray = new JSONArray();
            JSONObject instructionObj = new JSONObject();
            instructionObj.put("programId", "11111111111111111111111111111111");
            instructionObj.put("keys", keysArray);
            instructionObj.put("data", instructionDataBase64);
            instructionsArray.put(instructionObj);

            // Build the full transaction data
            txData.put("feePayer", fromAddress);
            txData.put("recipient", toAddress);
            txData.put("instructions", instructionsArray);

            // Wrap in the outer JSON object
            jsonObj.put("tx_data", txData);

            return jsonObj.toString();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Signs a Solana transfer transaction.
     *
     * @param fromAddress The sender's address
     * @param toAddress The recipient's address
     * @param lamports Amount to transfer in lamports
     * @param blockhash Recent blockhash from the Solana network
     * @param privateKeyHex Private key in hex format
     * @return Base58-encoded signed transaction, or null on error
     */
    public String signSolanaTransaction(
            String fromAddress,
            String toAddress,
            long lamports,
            String blockhash,
            String privateKeyHex
    ) {
        try {
            // 1) Construct the Transfer proto
            Solana.Transfer transfer = Solana.Transfer.newBuilder()
                    .setRecipient(toAddress)
                    .setValue(lamports)
                    .build();

            // 2) Convert private key hex -> byte[]
            byte[] privateKeyBytes = hexStringToByteArray(privateKeyHex);

            // 3) Build the SigningInput
            Solana.SigningInput input = Solana.SigningInput.newBuilder()
                    .setTransferTransaction(transfer)
                    .setRecentBlockhash(blockhash)
                    .setSender(fromAddress)
                    .setPrivateKey(ByteString.copyFrom(privateKeyBytes))
                    .build();

            // 4) Sign with AnySigner
            byte[] resultData = AnySigner.nativeSign(input.toByteArray(), CoinType.SOLANA.value());

            // 5) Parse the SigningOutput
            Solana.SigningOutput output = Solana.SigningOutput.parseFrom(resultData);

            // 6) Return the final encoded transaction string
            return output.getEncoded();

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Signs a Solana token transfer transaction.
     *
     * @param fromAddress Fee payer / owner address
     * @param toAddress Recipient's main address
     * @param senderTokenAddress Sender's token account address
     * @param recipientTokenAddress Recipient's token account address
     * @param amount Amount in token's smallest units
     * @param tokenMint Token mint address
     * @param decimals Token decimals
     * @param blockhash Recent blockhash
     * @param privateKeyHex Sender's private key
     * @param shouldCreateAssociatedTokenAccount Whether to create recipient's token account if needed
     * @return Encoded signed transaction, or null on error
     */
    public String signSolanaTokenTransaction(
            String fromAddress,
            String toAddress,
            String senderTokenAddress,
            String recipientTokenAddress,
            long amount,
            String tokenMint,
            int decimals,
            String blockhash,
            String privateKeyHex,
            boolean shouldCreateAssociatedTokenAccount
    ) {
        try {
            // 1) Convert private key hex -> byte[]
            byte[] privateKeyBytes = hexStringToByteArray(privateKeyHex);
            if (privateKeyBytes == null) {
                System.err.println("Invalid privateKey hex: " + privateKeyHex);
                return null;
            }

            // 2) Start Building the SigningInput
            Solana.SigningInput.Builder inputBuilder = Solana.SigningInput.newBuilder()
                    .setRecentBlockhash(blockhash)
                    .setSender(fromAddress)
                    .setPrivateKey(ByteString.copyFrom(privateKeyBytes));

            // 3) Conditionally build the correct transaction type
            if (shouldCreateAssociatedTokenAccount) {
                // Build CreateAndTransferToken message
                Solana.CreateAndTransferToken.Builder createTransferBuilder = Solana.CreateAndTransferToken.newBuilder()
                        .setRecipientMainAddress(toAddress)
                        .setTokenMintAddress(tokenMint)
                        .setRecipientTokenAddress(recipientTokenAddress)
                        .setSenderTokenAddress(senderTokenAddress)
                        .setAmount(amount)
                        .setDecimals(decimals);

                inputBuilder.setCreateAndTransferTokenTransaction(createTransferBuilder.build());
            } else {
                // Build TokenTransfer message
                Solana.TokenTransfer.Builder tokenTransferBuilder = Solana.TokenTransfer.newBuilder()
                        .setTokenMintAddress(tokenMint)
                        .setSenderTokenAddress(senderTokenAddress)
                        .setRecipientTokenAddress(recipientTokenAddress)
                        .setAmount(amount)
                        .setDecimals(decimals);

                inputBuilder.setTokenTransferTransaction(tokenTransferBuilder.build());
            }

            Solana.SigningInput input = inputBuilder.build();

            // 4) Sign with AnySigner
            byte[] resultData = AnySigner.nativeSign(input.toByteArray(), CoinType.SOLANA.value());
            if (resultData == null || resultData.length == 0) {
                System.err.println("Token signing failed, result is null or empty.");
                return null;
            }

            // 5) Parse the SigningOutput
            Solana.SigningOutput output = Solana.SigningOutput.parseFrom(resultData);

            // 6) Return the final encoded transaction string
            return output.getEncoded();

        } catch (Exception e) {
            System.err.println("Error during Solana token transaction signing: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    // Helper method to convert hex string to byte array
    private byte[] hexStringToByteArray(String s) {
        if (s == null) return null;

        String clean = s.replace("0x", "").replaceAll("[^0-9A-Fa-f]", "");
        int len = clean.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(clean.charAt(i), 16) << 4)
                    + Character.digit(clean.charAt(i + 1), 16));
        }
        return data;
    }
}
