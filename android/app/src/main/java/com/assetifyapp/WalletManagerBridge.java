package com.assetifyapp;

import android.util.Base64;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import javax.crypto.spec.PBEKeySpec;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.SecretKeySpec;

import wallet.core.jni.CoinType;
import wallet.core.jni.HDVersion;
import wallet.core.jni.HDWallet;
import wallet.core.jni.PrivateKey;
import wallet.core.jni.PublicKey;
import wallet.core.jni.AnyAddress;
import wallet.core.jni.Purpose;

public class WalletManagerBridge extends ReactContextBaseJavaModule {

    public WalletManagerBridge(ReactApplicationContext reactContext) {
        super(reactContext);
    }

    @Override
    public String getName() {
        return "WalletManagerBridge";
    }

    @ReactMethod
    public void createHDWalletFromMnemonic(String mnemonic, String blockchain, Promise promise) {
        try {
            HDWallet hdWallet = new HDWallet(mnemonic, "");

            byte[] seed = hdWallet.seed();
            if (seed == null) {
                promise.resolve(Arguments.createMap());
                return;
            }

            List<Map<String, String>> xPubsList = new ArrayList<>();
            PrivateKey privateKey = null;
            PublicKey publicKey = null;
            String address = null;

            CoinType coin = switch (blockchain) {
                case "ethereum" -> CoinType.ETHEREUM;
                case "binance-smart-chain" -> CoinType.ETHEREUM;
                case "trx" -> CoinType.TRON;
                case "xrp" -> CoinType.XRP;
                case "litecoin" -> CoinType.LITECOIN;
                case "dogecoin" -> CoinType.DOGECOIN;
                case "bitcoin-cash" -> CoinType.BITCOINCASH;
                case "avalanche" -> CoinType.AVALANCHECCHAIN;
                case "solana" -> CoinType.SOLANA;
                default -> CoinType.BITCOIN;
            };

            if (coin == CoinType.BITCOIN || coin == CoinType.LITECOIN) {
                // BIP84
                String xPriv = hdWallet.getExtendedPrivateKey(Purpose.BIP84, coin, HDVersion.ZPRV);
                String xPub = hdWallet.getExtendedPublicKey(Purpose.BIP84, coin, HDVersion.ZPUB);

                if (xPriv == null || xPub == null) {
                    promise.resolve(Arguments.createMap());
                    return;
                }

                xPubsList.add(Map.of("accountXpub", xPub, "accountXpriv", xPriv));

                privateKey = hdWallet.getKeyForCoin(coin);
                publicKey = privateKey.getPublicKey(coin);
                address = new AnyAddress(publicKey, coin).description();

            } else {
                // BIP44
                String xPriv = hdWallet.getExtendedPrivateKey(Purpose.BIP44, coin, HDVersion.XPRV);
                String xPub = hdWallet.getExtendedPublicKey(Purpose.BIP44, coin, HDVersion.XPUB);

                if (xPriv == null || xPub == null) {
                    promise.resolve(Arguments.createMap());
                    return;
                }

                xPubsList.add(Map.of("accountXpub", xPub, "accountXpriv", xPriv));

                privateKey = hdWallet.getKeyForCoin(coin);
                publicKey = privateKey.getPublicKey(coin);
                address = new AnyAddress(publicKey, coin).description();
            }

            WalletDTO walletDTO = new WalletDTO(
                    blockchain, "mainnet", mnemonic, bytesToHex(seed),
                    xPubsList, address, bytesToHex(privateKey.data()),
                    publicKey.description()
            );

            promise.resolve(convertWalletDTOToWritableMap(walletDTO));

        } catch (Exception e) {
            promise.reject("ERROR", e.getMessage());
        }
    }

    @ReactMethod
    public void signSolanaTransaction(
            String fromAddress,
            String toAddress,
            double lamportsDouble,
            String blockhash,
            String privateKeyHex,
            Promise promise
    ) {
        try {
            System.loadLibrary("TrustWalletCore");
            TransactionManager transactionManager = new TransactionManager();
            long lamports = (long) lamportsDouble; // Cast double to long

            String signedTx = transactionManager.signSolanaTransaction(
                    fromAddress,
                    toAddress,
                    lamports,
                    blockhash,
                    privateKeyHex
            );

            if (signedTx != null) {
                promise.resolve(signedTx);
            } else {
                promise.reject("SIGN_TX_ERROR", "Unable to sign transaction");
            }
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject("SIGN_TX_ERROR", e.getMessage(), e);
        }
    }


    @ReactMethod
    public void signSolanaTokenTransaction(
            String fromAddress,
            String toAddress,
            String senderTokenAddress,
            String recipientTokenAddress,
            double amountDouble,
            String tokenMint,
            double decimalsDouble,
            String blockhash,
            String privateKeyHex,
            boolean shouldCreateAssociatedTokenAccount,
            Promise promise
    ) {
        try {
            System.loadLibrary("TrustWalletCore"); // Ensure loaded
            TransactionManager transactionManager = new TransactionManager();

            long amount = (long) amountDouble;       // Cast double to long
            int decimals = (int) decimalsDouble;     // Cast double to int

            String signedTx = transactionManager.signSolanaTokenTransaction(
                    fromAddress,
                    toAddress,
                    senderTokenAddress,
                    recipientTokenAddress,
                    amount,
                    tokenMint,
                    decimals,
                    blockhash,
                    privateKeyHex,
                    shouldCreateAssociatedTokenAccount
            );

            if (signedTx != null) {
                promise.resolve(signedTx);
            } else {
                // Use a consistent error code if possible
                promise.reject("SIGN_TOKEN_TX_ERROR", "Unable to sign token transaction");
            }
        } catch (Exception e) {
            e.printStackTrace(); // Log stack trace for debugging
            promise.reject("SIGN_TOKEN_TX_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void buildUnsignedSolanaTransaction(
            String fromAddress,
            String toAddress,
            double lamportsDouble,
            Promise promise
    ) {
        try {
            System.loadLibrary("TrustWalletCore");
            TransactionManager transactionManager = new TransactionManager();
            long lamports = (long) lamportsDouble; // Cast double to long

            String unsignedTx = transactionManager.buildUnsignedSolanaTransaction(
                    fromAddress,
                    toAddress,
                    lamports
            );

            if (unsignedTx != null) {
                promise.resolve(unsignedTx);
            } else {
                promise.reject("BUILD_UNSIGNED_ERROR", "Unable to build unsigned transaction");
            }
        } catch (Exception e) {
            e.printStackTrace();
            promise.reject("BUILD_UNSIGNED_ERROR", e.getMessage(), e);
        }
    }

    public void pbkdf2(String password, String salt, int iterations, int dkLen, Promise promise) {
        try {
            byte[] saltBytes = salt.getBytes();
            PBEKeySpec spec = new PBEKeySpec(password.toCharArray(), saltBytes, iterations, dkLen * 8);
            SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            byte[] derivedKey = factory.generateSecret(spec).getEncoded();
            
            String hexDerivedKey = bytesToHex(derivedKey);
            promise.resolve(hexDerivedKey);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            promise.reject("PBKDF2_ERROR", "Error deriving key: " + e.getMessage());
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    private WritableMap convertMapToWritableMap(Map<String, String> map) {
        WritableMap writableMap = Arguments.createMap();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            writableMap.putString(entry.getKey(), entry.getValue());
        }
        return writableMap;
    }

    private WritableMap convertWalletDTOToWritableMap(WalletDTO walletDTO) {
        WritableMap writableMap = Arguments.createMap();
        writableMap.putString("blockchain", walletDTO.blockchain);
        writableMap.putString("network", walletDTO.network);
        writableMap.putString("mnemonic", walletDTO.mnemonic);
        writableMap.putString("seed", walletDTO.seed);
        writableMap.putArray("xPubsList", convertListToWritableArray(walletDTO.xPubsList));
        writableMap.putString("address", walletDTO.address);
        writableMap.putString("privateKey", walletDTO.privateKey);
        writableMap.putString("publicKey", walletDTO.publicKey);
        return writableMap;
    }

    private WritableArray convertListToWritableArray(List<Map<String, String>> list) {
        WritableArray writableArray = Arguments.createArray();
        for (Map<String, String> map : list) {
            writableArray.pushMap(convertMapToWritableMap(map));
        }
        return writableArray;
    }
}
