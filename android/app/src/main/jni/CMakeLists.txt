cmake_minimum_required(VERSION 3.13)

project(appmodules)

include(${REACT_ANDROID_DIR}/cmake-utils/ReactNative-application.cmake)

# Target source (relative to this CMake file's directory)
target_sources(${CMAKE_PROJECT_NAME} PRIVATE
  ${CMAKE_CURRENT_LIST_DIR}/../../../../../shared/src/NativeSampleModule.cpp
)

# Target include directories (relative to this CMake file's directory)
target_include_directories(${CMAKE_PROJECT_NAME} PUBLIC
  ${CMAKE_CURRENT_LIST_DIR}/../../../../../shared/include
)

if("${ANDROID_ABI}" STREQUAL "armeabi-v7a")
  set(MYLIB_ABI_DIR "armeabi-v7a")
elseif("${ANDROID_ABI}" STREQUAL "arm64-v8a")
  set(MYLIB_ABI_DIR "arm64-v8a")
elseif("${ANDROID_ABI}" STREQUAL "x86_64")
  set(MYLIB_ABI_DIR "x86_64")
elseif("${ANDROID_ABI}" STREQUAL "x86")
  set(MYLIB_ABI_DIR "x86")
endif()

# Define the imported static library using a relative path pointing to
# android/app/libs/mpc/<abi>/libmpc_core_bindings.a
#
# Explanation:
#   - CMAKE_CURRENT_LIST_DIR = android/app/src/main/jni
#   - We go up 3 directories (to android/app):
#       ../../../
#   - Then descend into libs/mpc/<MYLIB_ABI_DIR>/libmpc_core_bindings.a
add_library(mpc_core_bindings STATIC IMPORTED)
set_target_properties(mpc_core_bindings PROPERTIES
  IMPORTED_LOCATION
    "${CMAKE_CURRENT_LIST_DIR}/../../../libs/mpc/${MYLIB_ABI_DIR}/libmpc_core_bindings.a"
)

target_link_libraries(${CMAKE_PROJECT_NAME} mpc_core_bindings)
